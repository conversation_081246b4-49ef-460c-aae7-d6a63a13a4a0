<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>推广用户信息API测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #409eff;
            border-bottom: 2px solid #409eff;
            padding-bottom: 10px;
            margin-top: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
        input, textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        input:focus, textarea:focus {
            outline: none;
            border-color: #409eff;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }
        button {
            background: #409eff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #66b1ff;
        }
        button:active {
            background: #3a8ee6;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            background: #fef0f0;
            border: 1px solid #fbc4c4;
            color: #f56c6c;
        }
        .success {
            background: #f0f9ff;
            border: 1px solid #b3d8ff;
            color: #409eff;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .api-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #409eff;
        }
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background: #67c23a; }
        .status-error { background: #f56c6c; }
        .status-warning { background: #e6a23c; }
    </style>
</head>
<body>
    <div class="container">
        <h1>推广用户信息API测试页面</h1>
        
        <div class="api-info">
            <h3>API 基本信息</h3>
            <p><strong>接口地址:</strong> GET /admin/marketing/point_record/promotion_user/{userId}</p>
            <p><strong>功能:</strong> 获取推广用户的详细信息</p>
            <p><strong>权限:</strong> marketing-point_record-index</p>
        </div>

        <h2>测试获取推广用户信息</h2>
        <div class="form-group">
            <label for="userId">被推广用户ID:</label>
            <input type="number" id="userId" placeholder="请输入用户ID，例如: 123" value="">
        </div>
        <button onclick="testGetPromotionUserInfo()">获取用户信息</button>
        <button onclick="testWithSampleData()">使用示例数据</button>
        <div id="result1" class="result" style="display: none;"></div>

        <h2>测试积分记录列表（验证relation字段格式）</h2>
        <div class="form-group">
            <label for="testPage">页码:</label>
            <input type="number" id="testPage" value="1" min="1">
        </div>
        <div class="form-group">
            <label for="testLimit">每页数量:</label>
            <input type="number" id="testLimit" value="10" min="1" max="50">
        </div>
        <div class="form-group">
            <label for="testTradingType">交易类型:</label>
            <select id="testTradingType" style="padding: 12px; width: 100%; border: 1px solid #dcdfe6; border-radius: 4px;">
                <option value="">全部</option>
                <option value="earn">获得积分</option>
                <option value="use">消费积分</option>
                <option value="refund">退回积分</option>
            </select>
        </div>
        <button onclick="testPointRecordList()">获取积分记录列表</button>
        <div id="result2" class="result" style="display: none;"></div>

        <h2>API状态检查</h2>
        <button onclick="checkApiStatus()">检查API状态</button>
        <div id="result3" class="result" style="display: none;"></div>

        <h2>功能说明</h2>
        <div class="info-grid">
            <div class="api-info">
                <h4>推广用户信息内容</h4>
                <ul>
                    <li>基本信息: 昵称、头像、用户ID</li>
                    <li>联系信息: 手机号码</li>
                    <li>积分信息: 当前积分余额</li>
                    <li>时间信息: 注册时间、最后登录时间</li>
                    <li>推广信息: 推广者、推广时间</li>
                </ul>
            </div>
            <div class="api-info">
                <h4>relation字段格式</h4>
                <ul>
                    <li>订单记录: "订单:12345"</li>
                    <li>推广记录: "推广用户:123"</li>
                    <li>无关联: "-"</li>
                </ul>
            </div>
            <div class="api-info">
                <h4>前端交互逻辑</h4>
                <ul>
                    <li>自动识别记录类型</li>
                    <li>推广记录显示"查看被推广用户"</li>
                    <li>订单记录显示订单号</li>
                    <li>点击时弹出对应信息窗口</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // API基础URL
        const API_BASE_URL = 'http://localhost:8080/adminapi/marketing';

        // 显示结果
        function displayResult(elementId, data) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'result success';
            element.textContent = JSON.stringify(data, null, 2);
        }

        // 显示错误
        function displayError(elementId, error) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'result error';
            element.textContent = `错误: ${error.message || error}`;
        }

        // 测试获取推广用户信息
        async function testGetPromotionUserInfo() {
            try {
                const userId = document.getElementById('userId').value;

                if (!userId) {
                    throw new Error('请输入用户ID');
                }

                const url = `${API_BASE_URL}/point_record/promotion_user/${userId}`;
                console.log('请求URL:', url);

                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        // 'Authorization': 'Bearer your-token-here' // 如果需要认证
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                displayResult('result1', data);
            } catch (error) {
                console.error('获取推广用户信息失败:', error);
                displayError('result1', error);
            }
        }

        // 使用示例数据
        function testWithSampleData() {
            const sampleData = {
                功能说明: {
                    接口地址: '/admin/marketing/point_record/promotion_user/{userId}',
                    请求方法: 'GET',
                    功能描述: '获取推广用户的详细信息',
                    权限要求: 'marketing-point_record-index'
                },
                请求示例: {
                    URL: '/admin/marketing/point_record/promotion_user/123',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                },
                响应示例: {
                    code: 200,
                    message: 'success',
                    data: {
                        id: 123,
                        nickname: '张三',
                        avatar: 'https://example.com/avatar.jpg',
                        mobile: '13800138000',
                        registerTime: '2024-01-01 10:00:00',
                        lastLoginTime: '2024-01-15 15:30:00',
                        points: 1500,
                        promotionTime: '2024-01-01 10:05:00',
                        promoterId: 456,
                        promoterNickname: '推广者昵称'
                    }
                },
                错误响应示例: {
                    code: 400,
                    message: '用户不存在',
                    data: null
                }
            };

            displayResult('result1', sampleData);
        }

        // 测试积分记录列表
        async function testPointRecordList() {
            try {
                const page = document.getElementById('testPage').value || '1';
                const limit = document.getElementById('testLimit').value || '10';
                const trading_type = document.getElementById('testTradingType').value;

                const params = new URLSearchParams();
                params.append('page', page);
                params.append('limit', limit);
                if (trading_type) params.append('trading_type', trading_type);

                const url = `${API_BASE_URL}/point_record?${params.toString()}`;
                console.log('请求URL:', url);

                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                // 重点显示relation字段的格式
                if (data.data && data.data.list) {
                    const relationFormats = data.data.list.map(item => ({
                        id: item.id,
                        relation: item.relation,
                        type_name: item.type_name,
                        是否推广记录: item.relation && item.relation.includes('推广用户:') ? '是' : '否'
                    }));
                    
                    const result = {
                        总记录数: data.data.count,
                        relation字段格式分析: relationFormats,
                        完整响应: data
                    };
                    
                    displayResult('result2', result);
                } else {
                    displayResult('result2', data);
                }
            } catch (error) {
                console.error('获取积分记录列表失败:', error);
                displayError('result2', error);
            }
        }

        // 检查API状态
        async function checkApiStatus() {
            try {
                const url = `${API_BASE_URL}/point_record?page=1&limit=1`;
                console.log('检查API状态:', url);

                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const status = {
                    httpStatus: response.status,
                    statusText: response.statusText,
                    contentType: response.headers.get('content-type'),
                    timestamp: new Date().toISOString()
                };

                if (response.ok) {
                    const data = await response.json();
                    status.responseData = data;
                    status.message = 'API 正常工作';
                    status.推广功能状态 = '已部署新的推广用户信息查看功能';
                } else {
                    status.error = await response.text();
                    status.message = 'API 返回错误';
                }

                displayResult('result3', status);
            } catch (error) {
                console.error('检查API状态失败:', error);
                displayError('result3', error);
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('推广用户信息API测试页面已加载');
            
            // 添加一些示例用户ID
            const userIdInput = document.getElementById('userId');
            userIdInput.placeholder = '请输入用户ID，例如: 1, 2, 3';
        });
    </script>
</body>
</html>