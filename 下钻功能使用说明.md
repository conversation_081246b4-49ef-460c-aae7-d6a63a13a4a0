# 用户管理下钻功能使用说明

## 功能概述

下钻功能允许管理员在用户管理页面中点击用户的推广数据（今日直邀、本月直邀、总直邀），查看该用户的下级成员列表。

## 主要特性

### 1. 下钻导航
- **导航栏显示**: 在下钻模式下，导航栏显示当前查看的用户名和"的下级"字样
- **返回功能**: 支持返回上一级或直接返回首页
- **历史记录**: 使用栈结构保存下钻历史，支持多级返回

### 2. 数据展示优化
- **统计信息隐藏**: 下钻模式下隐藏总用户统计信息栏
- **筛选标签隐藏**: 下钻模式下隐藏类型筛选标签
- **功能禁用**: 下钻模式下禁用进一步下钻功能

### 3. 接口调用优化
- **POST请求**: 使用正确的POST方法调用GetPromotionUserDetail接口
- **参数格式**: 正确的请求体参数格式 `{ userId: targetUserId }`
- **数据处理**: 正确处理下钻模式返回的数据结构（invitesList字段）

## 使用流程

### 普通模式 → 下钻模式
1. 在用户管理页面找到目标用户
2. 点击用户的"今日直邀"、"本月直邀"或"总直邀"数据项
3. 或点击"邀请明细"按钮
4. 页面将跳转到下钻模式，显示该用户的下级成员

### 下钻模式操作
- **返回上级**: 点击导航栏左侧的"返回上级"按钮
- **返回首页**: 点击导航栏右侧的"首页"按钮，退出下钻模式
- **查看详情**: 仍可查看用户详情和订单信息

## 技术实现

### 核心文件修改

#### 1. users.js
- 修复了POST请求参数格式
- 正确处理下钻模式的数据结构（invitesList）
- 实现了下钻历史栈管理

#### 2. users.wxml  
- 下钻模式下隐藏统计信息和筛选标签
- 下钻模式下禁用点击功能
- 导航栏动态显示

#### 3. users.wxss
- 添加禁用状态的样式效果
- 优化视觉反馈

### API调用

**下钻模式接口调用**:
```javascript
util.request(api.GetPromotionUserDetail, {
  userId: drillDownUserId
}, 'POST')
```

**返回数据结构**:
```javascript
{
  success: true,
  data: {
    invitesList: [...]  // 下级用户列表
    // 其他用户详情信息
  }
}
```

## 测试验证

已通过代码检查验证以下功能：
- ✅ POST请求方法设置正确
- ✅ 下钻模式数据处理逻辑正确  
- ✅ 下钻模式下UI元素正确隐藏/显示
- ✅ 点击功能正确禁用
- ✅ 样式效果正常应用

## 注意事项

1. **权限控制**: 下钻功能仅对管理员用户开放
2. **数据层级**: 目前支持单级下钻，避免无限层级
3. **性能考虑**: 下钻模式下禁用分页加载，一次性加载所有下级用户
4. **用户体验**: 提供清晰的导航提示和状态反馈

## 后续优化建议

1. 添加加载状态指示器
2. 支持多级下钻历史导航
3. 添加下级用户统计信息
4. 优化大数据量下的性能表现