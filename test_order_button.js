// 测试"查看订单"按钮功能
console.log('=== 测试"查看订单"按钮功能 ===');

// 模拟点击事件对象
const mockEvent = {
  currentTarget: {
    dataset: {
      id: '12345'
    }
  }
};

// 模拟 viewUserOrders 方法
function viewUserOrders(e) {
  console.log('viewUserOrders 被调用，事件对象:', e);
  const userId = e.currentTarget.dataset.id;
  console.log('获取到的用户ID:', userId);
  
  // 模拟导航到订单页面
  const url = `/pages/ucenter/order/order?userId=${userId}`;
  console.log('导航URL:', url);
  
  return {
    success: true,
    userId: userId,
    url: url
  };
}

// 运行测试
const result = viewUserOrders(mockEvent);
console.log('测试结果:', result);

if (result.success && result.userId === '12345') {
  console.log('✅ "查看订单"按钮功能测试通过');
} else {
  console.log('❌ "查看订单"按钮功能测试失败');
}

console.log('\n=== 测试完成 ===');