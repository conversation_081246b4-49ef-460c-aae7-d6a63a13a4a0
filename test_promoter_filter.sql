-- 推广用户筛选功能测试SQL脚本

-- 1. 检查当前推广关系数据
SELECT 
    '当前推广关系数据' as description,
    COUNT(*) as total_promotion_relations
FROM user 
WHERE promoter_id IS NOT NULL;

-- 2. 查看推广者统计
SELECT 
    '推广者统计' as description,
    COUNT(DISTINCT promoter_id) as unique_promoters
FROM user 
WHERE promoter_id IS NOT NULL;

-- 3. 查看详细推广关系
SELECT 
    u1.id as user_id,
    u1.nickname as user_name,
    u1.promoter_id,
    u2.nickname as promoter_name,
    (SELECT COUNT(*) FROM user WHERE promoter_id = u1.id) as promoted_count
FROM user u1
LEFT JOIN user u2 ON u1.promoter_id = u2.id
WHERE u1.promoter_id IS NOT NULL 
   OR (SELECT COUNT(*) FROM user WHERE promoter_id = u1.id) > 0
ORDER BY u1.id;

-- 4. 验证推广用户筛选查询
SELECT 
    id,
    nickname,
    mobile,
    register_time,
    (SELECT COUNT(*) FROM user u2 WHERE u2.promoter_id = user.id) as promotion_count
FROM user
WHERE EXISTS (SELECT 1 FROM user u2 WHERE u2.promoter_id = user.id)
ORDER BY register_time DESC;

-- 5. 如果没有推广关系数据，创建测试数据
-- 注意：请根据实际用户ID调整以下数据

-- 假设用户ID 1 推广了用户ID 2 和 3
-- UPDATE user SET promoter_id = 1 WHERE id IN (2, 3);

-- 假设用户ID 4 推广了用户ID 5
-- UPDATE user SET promoter_id = 4 WHERE id = 5;

-- 6. 验证统计查询性能
EXPLAIN SELECT COUNT(*) 
FROM user 
WHERE id IN (SELECT DISTINCT promoter_id FROM user WHERE promoter_id IS NOT NULL);

-- 7. 建议的索引（如果不存在）
-- CREATE INDEX idx_user_promoter_id ON user(promoter_id);

-- 8. 检查用户等级数据（用于其他筛选功能）
SELECT 
    user_level_id,
    COUNT(*) as user_count
FROM user 
GROUP BY user_level_id
ORDER BY user_level_id;

-- 9. 检查最近登录数据（用于活跃用户筛选）
SELECT 
    '最近3天登录用户' as description,
    COUNT(*) as active_users
FROM user 
WHERE last_login_time >= DATE_SUB(NOW(), INTERVAL 3 DAY);

-- 10. 检查新用户数据（用于新用户筛选）
SELECT 
    '最近7天注册用户' as description,
    COUNT(*) as new_users
FROM user 
WHERE register_time >= DATE_SUB(NOW(), INTERVAL 7 DAY);