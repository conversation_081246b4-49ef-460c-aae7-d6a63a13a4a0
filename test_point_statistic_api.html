<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>积分统计API测试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        .result {
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            border-left: 4px solid #4CAF50;
            background-color: #f1f8e9;
        }
        .error {
            border-left: 4px solid #f44336;
            background-color: #ffebee;
            color: #c62828;
        }
        .api-section {
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .inline-form {
            display: flex;
            gap: 10px;
            align-items: flex-end;
            flex-wrap: wrap;
        }
        .inline-form .form-group {
            flex: 1;
            min-width: 200px;
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <h1>🔢 积分统计API测试工具</h1>
    
    <div class="container">
        <h2>配置</h2>
        <div class="inline-form">
            <div class="form-group">
                <label for="baseUrl">API基础URL:</label>
                <input type="text" id="baseUrl" value="http://localhost:8080/adminapi/marketing" placeholder="API基础地址">
            </div>
            <div class="form-group">
                <label for="timeRange">时间范围:</label>
                <input type="text" id="timeRange" value="2024/01/01-2024/12/31" placeholder="yyyy/MM/dd-yyyy/MM/dd">
            </div>
        </div>
    </div>

    <div class="container">
        <div class="api-section">
            <h2>1. 积分基础统计 (get_basic)</h2>
            <p><strong>功能:</strong> 获取当前积分、累计总积分、累计消耗积分</p>
            <button onclick="testGetBasic()">测试基础统计API</button>
            <div id="result1" class="result"></div>
        </div>

        <div class="api-section">
            <h2>2. 积分趋势统计 (get_trend)</h2>
            <p><strong>功能:</strong> 获取积分获得和使用的趋势数据，用于折线图展示</p>
            <button onclick="testGetTrend()">测试趋势统计API</button>
            <div id="result2" class="result"></div>
        </div>

        <div class="api-section">
            <h2>3. 积分来源分析 (get_channel)</h2>
            <p><strong>功能:</strong> 分析积分获得的来源分布，用于饼图和表格展示</p>
            <button onclick="testGetChannel()">测试来源分析API</button>
            <div id="result3" class="result"></div>
        </div>

        <div class="api-section">
            <h2>4. 积分消耗分析 (get_type)</h2>
            <p><strong>功能:</strong> 分析积分使用的类型分布，用于饼图和表格展示</p>
            <button onclick="testGetType()">测试消耗分析API</button>
            <div id="result4" class="result"></div>
        </div>

        <div class="api-section">
            <h2>5. 批量测试</h2>
            <button onclick="testAllApis()">🚀 测试所有API</button>
            <button onclick="clearAllResults()">🧹 清空结果</button>
            <div id="result5" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = () => document.getElementById('baseUrl').value;
        const TIME_RANGE = () => document.getElementById('timeRange').value;

        function displayResult(elementId, data, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.className = isSuccess ? 'result success' : 'result error';
            element.textContent = typeof data === 'string' ? data : JSON.stringify(data, null, 2);
        }

        function displayError(elementId, error) {
            const element = document.getElementById(elementId);
            element.className = 'result error';
            element.textContent = `错误: ${error.message || error}`;
        }

        async function testGetBasic() {
            try {
                const time = TIME_RANGE();
                const url = `${API_BASE_URL()}/point/get_basic${time ? `?time=${encodeURIComponent(time)}` : ''}`;
                console.log('请求URL:', url);

                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        // 'Authorization': 'Bearer your-token-here' // 如果需要认证
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                displayResult('result1', {
                    请求URL: url,
                    响应数据: data,
                    数据说明: {
                        now_point: '当前积分总数',
                        all_point: '累计总积分（所有获得的积分）',
                        pay_point: '累计消耗积分（所有使用的积分）'
                    }
                });
            } catch (error) {
                console.error('测试基础统计API失败:', error);
                displayError('result1', error);
            }
        }

        async function testGetTrend() {
            try {
                const time = TIME_RANGE();
                const url = `${API_BASE_URL()}/point/get_trend${time ? `?time=${encodeURIComponent(time)}` : ''}`;
                console.log('请求URL:', url);

                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                displayResult('result2', {
                    请求URL: url,
                    响应数据: data,
                    数据说明: {
                        xAxis: '时间轴数据（日期）',
                        series: '系列数据，包含"积分获得"和"积分使用"两条线'
                    }
                });
            } catch (error) {
                console.error('测试趋势统计API失败:', error);
                displayError('result2', error);
            }
        }

        async function testGetChannel() {
            try {
                const time = TIME_RANGE();
                const url = `${API_BASE_URL()}/point/get_channel${time ? `?time=${encodeURIComponent(time)}` : ''}`;
                console.log('请求URL:', url);

                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                displayResult('result3', {
                    请求URL: url,
                    响应数据: data,
                    数据说明: {
                        list: '表格展示数据，包含名称、数值、占比',
                        data: '图表展示数据，用于饼图',
                        来源类型: {
                            'order': '订单消费',
                            'promotion': '推广奖励', 
                            'register': '注册奖励',
                            'sign': '签到奖励',
                            'manual': '手动调整',
                            'refund': '退款返还'
                        }
                    }
                });
            } catch (error) {
                console.error('测试来源分析API失败:', error);
                displayError('result3', error);
            }
        }

        async function testGetType() {
            try {
                const time = TIME_RANGE();
                const url = `${API_BASE_URL()}/point/get_type${time ? `?time=${encodeURIComponent(time)}` : ''}`;
                console.log('请求URL:', url);

                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                displayResult('result4', {
                    请求URL: url,
                    响应数据: data,
                    数据说明: {
                        list: '表格展示数据，包含名称、数值、占比',
                        data: '图表展示数据，用于饼图',
                        消耗类型: '显示积分使用的不同渠道分布'
                    }
                });
            } catch (error) {
                console.error('测试消耗分析API失败:', error);
                displayError('result4', error);
            }
        }

        async function testAllApis() {
            displayResult('result5', '🚀 开始批量测试所有API接口...\n');
            
            const apis = [
                { name: '基础统计', test: testGetBasic },
                { name: '趋势统计', test: testGetTrend },
                { name: '来源分析', test: testGetChannel },
                { name: '消耗分析', test: testGetType }
            ];

            let results = [];
            for (const api of apis) {
                try {
                    console.log(`测试 ${api.name} API...`);
                    await api.test();
                    results.push(`✅ ${api.name}: 测试成功`);
                } catch (error) {
                    results.push(`❌ ${api.name}: 测试失败 - ${error.message}`);
                }
                
                // 添加延迟避免请求过快
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            const summaryResult = `
批量测试完成！

测试结果摘要:
${results.join('\n')}

测试时间: ${new Date().toLocaleString()}
API基础URL: ${API_BASE_URL()}
时间范围: ${TIME_RANGE()}

请查看上方各个API的详细测试结果。
            `;

            displayResult('result5', summaryResult);
        }

        function clearAllResults() {
            ['result1', 'result2', 'result3', 'result4', 'result5'].forEach(id => {
                const element = document.getElementById(id);
                element.className = 'result';
                element.textContent = '';
            });
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('积分统计API测试工具已加载');
            displayResult('result5', `
🎯 积分统计API测试工具使用说明:

1. 确保后端服务已启动（默认端口8080）
2. 根据需要修改API基础URL和时间范围
3. 点击各个API按钮进行单独测试
4. 或者点击"测试所有API"进行批量测试
5. 查看控制台获取更详细的调试信息

📝 注意事项:
- 需要有积分记录数据才能看到统计效果
- 如果接口需要认证，请取消注释Authorization头部
- 时间格式为: yyyy/MM/dd-yyyy/MM/dd

准备就绪，开始测试吧！ 🚀
            `);
        });
    </script>
</body>
</html>