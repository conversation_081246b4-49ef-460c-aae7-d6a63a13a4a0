# 团队管理页面接口数据完善总结

## 问题描述
团队管理页面（team.wxml）展示的用户列表数据字段不完整，接口返回的数据缺少页面所需的多个字段，导致页面显示不完整。

## 页面需要的字段分析

根据team.wxml模板分析，页面需要展示以下字段：

### 基本信息字段
- `id`: 用户ID
- `nickname`: 用户昵称
- `username`: 用户名
- `avatar`: 头像
- `mobile`: 手机号
- `userLevelId`: 用户等级ID
- `userLevelText`: 用户等级文本
- `registerTime`: 注册时间
- `lastLoginTime`: 最后登录时间
- `isOnline`: 在线状态

### 消费数据字段
- `orderCount`: 订单数量
- `totalAmount`: 消费总额
- `points`: 积分
- `balance`: 余额

### 推广数据字段
- `promotionCount`: 总直邀用户数
- `todayInviteCount`: 今日直邀数量
- `monthInviteCount`: 本月直邀数量
- `pendingEarnings`: 待确认收益
- `withdrawableAmount`: 可提现金额

## 修改内容

### 1. 完善getPromotionUserInvites方法

**文件**: `server/src/main/java/com/logic/code/service/UserService.java`

**修改内容**:
- 添加了用户基本信息字段（username, mobile, userLevelId等）
- 添加了订单统计信息（orderCount, totalAmount）
- 添加了推广统计信息（promotionCount, todayInviteCount, monthInviteCount）
- 添加了推广收益信息（pendingEarnings, withdrawableAmount）
- 添加了用户积分和余额信息

**修改前**:
```java
Map<String, Object> inviteInfo = new HashMap<>();
inviteInfo.put("id", invite.getId());
inviteInfo.put("nickname", invite.getNickname());
inviteInfo.put("avatar", invite.getAvatar());
inviteInfo.put("inviteTime", invite.getPromotionTime());

// 获取该用户的订单数量
QueryWrapper<Order> orderWrapper = new QueryWrapper<>();
orderWrapper.eq("user_id", invite.getId());
Long orderCount = orderMapper.selectCount(orderWrapper);
inviteInfo.put("orderCount", orderCount.intValue());
```

**修改后**:
```java
Map<String, Object> inviteInfo = new HashMap<>();
inviteInfo.put("id", invite.getId());
inviteInfo.put("nickname", invite.getNickname());
inviteInfo.put("username", invite.getUsername());
inviteInfo.put("avatar", invite.getAvatar());
inviteInfo.put("mobile", invite.getMobile());
inviteInfo.put("inviteTime", invite.getPromotionTime());
inviteInfo.put("registerTime", invite.getRegisterTime());
inviteInfo.put("lastLoginTime", invite.getLastLoginTime());
inviteInfo.put("userLevelId", invite.getUserLevelId());
inviteInfo.put("points", invite.getPoints() != null ? invite.getPoints() : 0);
inviteInfo.put("balance", invite.getBalance() != null ? invite.getBalance().toString() : "0.00");

// 获取订单统计信息
// ... 完整的订单和消费统计
// 获取推广统计信息
// ... 完整的推广数据统计
// 获取推广收益信息
// ... 完整的收益数据统计
```

### 2. 扩展AdminUserVO类

**文件**: `server/src/main/java/com/logic/code/model/vo/AdminUserListVO.java`

**新增字段**:
```java
private Integer todayInviteCount; // 今日直邀数量
private Integer monthInviteCount; // 本月直邀数量
```

**新增方法**:
```java
public Integer getTodayInviteCount() { return todayInviteCount; }
public void setTodayInviteCount(Integer todayInviteCount) { this.todayInviteCount = todayInviteCount; }
public Integer getMonthInviteCount() { return monthInviteCount; }
public void setMonthInviteCount(Integer monthInviteCount) { this.monthInviteCount = monthInviteCount; }
```

### 3. 完善getAdminUserList方法

**文件**: `server/src/main/java/com/logic/code/service/UserService.java`

**新增统计逻辑**:
```java
// 获取今日直邀数量
QueryWrapper<User> todayInviteWrapper = new QueryWrapper<>();
todayInviteWrapper.eq("promoter_id", user.getId());
todayInviteWrapper.ge("promotion_time", getTodayStart());
Long todayInviteCount = userMapper.selectCount(todayInviteWrapper);
userVO.setTodayInviteCount(todayInviteCount.intValue());

// 获取本月直邀数量
QueryWrapper<User> monthInviteWrapper = new QueryWrapper<>();
monthInviteWrapper.eq("promoter_id", user.getId());
monthInviteWrapper.ge("promotion_time", getMonthStart());
Long monthInviteCount = userMapper.selectCount(monthInviteWrapper);
userVO.setMonthInviteCount(monthInviteCount.intValue());
```

### 4. 完善searchPromotionUsers方法

**文件**: `server/src/main/java/com/logic/code/service/UserService.java`

**修改内容**:
- 添加了完整的用户基本信息
- 添加了订单统计计算
- 添加了推广统计计算
- 添加了推广收益计算
- 确保返回数据与页面需求完全匹配

## 涉及的接口

### 1. GetPromotionUserDetail接口
- **URL**: `/wechat/user/getPromotionUserDetail`
- **方法**: POST
- **用途**: 获取推广用户详情，包含邀请用户列表
- **返回**: 完整的用户信息和统计数据

### 2. AdminUserList接口
- **URL**: `/wechat/admin/user/list2`
- **方法**: GET
- **用途**: 管理员获取用户列表
- **返回**: 包含推广统计的用户列表

### 3. 搜索功能
- 支持按用户名和手机号搜索
- 返回完整的用户信息和统计数据

## 数据完整性保证

### 1. 字段映射
确保后端返回的字段名与前端期望的字段名一致：
- `inviteTime` 和 `promotionTime` 都提供（兼容性）
- `orderCount` 和 `totalOrderCount` 都提供
- 所有金额字段都转换为字符串格式

### 2. 默认值处理
对于可能为空的字段提供默认值：
- 数值字段默认为0
- 金额字段默认为"0.00"
- 字符串字段默认为空字符串或null

### 3. 异常处理
对于统计数据获取失败的情况，提供默认值，确保接口不会因为统计计算失败而报错。

## 测试验证

### 1. 管理员查看区域总监列表
- 验证返回的用户信息完整
- 验证推广统计数据准确

### 2. 管理员下钻查看直邀列表
- 验证显示推广用户统计
- 验证用户列表数据完整

### 3. 区域总监查看自己的推广用户
- 验证用户信息完整显示
- 验证统计数据准确

## 性能优化建议

1. **批量查询**: 对于用户列表，可以考虑批量查询统计信息以提高性能
2. **缓存机制**: 对于不经常变化的统计数据，可以考虑添加缓存
3. **分页优化**: 确保分页查询的性能，避免大数据量时的性能问题

## 后续优化方向

1. **数据预计算**: 将一些统计数据预先计算并存储，减少实时计算
2. **索引优化**: 为查询频繁的字段添加数据库索引
3. **接口合并**: 考虑将相关的统计查询合并，减少数据库访问次数
