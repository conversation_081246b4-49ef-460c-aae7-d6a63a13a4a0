# 管理员下钻查看直邀统计显示推广用户统计修改总结

## 需求描述
当管理员（userLevelId=1）下钻查看直邀统计时，需要显示推广用户统计而不是区域总监统计。

## 修改前的逻辑
- 管理员在任何情况下都显示区域总监统计
- 区域总监显示推广用户统计

## 修改后的逻辑
- 管理员在第一级（drillDownLevel=0）时显示区域总监统计
- 管理员下钻查看直邀列表时（drillDownLevel>0）显示推广用户统计
- 区域总监始终显示推广用户统计

## 具体修改内容

### 1. 前端模板修改 (team.wxml)

#### 修改统计标题显示逻辑
```xml
<!-- 修改前 -->
<view class="stats-title" wx:if="{{userLevelId == 1}}">区域总监统计</view>
<view class="stats-title" wx:else>推广用户统计</view>

<!-- 修改后 -->
<view class="stats-title" wx:if="{{userLevelId == 1 && drillDownLevel == 0}}">区域总监统计</view>
<view class="stats-title" wx:else>推广用户统计</view>
```

#### 修改统计内容显示逻辑
```xml
<!-- 修改前 -->
<view class="stats-content" wx:if="{{userLevelId == 1}}">
  <!-- 区域总监统计内容 -->
</view>

<!-- 修改后 -->
<view class="stats-content" wx:if="{{userLevelId == 1 && drillDownLevel == 0}}">
  <!-- 区域总监统计内容 -->
</view>
```

### 2. JavaScript逻辑修改 (team.js)

#### 修改loadStats方法
```javascript
// 修改前
if (this.data.userLevelId == 1) {
  // 管理员统计信息 - 区域总监统计
  // ...
} else if (this.data.userLevelId == 2) {
  // 区域总监统计信息
  // ...
}

// 修改后
if (this.data.userLevelId == 1 && this.data.drillDownLevel == 0) {
  // 管理员在第一级时显示区域总监统计
  // ...
} else if (this.data.userLevelId == 2 || (this.data.userLevelId == 1 && this.data.drillDownLevel > 0)) {
  // 区域总监统计信息 或 管理员下钻时显示推广用户统计
  let targetUserId;
  if (this.data.userLevelId == 2) {
    // 区域总监查看自己的统计
    targetUserId = wx.getStorageSync('userInfo').id;
  } else {
    // 管理员下钻时查看指定用户的统计
    targetUserId = this.data.currentDrillUserId;
  }
  // ...
}
```

#### 修改viewPromotionDetail方法
在下钻时添加重新加载统计信息的调用：
```javascript
// 加载该用户的直邀列表
this.loadDrillDownTeamList(userId);

// 重新加载统计信息（管理员下钻时需要显示推广用户统计）
this.loadStats();
```

#### 修改drillBack方法
在返回上一级时添加重新加载统计信息的调用：
```javascript
this.setData({
  // ... 恢复状态
});

// 重新加载统计信息（根据当前层级显示正确的统计）
this.loadStats();
```

#### 修改goToHome方法
在返回首页时确保重新加载正确的统计信息：
```javascript
this.setData({
  // ... 重置状态
});
this.loadTeamList(true);
// 重新加载统计信息（返回首页时显示管理员的区域总监统计）
this.loadStats();
```

## 功能验证

### 测试场景
1. **管理员首次进入页面**
   - 应显示"区域总监统计"
   - 显示区域总监相关数据

2. **管理员点击"直邀明细"下钻**
   - 标题应变为"推广用户统计"
   - 显示该用户的推广统计数据

3. **管理员在下钻状态下返回**
   - 应正确恢复到"区域总监统计"

4. **区域总监用户**
   - 始终显示"推广用户统计"
   - 显示自己的推广数据

## 涉及的API接口
- `DirectorStats`: 获取区域总监统计数据
- `GetPromotionStats`: 获取推广用户统计数据

## 注意事项
1. 确保在所有状态切换时都正确调用`loadStats()`方法
2. 管理员下钻时需要传递正确的`currentDrillUserId`
3. 统计数据的显示逻辑与下钻层级紧密相关
