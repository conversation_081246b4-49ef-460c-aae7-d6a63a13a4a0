// 团队管理页面
const util = require('../../../utils/util.js');
const api = require('../../../config/api.js');

Page({
  data: {
    userLevelId: 0, // 用户等级：1-管理员，2-区域总监
    teamList: [],
    page: 1,
    pageSize: 10,
    hasMore: true,
    loading: false,
    loadingMore: false,
    searchKeyword: '', // 搜索关键词
    currentType: '', // 当前筛选类型
    showUserDetail: false, // 是否显示用户详情弹窗
    userDetail: {}, // 用户详情数据
    
    // 下钻相关状态
    drillDownLevel: 0, // 下钻层级：0-第一级，1-第二级...
    drillDownStack: [], // 下钻历史栈
    currentDrillUserId: null, // 当前下钻的用户ID
    currentDrillUserName: '', // 当前下钻的用户名称
    
    // 管理员相关数据
    adminStats: {
      totalDirectors: 0,
      newDirectors: 0,
      activeDirectors: 0
    },
    
    // 区域总监相关数据
    directorStats: {
      totalUsers: 0,
      newUsers: 0,
      activeUsers: 0,
      fansCount: 0
    },
    
    // 自定义导航栏相关
    navOpacity: 0.95,          // 导航栏透明度，初始稍微透明
    navbarHeight: 0,           // 导航栏高度
  },

  onLoad: function (options) {
    // 初始化导航栏
    this.initNavbar();
    
    // 检查用户权限
    this.checkUserPermission();
    
    // 加载团队列表
    this.loadTeamList();
    
    // 加载统计信息
    this.loadStats();
  },

  /**
   * 检查用户权限
   */
  checkUserPermission: function () {
    try {
      const userInfo = wx.getStorageSync('userInfo');
      const token = wx.getStorageSync('token');

      if (!userInfo || !token) {
        this.redirectToLogin();
        return false;
      }


      this.setData({
        userLevelId: userInfo.userLevelId
      });
      
      return true;
    } catch (e) {
      console.error('检查用户权限失败:', e);
      this.redirectToLogin();
      return false;
    }
  },

  /**
   * 加载统计信息
   */
  loadStats: function () {
    if (this.data.userLevelId == 1 && this.data.drillDownLevel == 0) {
      // 管理员在第一级时显示区域总监统计
      util.request(api.DirectorStats, {}, 'GET').then(res => {
        if (res.success) {
          this.setData({
            adminStats: {
              totalDirectors: res.data.totalDirectors || 0,
              newDirectors: res.data.newDirectors || 0,
              activeDirectors: res.data.monthDirectors || 0 // 使用月新增作为活跃总监
            }
          });
        }
      }).catch(err => {
        console.log('获取区域总监统计信息失败:', err);
      });
    } else if (this.data.userLevelId == 2 || (this.data.userLevelId == 1 && this.data.drillDownLevel > 0)) {
      // 区域总监统计信息 或 管理员下钻时显示推广用户统计
      let targetUserId;
      if (this.data.userLevelId == 2) {
        // 区域总监查看自己的统计
        targetUserId = wx.getStorageSync('userInfo').id;
      } else {
        // 管理员下钻时查看指定用户的统计
        targetUserId = this.data.currentDrillUserId;
      }

      if (targetUserId) {
        util.request(api.GetPromotionStats, { userId: targetUserId }, 'GET').then(res => {
          if (res.success) {
            this.setData({
              directorStats: {
                totalUsers: res.data.promotionCount || 0,
                newUsers: res.data.todayInviteCount || 0, // 今日直邀用户数
                activeUsers: res.data.monthInviteCount || 0, // 本月直邀用户数
                fansCount: res.data.fansCount || 0 // 粉丝数
              }
            });
          }
        }).catch(err => {
          console.log('获取推广用户统计信息失败:', err);
        });
      }
    }
  },

  /**
   * 重定向到登录页面
   */
  redirectToLogin: function () {
    wx.showToast({
      title: '请先登录',
      icon: 'none'
    });
    setTimeout(() => {
      wx.navigateTo({
        url: '/pages/auth/login/login'
      });
    }, 1500);
  },

  /**
   * 加载团队列表
   */
  loadTeamList: function (isRefresh = false) {
    if (!this.checkUserPermission()) {
      return;
    }

    if (isRefresh) {
      this.setData({
        page: 1,
        hasMore: true,
        teamList: []
      });
    }

    if (this.data.loading || this.data.loadingMore) {
      return;
    }

    this.setData({
      loading: isRefresh || this.data.page === 1,
      loadingMore: !isRefresh && this.data.page > 1
    });

    let params = {};
    let apiUrl = '';
    let method = 'POST';

    if (this.data.userLevelId == 1) {
      // 管理员 - 查看区域总监列表（使用原有接口）
      apiUrl = api.AdminUserList;
      params = {
        pageNum: this.data.page,
        pageSize: this.data.pageSize,
        keyword: this.data.searchKeyword,
        userLevelId: 2 // 添加user_level_id=2过滤条件
      };
      method = 'GET';
    } else if (this.data.userLevelId == 2) {
      // 区域总监 - 查看自己推广的用户列表（使用新的搜索接口）
      apiUrl = api.GetPromotionUserDetail;
      if (this.data.searchKeyword && this.data.searchKeyword.trim()) {
        // 搜索模式
        params = {
          searchKeyword: this.data.searchKeyword.trim(),
          page: this.data.page,
          size: this.data.pageSize
        };
      } else {
        // 普通模式，获取推广统计信息
        params = {
          userId: wx.getStorageSync('userInfo').id
        };
      }
      method = 'POST';
    }

    util.request(apiUrl, params, method).then(res => {
      this.handleTeamListResponse(res, isRefresh);
    }).catch(err => {
      console.log('获取团队列表失败:', err);
      wx.showToast({
        title: '获取数据失败',
        icon: 'none'
      });
      
      this.setData({
        loading: false,
        loadingMore: false
      });
    });
  },

  /**
   * 加载下钻用户的直邀列表
   */
  loadDrillDownTeamList: function (userId) {
    if (!this.checkUserPermission()) {
      return;
    }

    this.setData({
      loading: true,
      page: 1,
      hasMore: true
    });

    let params = {};
    if (this.data.searchKeyword && this.data.searchKeyword.trim()) {
      // 搜索模式
      params = {
        searchKeyword: this.data.searchKeyword.trim(),
        page: this.data.page,
        size: this.data.pageSize
      };
    } else {
      // 普通模式，获取推广用户详情
      params = {
        userId: userId
      };
    }

    // 调用获取推广用户详情的接口
    util.request(api.GetPromotionUserDetail, params, 'POST').then(res => {
      this.handleDrillDownResponse(res);
    }).catch(err => {
      console.log('获取下钻用户直邀列表失败:', err);
      wx.showToast({
        title: '获取数据失败',
        icon: 'none'
      });
      this.setData({
        loading: false
      });
    });
  },

  /**
   * 处理下钻响应数据
   */
  handleDrillDownResponse: function (res) {
    this.setData({
      loading: false
    });

    if (res.success && res.data) {
      let users = [];
      let hasMore = false;
      
      if (this.data.searchKeyword && this.data.searchKeyword.trim()) {
        // 搜索模式返回的数据结构
        users = res.data.list || [];
        hasMore = res.data.page < res.data.pages;
      } else {
        // 普通模式返回的数据结构
        users = res.data.invitesList || [];
        hasMore = false; // 普通模式不分页
      }
      
      // 处理数据格式，确保与UI匹配
      const formattedUsers = users.map(item => {
        return {
          ...item,
          userLevelText: this.getUserLevelText(item.userLevelId),
          isOnline: this.isOnline(item.lastLoginTime)
        };
      });

      this.setData({
        teamList: formattedUsers,
        hasMore: hasMore
      });
    } else {
      wx.showToast({
        title: res.msg || '获取直邀列表失败',
        icon: 'none'
      });
    }
  },

  /**
   * 处理团队列表响应
   */
  handleTeamListResponse: function (res, isRefresh) {
    this.setData({
      loading: false,
      loadingMore: false
    });

    if (res.success) {
      let newList = [];
      let hasMore = false;
      
      // 处理不同的接口返回数据结构
      if (this.data.userLevelId == 2) {
        // 区域总监 - 推广用户详情接口返回的数据结构
        if (this.data.searchKeyword && this.data.searchKeyword.trim()) {
          // 搜索模式返回的数据结构
          newList = res.data.list || [];
          hasMore = res.data.page < res.data.pages;
        } else {
          // 普通模式返回的数据结构
          newList = res.data.invitesList || [];
          hasMore = false; // 普通模式不分页
        }
      } else {
        // 管理员 - 用户列表接口返回的数据结构
        newList = res.data.list || [];
        hasMore = res.data.pageNum < res.data.pages;
      }
      
      // 处理数据格式，确保与UI匹配
      newList = newList.map(item => {
        return {
          ...item,
          userLevelText: this.getUserLevelText(item.userLevelId),
          isOnline: this.isOnline(item.lastLoginTime)
        };
      });
      
      const teamList = isRefresh ? newList : [...this.data.teamList, ...newList];

      this.setData({
        teamList: teamList,
        hasMore: hasMore,
        page: this.data.page + 1
      });
    } else {
      wx.showToast({
        title: res.msg || '获取团队列表失败',
        icon: 'none'
      });
    }
  },

  /**
   * 获取用户等级文本
   */
  getUserLevelText: function (levelId) {
    const levelMap = {
      0: 'L0',
      1: 'L1',
      2: 'VIP',
      3: 'SVIP'
    };
    return levelMap[levelId] || 'L0';
  },

  /**
   * 判断用户是否在线（最近1小时登录算在线）
   */
  isOnline: function (lastLoginTime) {
    if (!lastLoginTime) return false;
    const lastLogin = new Date(lastLoginTime);
    const now = new Date();
    const diffMinutes = (now - lastLogin) / (1000 * 60);
    return diffMinutes <= 60;
  },

  /**
   * 搜索输入
   */
  onSearchInput: function (e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  /**
   * 搜索团队成员
   */
  searchTeam: function () {
    // 如果在下钻状态，调用下钻搜索方法；否则调用普通搜索方法
    if (this.data.drillDownLevel > 0 && this.data.currentDrillUserId) {
      this.loadDrillDownTeamList(this.data.currentDrillUserId);
    } else {
      this.loadTeamList(true);
    }
  },

  /**
   * 查看用户详情
   */
  viewUserDetail: function (e) {
    const userId = e.currentTarget.dataset.id;
    const user = this.data.teamList.find(item => item.id == userId);
    
    if (user) {
      this.setData({
        userDetail: user,
        showUserDetail: true
      });
    }
  },

  /**
   * 关闭用户详情弹窗
   */
  closeUserDetail: function () {
    this.setData({
      showUserDetail: false
    });
  },

  /**
   * 查看用户订单
   */
  viewUserOrders: function (e) {
    console.log("viewUserOrders===",e)
    const userId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/ucenter/order/order?userId=${userId}`
    });
  },

  /**
   * 查看推广明细 - 下钻到该用户的直邀列表
   */
  viewPromotionDetail: function (e) {
    const userId = e.currentTarget.dataset.userId;
    const userName = e.currentTarget.dataset.userName;
    console.log('下钻查看用户直邀列表 userId:', userId, 'userName:', userName);

    // 保存当前状态到下钻栈
    const currentState = {
      teamList: this.data.teamList,
      page: this.data.page,
      hasMore: this.data.hasMore,
      searchKeyword: this.data.searchKeyword
    };

    // 更新下钻状态
    this.setData({
      drillDownLevel: this.data.drillDownLevel + 1,
      drillDownStack: [...this.data.drillDownStack, currentState],
      currentDrillUserId: userId,
      currentDrillUserName: userName,
      teamList: [], // 清空列表准备加载新数据
      page: 1,
      hasMore: true
      // 注意：这里移除了searchKeyword重置，保持搜索状态
    });

    // 加载该用户的直邀列表
    this.loadDrillDownTeamList(userId);

    // 重新加载统计信息（管理员下钻时需要显示推广用户统计）
    this.loadStats();
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function () {
    this.loadTeamList(true);
    this.loadStats();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 上拉加载更多
   */
  onReachBottom: function () {
    if (this.data.hasMore && !this.data.loadingMore) {
      this.loadTeamList();
    }
  },

  /**
   * 返回
   */
  goBack: function () {
    // 如果在下钻状态，则返回上一级
    if (this.data.drillDownLevel > 0) {
      this.drillBack();
    } else {
      wx.navigateBack();
    }
  },

  /**
   * 下钻返回上一级
   */
  drillBack: function () {
    if (this.data.drillDownStack.length === 0) {
      return;
    }

    // 从栈中恢复上一级状态
    const prevState = this.data.drillDownStack[this.data.drillDownStack.length - 1];
    const newStack = this.data.drillDownStack.slice(0, -1);

    this.setData({
      drillDownLevel: this.data.drillDownLevel - 1,
      drillDownStack: newStack,
      currentDrillUserId: newStack.length > 0 ? this.data.currentDrillUserId : null,
      currentDrillUserName: newStack.length > 0 ? this.data.currentDrillUserName : '',
      teamList: prevState.teamList,
      page: prevState.page,
      hasMore: prevState.hasMore,
      searchKeyword: prevState.searchKeyword
    });

    // 重新加载统计信息（根据当前层级显示正确的统计）
    this.loadStats();
  },

  /**
   * 返回首页（重置所有下钻状态）
   */
  goToHome: function () {
    this.setData({
      drillDownLevel: 0,
      drillDownStack: [],
      currentDrillUserId: null,
      currentDrillUserName: ''
    });
    this.loadTeamList(true);
    // 重新加载统计信息（返回首页时显示管理员的区域总监统计）
    this.loadStats();
  },

  /**
   * 初始化导航栏
   */
  initNavbar: function() {
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight || 0;
    const titleBarHeight = 44;
    const navbarHeight = statusBarHeight + titleBarHeight;

    this.setData({
      navbarHeight: navbarHeight
    });
  },

  /**
   * 导航栏返回事件
   */
  onNavBack: function() {
    // 如果在下钻状态，则返回上一级
    if (this.data.drillDownLevel > 0) {
      this.drillBack();
    } else {
      const pages = getCurrentPages();
      if (pages.length > 1) {
        wx.navigateBack();
      } else {
        wx.switchTab({
          url: '/pages/ucenter/me/me'
        });
      }
    }
  },

  /**
   * 查看可提现金额收入明细
   */
  viewWithdrawableDetail: function(e) {
    const userId = e.currentTarget.dataset.userId;
    const userName = e.currentTarget.dataset.userName;
    
    console.log('查看用户收入明细 userId:', userId, 'userName:', userName);
    
    wx.navigateTo({
      url: `/pages/ucenter/withdrawable-detail/withdrawable-detail?userId=${userId}&userName=${encodeURIComponent(userName)}`
    });
  },

  /**
   * 页面滚动事件 - 动态调整导航栏透明度
   */
  onPageScroll: function(e) {
    const scrollTop = e.detail.scrollTop;
    // 根据滚动距离计算透明度
    // 滚动0-100rpx时，透明度从0.95变化到1
    let opacity = Math.min(1, Math.max(0.95, 0.95 + (scrollTop / 200)));

    // 避免频繁更新，只在透明度变化超过0.02时才更新
    if (Math.abs(opacity - this.data.navOpacity) > 0.02) {
      this.setData({
        navOpacity: opacity
      });
    }
  }
});