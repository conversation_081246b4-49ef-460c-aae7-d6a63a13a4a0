<!-- 团队管理页面 -->
<wxs src="../../../utils/format.wxs" module="format" />

<!-- 自定义导航栏 -->
<custom-navbar
  title="{{drillDownLevel === 0 ? '团队管理中心' : currentDrillUserName + '的直邀列表'}}"
  gradient-background="linear-gradient(135deg, #42A5F5 0%, #64B5F6 100%)"
  text-color="#ffffff"
  opacity="{{navOpacity}}"
  bind:back="onNavBack">
  <view slot="right" wx:if="{{drillDownLevel > 0}}" bindtap="goToHome">
    <text style="color: #ffffff; font-size: 28rpx;">首页</text>
  </view>
</custom-navbar>

<scroll-view
  class="container"
  style="padding-top: {{navbarHeight}}px;"
  scroll-y="{{true}}"
  bindscroll="onPageScroll"
  enhanced="{{true}}"
  show-scrollbar="{{false}}">
  
  <!-- 下钻导航提示 -->
  <view class="drilldown-tip" wx:if="{{drillDownLevel > 0}}">
    <text class="tip-text">当前查看：{{currentDrillUserName}}的直邀列表</text>
  </view>
  
  <!-- 搜索区域 -->
  <view class="search-section">
    <view class="search-bar">
      <input class="search-input" placeholder="搜索用户昵称、手机号" bindinput="onSearchInput" value="{{searchKeyword}}" />
      <view class="search-btn" bindtap="searchTeam">
        <text>🔍</text>
      </view>
    </view>
  </view>
  
  <!-- 统计信息卡片 -->
  <view class="stats-card">
    <view class="stats-header">
      <view class="stats-title" wx:if="{{userLevelId == 1 && drillDownLevel == 0}}">区域总监统计</view>
      <view class="stats-title" wx:else>推广用户统计</view>
    </view>
    <view class="stats-content" wx:if="{{userLevelId == 1 && drillDownLevel == 0}}">
      <view class="stat-item">
        <view class="stat-number">{{adminStats.totalDirectors || 0}}</view>
        <view class="stat-label">区域总监</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{adminStats.newDirectors || 0}}</view>
        <view class="stat-label">今日直邀</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{adminStats.activeDirectors || 0}}</view>
        <view class="stat-label">本月直邀</view>
      </view>
    </view>
    <view class="stats-content" wx:else>
      <view class="stat-item">
        <view class="stat-number">{{directorStats.totalUsers || 0}}</view>
        <view class="stat-label">总直邀用户</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{directorStats.newUsers || 0}}</view>
        <view class="stat-label">今日直邀</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{directorStats.activeUsers || 0}}</view>
        <view class="stat-label">本月直邀</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{directorStats.fansCount || 0}}</view>
        <view class="stat-label">粉丝数</view>
      </view>
    </view>
  </view>
  
  <!-- 团队列表 -->
  <view class="user-list">
    <view class="user-item" wx:for="{{teamList}}" wx:key="id">
      <!-- 用户基本信息 -->
      <view class="user-header">
    <!--    <image class="user-avatar" src="{{item.avatar || '/static/images/svg/头像.svg'}}" mode="aspectFill"></image>-->
        <view class="user-info">
          <view class="user-name-row">
            <text class="user-name">{{item.nickname || item.username || '匿名用户'}}</text>
            <view class="user-level level-{{item.userLevelId || 0}}">
              {{item.userLevelText}}
            </view>
          </view>
          <text class="user-mobile" wx:if="item.mobile" >{{item.mobile}}</text>
          <text class="user-register-time">注册时间：{{format.formatDateTime(item.registerTime)}}</text>
        </view>
        <!--<view class="user-status">
          <view class="status-dot {{item.isOnline ? 'online' : 'offline'}}"></view>
          <text class="status-text">{{item.isOnline ? '在线' : '离线'}}</text>
        </view>-->
      </view>
      
      <!-- 推广者信息 -->
     <!-- <view class="promoter-info" wx:if="{{item.promoterName}}">
        <text class="promoter-label">推广者：</text>
        <text class="promoter-name">{{item.promoterName}}</text>
      </view>-->
      
      <!-- 用户数据 - 第一行（基础数据） -->
      <view class="user-data user-data-row">
        <view class="data-item">
          <text class="data-label">订单数</text>
          <text class="data-value">{{item.orderCount || 0}}</text>
        </view>
        <view class="data-item">
          <text class="data-label">消费金额</text>
          <text class="data-value">¥{{item.totalAmount || '0.00'}}</text>
        </view>
        <view class="data-item">
          <text class="data-label">积分</text>
          <text class="data-value">{{item.points || 0}}</text>
        </view>
        <view class="data-item">
          <text class="data-label">余额</text>
          <text class="data-value">¥{{item.balance || '0.00'}}</text>
        </view>
      </view>
      
      <!-- 用户数据 - 第二行（推广数据） -->
      <view class="user-data user-data-row promotion-data">
        <view class="data-item">
          <text class="data-label">今日直邀</text>
          <text class="data-value">{{item.todayInviteCount || 0}}人</text>
        </view>
        <view class="data-item">
          <text class="data-label">本月直邀</text>
          <text class="data-value">{{item.monthInviteCount || 0}}人</text>
        </view>
        <view class="data-item">
          <text class="data-label">总直邀</text>
          <text class="data-value">{{item.promotionCount || 0}}人</text>
        </view>
        <view class="data-item">
          <text class="data-label">待确认收益</text>
          <text class="data-value">¥{{item.pendingEarnings || '0.00'}}</text>
        </view>
        <view class="data-item">
          <text class="data-label">可提现金额</text>
          <text class="data-value withdrawable-amount" bindtap="viewWithdrawableDetail" data-user-id="{{item.id}}" data-user-name="{{item.nickname || item.username}}">¥{{item.withdrawableAmount || '0.00'}}</text>
        </view>
      </view>
      
      <!-- 操作按钮 -->
      <view class="user-actions">
        <view class="action-btn detail-btn" bindtap="viewPromotionDetail" data-user-id="{{item.id}}" data-user-name="{{item.nickname || item.username}}">
          直邀明细
        </view>
       <!-- <view class="action-btn detail-btn" bindtap="viewUserDetail" data-id="{{item.id}}">
          查看详情
        </view>-->
        <view class="action-btn order-btn" bindtap="viewUserOrders" data-id="{{item.id}}">
          查看订单
        </view>
      </view>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{teamList.length === 0 && !loading}}">
      <text class="empty-text">暂无团队成员</text>
    </view>
    
    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{hasMore}}">
      <text class="load-text" wx:if="{{!loadingMore}}">上拉加载更多</text>
      <text class="load-text" wx:else>加载中...</text>
    </view>
    
    <!-- 没有更多 -->
    <!-- <view class="no-more" wx:if="{{!hasMore && teamList.length > 0}}">
      <text>没有更多用户了</text>
    </view> -->
  </view>
  
  <!-- 加载中 -->
  <view class="loading-overlay" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
</scroll-view>

<!-- 用户详情弹窗 -->
<view class="user-detail-modal" wx:if="{{showUserDetail}}">
  <view class="modal-mask" bindtap="closeUserDetail"></view>
  <view class="modal-dialog">
    <view class="modal-header">
      <text class="modal-title">用户详情</text>
      <view class="modal-close" bindtap="closeUserDetail">×</view>
    </view>
    <view class="modal-content">
      <!-- 基本信息 -->
      <view class="detail-section">
        <view class="detail-title">基本信息</view>
        <view class="user-detail-header">
          <image class="detail-avatar" src="{{userDetail.avatar || '/static/images/svg/头像.svg'}}" mode="aspectFill"></image>
          <view class="detail-info">
            <text class="detail-name">
              {{userDetail.nickname || userDetail.username || '未知用户'}}
            </text>
            <text class="detail-level">等级：{{userDetail.userLevelText || 'L0'}}</text>
          </view>
        </view>
        <view class="detail-item">
          <text class="detail-label">手机号：</text>
          <text class="detail-value">{{userDetail.mobile || '未绑定'}}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">注册时间：</text>
          <text class="detail-value">{{format.formatDateTime(userDetail.registerTime)}}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">最后登录：</text>
          <text class="detail-value">{{userDetail.lastLoginTime || '未知'}}</text>
        </view>
      </view>
      <!-- 消费数据 -->
      <view class="detail-section">
        <view class="detail-title">消费数据</view>
        <view class="detail-item">
          <text class="detail-label">订单总数：</text>
          <text class="detail-value">{{userDetail.orderCount || 0}}单</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">消费总额：</text>
          <text class="detail-value">¥{{userDetail.totalAmount || '0.00'}}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">当前积分：</text>
          <text class="detail-value">{{userDetail.points || 0}}分</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">账户余额：</text>
          <text class="detail-value">¥{{userDetail.balance || '0.00'}}</text>
        </view>
      </view>
      <!-- 推广数据 -->
      <view class="detail-section" wx:if="{{userDetail.promotionCount > 0 || userDetail.promoterId}}">
        <view class="detail-title">推广数据</view>
        <view class="detail-item" wx:if="{{userDetail.promotionCount > 0}}">
          <text class="detail-label">推广用户：</text>
          <text class="detail-value">{{userDetail.promotionCount}}人</text>
        </view>
        <view class="detail-item" wx:if="{{userDetail.promoterId}}">
          <text class="detail-label">推广者：</text>
          <text class="detail-value">{{userDetail.promoterName}}</text>
        </view>
        <view class="detail-item" wx:if="{{userDetail.promotionEarnings}}">
          <text class="detail-label">推广收益：</text>
          <text class="detail-value">¥{{userDetail.promotionEarnings}}</text>
        </view>
      </view>
    </view>
  </view>
</view>