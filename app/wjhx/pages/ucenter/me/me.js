// pages/ucenter/me/me.js
const util = require('../../../utils/util.js');
const api = require('../../../config/api.js');
const pay = require('../../../services/pay.js');
var app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    orderAmount: {
      unPay: 0,
      payed: 0,
      consignment: 0
    },
    cardCount: 0,
    footprintCount: 0,
    collectionCount: 0,
    couponCount: 0, // 用户优惠券数量
    userBalance: '0.00', // 用户余额
    userPoints: 0, // 用户积分
    hasLogin: false,
    userInfo: {
      id: '',
      username: '',
      nickname: '',
      avatar: '',
      mobile: '',
      gender: 0
    },
    hasBindPhone: false,
    hasSub: false,
    showBindPhoneModal: false,
    showContactModal: false,
    showPromotionQrModal: false,
    tempNickname: '', // 临时存储昵称
    promotionQrCode: {}, // 推广二维码信息
    customerService: {
      phone: (api.CustomerService && api.CustomerService.phone) || '136 2929 5757',
      wechat: (api.CustomerService && api.CustomerService.wechat) || 'WJSY-029-888',
      workTime: (api.CustomerService && api.CustomerService.workTime) || '工作时间：7:00-24:00'
    },
    // 团队统计数据
    teamStats: {
      totalDirectors: 0, // 管理员看到的总监数
      totalUsers: 0      // 总监看到的团队成员数
    }
  },

  /**
   * 安全获取用户信息属性
   */
  getUserInfoProperty: function(property, defaultValue = '') {
    return (this.data.userInfo && this.data.userInfo[property]) || defaultValue;
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.checkLoginStatus();

    // 设置导航栏渐变色
    this.setNavigationBarGradient();
  },

  /**
   * 设置导航栏渐变色
   */
  setNavigationBarGradient: function () {
    wx.setNavigationBarColor({
      frontColor: '#000000',
      backgroundColor: '#a2d6ff',
      animation: {
        duration: 400,
        timingFunc: 'easeIn'
      }
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    console.log('个人中心页面显示');

    // 检查登录状态
    this.checkLoginStatus();

    // 延迟检查，确保数据更新完成
    setTimeout(() => {
      if (this.data.hasLogin) {
        console.log('用户已登录，获取用户信息');
        this.getUserInfo();
      } else {
        console.log('用户未登录');
        // 未登录时重置余额和积分
        this.setData({
          userBalance: '0.00',
          userPoints: 0
        });
      }
    }, 100);

    // 每次页面显示时重新设置导航栏样式
    this.setNavigationBarGradient();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () { },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () { },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    if (this.data.hasLogin) {
      this.getUserInfo();
    }
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () { },
  // 格式化分享图片URL，确保符合微信分享要求
  formatShareImageUrl: function(url) {
    if (!url) {
      return '';
    }

    // 使用项目的formatImageUrl函数
    let formattedUrl = util.formatImageUrl(url);

    // 如果是localhost，需要替换为实际的域名
    if (formattedUrl.includes('localhost:9999')) {
      // 使用配置文件中的域名
      formattedUrl = formattedUrl.replace('http://localhost:9999', 'https://www.sxwjsm.com');
    }

    // 确保使用HTTPS协议（微信分享要求）
    if (formattedUrl.startsWith('http://')) {
      formattedUrl = formattedUrl.replace('http://', 'https://');
    }

    return formattedUrl;
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    let shareImageUrl = '';
    let shareTitle = '伍俊惠选 - 个人中心';
    let shareDesc = '精选优质商品，品质生活选择';
    let sharePath = '/pages/auth/login/login'; // 默认分享到登录页面

    // 如果用户已登录，使用用户头像作为分享图片，并添加推广信息
    if (this.data.hasLogin && this.getUserInfoProperty('avatar')) {
      shareImageUrl = this.formatShareImageUrl(this.getUserInfoProperty('avatar'));
      const username = this.getUserInfoProperty('username');
      if (username) {
        shareTitle = `${username}邀请您体验伍俊惠选`;
        shareDesc = '精选优质商品，品质生活选择，新用户专享优惠';
      }

      // 添加推广参数，让好友通过登录页面进入并建立推广关系
      const userId = this.getUserInfoProperty('id');
      if (userId) {
        // 分享链接直接到登录页面，登录成功后跳转到首页并建立推广关系
        sharePath = `/pages/auth/login/login?promo=${userId}&backUrl=${encodeURIComponent('/pages/index/index')}&backParamJson=${encodeURIComponent(JSON.stringify({promo: userId}))}`;
      }
    } else {
      // 未登录用户分享，直接到登录页面
      shareTitle = '伍俊惠选 - 精选优质商品';
      shareDesc = '品质生活选择，新用户专享优惠';
      sharePath = '/pages/auth/login/login?backUrl=' + encodeURIComponent('/pages/index/index');
    }

    console.log('分享参数:', { shareTitle, shareDesc, sharePath, shareImageUrl });

    return {
      title: shareTitle,
      desc: shareDesc,
      path: sharePath,
      imageUrl: shareImageUrl
    };
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline: function() {
    let shareImageUrl = '';
    let shareTitle = '伍俊惠选 - 品质生活选择';

    // 如果用户已登录，个性化分享内容
    if (this.data.hasLogin) {
      const avatar = this.getUserInfoProperty('avatar');
      if (avatar) {
        shareImageUrl = this.formatShareImageUrl(avatar);
      }

      const username = this.getUserInfoProperty('username');
      if (username) {
        shareTitle = `${username}正在使用伍俊惠选`;
      } else {
        shareTitle = '我正在使用伍俊惠选 - 品质生活选择';
      }

      // 根据用户数据添加个性化描述
      const stats = [];
      if (this.data.collectionCount > 0) {
        stats.push(`收藏了${this.data.collectionCount}件商品`);
      }
      if (this.data.footprintCount > 0) {
        stats.push(`浏览了${this.data.footprintCount}件商品`);
      }

      if (stats.length > 0) {
        shareTitle += ` - ${stats.join('，')}`;
      }
    } else {
      shareTitle = '伍俊惠选 - 精选优质商品，品质生活选择';
    }

    console.log('分享到朋友圈 - 标题:', shareTitle);
    console.log('分享到朋友圈 - 图片URL:', shareImageUrl);

    return {
      title: shareTitle,
      query: '', // 个人中心不需要特殊查询参数
      imageUrl: shareImageUrl
    };
  },

  /**
   * 初始化数据
   */
  initData: function () {
    console.log('初始化数据 - 设置为未登录状态');
    this.setData({
      hasLogin: false,
      userInfo: {},
      hasBindPhone: false,
      hasSub: false,
      cardCount: 0,
      footprintCount: 0,
      collectionCount: 0,
      couponCount: 0,
      userBalance: '0.00',
      userPoints: 0,
    });
  },

  /**
   * 强制刷新登录状态
   */
  forceRefreshLoginStatus: function () {
    console.log('强制刷新登录状态');
    this.checkLoginStatus();
    if (this.data.hasLogin) {
      this.getUserInfo();
    }
  },

  /**
   * 调试存储状态
   */
  debugStorageStatus: function () {
    const userInfo = wx.getStorageSync('userInfo');
    const token = wx.getStorageSync('token');
    const app = getApp();

    console.log('=== 存储状态调试 ===');
    console.log('本地存储 userInfo:', userInfo);
    console.log('本地存储 token:', token);
    console.log('全局数据 userInfo:', app.globalData.userInfo);
    console.log('全局数据 token:', app.globalData.token);
    console.log('页面状态 hasLogin:', this.data.hasLogin);
    console.log('页面状态 userInfo:', this.data.userInfo);

    wx.showModal({
      title: '存储状态',
      content: `Token: ${token ? '存在' : '不存在'}\nUserInfo: ${userInfo ? '存在' : '不存在'}\nhasLogin: ${this.data.hasLogin}`,
      showCancel: false
    });
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus: function () {
    try {
      let userInfo = wx.getStorageSync('userInfo');
      let token = wx.getStorageSync('token');

      console.log('检查登录状态 - userInfo:', userInfo);
      console.log('检查登录状态 - token:', token);

      if (userInfo && token && userInfo !== '' && token !== '') {
        let hasSub = false;
        if (userInfo && userInfo.userLevelId == 1) {
          hasSub = true;
        }

        // 确保userInfo是对象格式
        if (typeof userInfo === 'string') {
          try {
            userInfo = JSON.parse(userInfo);
          } catch (parseError) {
            console.error('解析userInfo失败:', parseError);
            this.initData();
            return;
          }
        }

        this.setData({
          hasLogin: true,
          userInfo: userInfo || {},
          hasBindPhone: (userInfo && userInfo.mobile) ? true : false,
          hasSub: hasSub
        });

        console.log('登录状态检查完成 - hasLogin: true');
      } else {
        console.log('登录状态检查完成 - hasLogin: false');
        this.initData();
      }
    } catch (e) {
      console.error('检查登录状态发生错误', e);
      this.initData();
    }
  },

  /**
   * 获取用户信息
   */
  getUserInfo: function () {
    let that = this;
    wx.showLoading({
      title: '加载中...',
    });

    util.request(api.UserInfo).then(function (res) {
      wx.hideLoading();
      if (res.success) {
        // 更新统计数据
        that.setData({
          cardCount: res.data.cardCount || 0,
          collectionCount: res.data.collectionCount || 0,
          footprintCount: res.data.footprintCount || 0,
          couponCount: res.data.couponCount || 0,
        });

        // 如果返回了用户基本信息，则更新用户信息
        if (res.data.nickname || res.data.avatar) {
          let currentUserInfo = that.data.userInfo || {};

          // 更新用户基本信息
          if (res.data.nickname) currentUserInfo.nickname = res.data.nickname;
          if (res.data.avatar) currentUserInfo.avatar = res.data.avatar;
          if (res.data.mobile) currentUserInfo.mobile = res.data.mobile;
          if (res.data.gender !== undefined) currentUserInfo.gender = res.data.gender;
          if (res.data.username) currentUserInfo.username = res.data.username;

          // 更新页面数据
          that.setData({
            userInfo: currentUserInfo,
            hasBindPhone: currentUserInfo.mobile ? true : false
          });

          // 同时更新本地存储
          wx.setStorageSync('userInfo', currentUserInfo);

          // 更新全局数据
          const app = getApp();
          app.globalData.userInfo = currentUserInfo;

          console.log('用户信息已更新:', currentUserInfo);
        }
      } else {
        wx.showToast({
          title: res.msg || '获取用户信息失败',
          icon: 'none',
          duration: 2000
        });
      }
    }).catch(function (err) {
      wx.hideLoading();
      console.error('获取用户信息失败:', err);
      wx.showToast({
        title: '获取用户信息失败，请重试',
        icon: 'none',
        duration: 2000
      });
    });

    // 获取用户余额
    that.getUserBalance();

    // 获取用户积分
    that.getUserPoints();

    // 获取团队统计数据
    that.getTeamStats();
  },

  /**
   * 获取用户余额
   */
  getUserBalance: function () {
    let that = this;
    
    util.request(api.UserBalance).then(function (res) {
      if (res.success) {
        // 格式化余额显示，保留两位小数
        let balance = parseFloat(res.data.balance || 0).toFixed(2);
        that.setData({
          userBalance: balance
        });
        console.log('用户余额已更新:', balance);
      } else {
        console.error('获取用户余额失败:', res.msg);
        // 失败时设置默认值
        that.setData({
          userBalance: '0.00'
        });
      }
    }).catch(function (err) {
      console.error('获取用户余额异常:', err);
      // 异常时设置默认值
      that.setData({
        userBalance: '0.00'
      });
    });
  },

  /**
   * 获取用户积分
   */
  getUserPoints: function () {
    let that = this;
    
    util.request(api.UserPoints).then(function (res) {
      if (res.success) {
        that.setData({
          userPoints: res.data.points || 0
        });
        console.log('用户积分已更新:', res.data.points || 0);
      } else {
        console.error('获取用户积分失败:', res.msg);
        // 失败时设置默认值
        that.setData({
          userPoints: 0
        });
      }
    }).catch(function (err) {
      console.error('获取用户积分异常:', err);
      // 异常时设置默认值
      that.setData({
        userPoints: 0
      });
    });
  },

  /**
   * 刷新用户余额（供其他页面调用）
   */
  refreshBalance: function () {
    if (this.data.hasLogin) {
      this.getUserBalance();
    }
  },

  /**
   * 刷新用户积分（供其他页面调用）
   */
  refreshPoints: function () {
    if (this.data.hasLogin) {
      this.getUserPoints();
    }
  },

  /**
   * 获取团队统计数据
   */
  getTeamStats: function () {
    let that = this;
    const userInfo = that.data.userInfo;

    // 只有管理员和区域总监才需要获取团队统计
    if (!userInfo || !userInfo.userLevelId || (userInfo.userLevelId != 1 && userInfo.userLevelId != 2)) {
      return;
    }

    if (userInfo.userLevelId == 1) {
      // 管理员 - 获取区域总监统计
      util.request(api.DirectorStats, {}, 'GET').then(res => {
        if (res.success) {
          that.setData({
            'teamStats.totalDirectors': res.data.totalDirectors || 0
          });
          console.log('管理员团队统计已更新:', res.data.totalDirectors);
        }
      }).catch(err => {
        console.log('获取区域总监统计信息失败:', err);
        that.setData({
          'teamStats.totalDirectors': 0
        });
      });
    } else if (userInfo.userLevelId == 2) {
      // 区域总监 - 获取推广用户统计
      util.request(api.GetPromotionStats, { userId: userInfo.id }, 'GET').then(res => {
        if (res.success) {
          that.setData({
            'teamStats.totalUsers': res.data.promotionCount || 0
          });
          console.log('区域总监团队统计已更新:', res.data.promotionCount);
        }
      }).catch(err => {
        console.log('获取区域总监统计信息失败:', err);
        that.setData({
          'teamStats.totalUsers': 0
        });
      });
    }
  },



  /**
   * 选择头像后的回调
   */
  onChooseAvatar: function (e) {
    const { avatarUrl } = e.detail;
    if (!avatarUrl) {
      wx.showToast({
        title: '获取头像失败',
        icon: 'none'
      });
      return;
    }

    // 显示加载中
    wx.showLoading({
      title: '上传中...',
    });

    // 上传头像到服务器
    this.uploadAvatar(avatarUrl);
  },

  /**
   * 上传头像到服务器
   */
  uploadAvatar: function (avatarUrl) {
    const that = this;


    // 上传文件到服务器
    util.request(api.UpdateAvatar, {
      avatar: avatarUrl
    }).then(data => {
      wx.hideLoading();
      if (data.success) {
        // 更新本地存储的用户信息
        let userInfo = wx.getStorageSync('userInfo') || {};
        userInfo.avatar = avatarUrl;
        wx.setStorageSync('userInfo', userInfo);

        // 更新页面显示
        that.setData({
          'userInfo.avatar': avatarUrl
        });

        wx.showToast({
          title: '头像更新成功',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: data.msg || '头像上传失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 输入昵称时的回调
   */
  onInputNickname: function (e) {
    this.setData({
      tempNickname: e.detail.value
    });
  },

  /**
   * 保存昵称
   */
  onSaveNickname: function () {
    const nickname = this.data.tempNickname;
    if (!nickname || nickname.trim() === '') {
      return;
    }

    const that = this;
    wx.showLoading({
      title: '保存中...',
    });

    // 调用后端接口更新昵称
    util.request(api.UpdateNickname, {
      nickname: nickname
    }).then(res => {
      wx.hideLoading();
      if (res.success) {
        // 更新本地存储的用户信息
        let userInfo = wx.getStorageSync('userInfo') || {};
        userInfo.username = nickname;
        userInfo.nickname = nickname; // 同时更新 nickname 字段
        wx.setStorageSync('userInfo', userInfo);

        // 更新页面显示
        that.setData({
          'userInfo.username': nickname,
          'userInfo.nickname': nickname,
          tempNickname: ''
        });

        wx.showToast({
          title: '昵称更新成功',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: res.msg || '昵称更新失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('更新昵称失败:', err);
      wx.showToast({
        title: '昵称更新失败，请重试',
        icon: 'none'
      });
    });
  },

  /**
   * 跳转到礼券兑换记录
   */
  navigateToRedemptionRecords: function () {
    // 只有当礼券数不为0时，才跳转到礼券兑换页面的兑换记录选项卡
    if (this.data.cardCount && this.data.cardCount > 0) {
      // 兑换记录选项卡的索引，假设是第二个选项卡 (index=1)
      const tabIndex = 1;
      wx.switchTab({
        url: '/pages/ucenter/redemption/redemption',
        success: function () {
          // 页面跳转成功后，发送自定义事件通知页面切换到指定选项卡
          wx.setStorageSync('redemptionActiveTab', tabIndex);
        }
      });
    }
  },

  /**
   * 清除本地缓存
   */
  clearStorage: function () {
    wx.showModal({
      title: '提示',
      content: '确定要清除缓存吗？清除后需要重新登录',
      success: (res) => {
        if (res.confirm) {
          wx.clearStorage({
            success: () => {
              wx.showToast({ icon: "success", title: '缓存已清除' });
              // 更新登录状态
              this.initData();
            },
            fail: (err) => {
              console.error('清除缓存失败', err);
              wx.showToast({ icon: "none", title: '清除缓存失败' });
            }
          });
        }
      }
    });
  },

  /**
   * 通用导航方法
   */
  navigateTo: function (e) {
    if (!this.data.hasLogin) {
      wx.navigateTo({
        url: '/pages/auth/login/login'
      });
      return;
    }
    let url = e.currentTarget.dataset.url;
    util.toPage(url)
  },

  /**
   * 跳转到订单列表
   */
  navigateToOrderList: function (e) {
    if (!this.data.hasLogin) {
      wx.navigateTo({
        url: '/pages/auth/login/login'
      });
      return;
    }

    let sts = e.currentTarget.dataset.sts || 0;
    wx.navigateTo({
      url: '/pages/ucenter/order/order?type=' + sts
    });
  },

  toAddressList: function () {
    wx.navigateTo({
      url: '/pages/delivery-address/delivery-address',
    })
  },

  /**
   * 跳转绑定手机号
   */
  bindPhoneNumber: function () {
    if (!this.data.hasLogin) {
      wx.navigateTo({
        url: '/pages/auth/login/login'
      });
      return;
    }

    this.setData({
      showBindPhoneModal: true
    });
  },

  /**
   * 关闭手机绑定弹窗
   */
  closeBindPhoneModal: function () {
    this.setData({
      showBindPhoneModal: false
    });
  },

  /**
   * 获取手机号
   */
  getPhoneNumber: function (e) {
    if (e.detail.errMsg !== 'getPhoneNumber:ok') {
      wx.showToast({
        title: '获取手机号失败',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '绑定中...'
    });

    // 获取到的加密信息
    const encryptedData = e.detail.encryptedData;
    const iv = e.detail.iv;

    // 请求后端解密并绑定手机号
    util.request(api.BindMobile, {
      encryptedData: encryptedData,
      iv: iv
    }, 'POST').then(res => {
      wx.hideLoading();
      if (res.success) {
        this.closeBindPhoneModal();

        // 更新用户信息
        let userInfo = wx.getStorageSync('userInfo');
        userInfo.mobile = res.data.mobile;
        wx.setStorageSync('userInfo', userInfo);

        this.setData({
          hasBindPhone: true,
          'userInfo.mobile': res.data.mobile
        });

        wx.showToast({
          title: '手机号绑定成功',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: res.msg || '绑定失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('绑定手机号异常', err);
      wx.showToast({
        title: '绑定失败，请重试',
        icon: 'none'
      });
    });
  },

  /**
   * 退出登录
   */
  logout: function () {
    const that = this;
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: function (res) {
        if (res.confirm) {
          wx.removeStorageSync('token');
          wx.removeStorageSync('userInfo');
          app.globalData.userInfo = {};
          app.globalData.token = '';
          that.initData()
        }
      }
    });
  },

  /**
   * 显示联系客服弹窗
   */
  showContactModal: function () {
    this.setData({
      showContactModal: true
    });
  },

  /**
   * 关闭联系客服弹窗
   */
  closeContactModal: function () {
    this.setData({
      showContactModal: false
    });
  },

  /**
   * 拨打客服电话
   */
  callPhone: function () {
    const phoneNumber = (api.CustomerService && api.CustomerService.phone) || this.data.customerService.phone;
    wx.makePhoneCall({
      phoneNumber: phoneNumber,
      success: () => {
        console.log('拨打电话成功');
        this.closeContactModal();
      },
      fail: (err) => {
        console.error('拨打电话失败', err);
        wx.showToast({
          title: '拨打失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 复制微信号
   */
  copyWechat: function () {
    const wechatId = (api.CustomerService && api.CustomerService.wechat) || this.data.customerService.wechat;
    wx.setClipboardData({
      data: wechatId,
      success: () => {
        wx.showToast({
          title: '微信号已复制',
          icon: 'success'
        });
        this.closeContactModal();
      },
      fail: (err) => {
        console.error('复制失败', err);
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 拨打投诉电话
   */
  callComplaintPhone: function () {
    const complaintPhone = '15332415685';
    wx.makePhoneCall({
      phoneNumber: complaintPhone,
      success: () => {
        console.log('拨打投诉电话成功');
      },
      fail: (err) => {
        console.error('拨打投诉电话失败', err);
        wx.showToast({
          title: '拨打失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 订阅消息
   */
  requestSubscribeMsg: function () {
    pay.requestOrderSubscribeMessage().then(res => {
      wx.showToast({
        title: '订阅成功',
        icon: 'success'
      });
    }).catch(err => {
      wx.showToast({
        title: '订阅失败',
        icon: 'none'
      });
    });
  },

  /**
   * 我的收藏跳转
   */
  myCollectionHandle: function () {
    var url = '/pages/prod-classify/prod-classify?sts=5';
    var id = 0;
    var title = "我的收藏商品";
    if (id) {
      url += "&tagid=" + id + "&title=" + title;
    }
    wx.navigateTo({
      url: url
    })
  },

  /**
   * 显示推广二维码弹窗
   */
  showPromotionQrCode: function () {
    if (!this.data.hasLogin) {
      wx.navigateTo({
        url: '/pages/auth/login/login'
      });
      return;
    }

    this.setData({
      showPromotionQrModal: true
    });

    // 生成推广二维码
    this.generatePromotionQrCode();
  },

  /**
   * 关闭推广二维码弹窗
   */
  closePromotionQrModal: function () {
    this.setData({
      showPromotionQrModal: false
    });
  },

  /**
   * 生成推广二维码
   */
  generatePromotionQrCode: function () {
    const that = this;
    wx.showLoading({
      title: '生成中...'
    });

    util.request(api.GeneratePromotionQrCode).then(res => {
      wx.hideLoading();
      if (res.success) {
        that.setData({
          promotionQrCode: res.data
        });
      } else {
        wx.showToast({
          title: res.msg || '生成二维码失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('生成推广二维码失败:', err);
      wx.showToast({
        title: '生成二维码失败，请重试',
        icon: 'none'
      });
    });
  },

  /**
   * 保存二维码到相册
   */
  saveQrCodeToAlbum: function () {
    const that = this;

    if (!this.data.promotionQrCode.qrCodeUrl) {
      wx.showToast({
        title: '二维码还未生成完成',
        icon: 'none'
      });
      return;
    }

    // 先获取保存图片到相册的权限
    wx.getSetting({
      success: (res) => {
        if (!res.authSetting['scope.writePhotosAlbum']) {
          wx.authorize({
            scope: 'scope.writePhotosAlbum',
            success: () => {
              that.doSaveQrCode();
            },
            fail: () => {
              wx.showModal({
                title: '提示',
                content: '需要您授权保存图片到相册',
                showCancel: false
              });
            }
          });
        } else {
          that.doSaveQrCode();
        }
      }
    });
  },

  /**
   * 执行保存二维码操作
   */
  doSaveQrCode: function () {
    const qrCodeUrl = this.data.promotionQrCode.qrCodeUrl;

    // 如果是base64格式，需要先转换为临时文件
    if (qrCodeUrl.startsWith('data:image')) {
      // 将base64转换为临时文件
      const base64Data = qrCodeUrl.split(',')[1];
      const filePath = wx.env.USER_DATA_PATH + '/promotion_qr_' + Date.now() + '.png';

      wx.getFileSystemManager().writeFile({
        filePath: filePath,
        data: base64Data,
        encoding: 'base64',
        success: () => {
          wx.saveImageToPhotosAlbum({
            filePath: filePath,
            success: () => {
              wx.showToast({
                title: '保存成功',
                icon: 'success'
              });
            },
            fail: (err) => {
              console.error('保存图片失败:', err);
              wx.showToast({
                title: '保存失败',
                icon: 'none'
              });
            }
          });
        },
        fail: (err) => {
          console.error('写入文件失败:', err);
          wx.showToast({
            title: '保存失败',
            icon: 'none'
          });
        }
      });
    } else {
      // 直接保存网络图片
      wx.saveImageToPhotosAlbum({
        filePath: qrCodeUrl,
        success: () => {
          wx.showToast({
            title: '保存成功',
            icon: 'success'
          });
        },
        fail: (err) => {
          console.error('保存图片失败:', err);
          wx.showToast({
            title: '保存失败',
            icon: 'none'
          });
        }
      });
    }
  },

  /**
   * 分享二维码
   */
  shareQrCode: function () {
    const that = this;

    if (!this.data.promotionQrCode.qrCodeUrl) {
      wx.showToast({
        title: '二维码还未生成完成',
        icon: 'none'
      });
      return;
    }

    // 关闭弹窗，触发分享
    this.closePromotionQrModal();

    // 延迟一下再提示分享
    setTimeout(() => {
      wx.showModal({
        title: '分享推广二维码',
        content: '点击右上角菜单选择"转发"即可分享给好友',
        showCancel: false,
        confirmText: '知道了'
      });
    }, 300);
  },

  /**
   * 跳转到推广明细页面
   */
  goToPromotionDetail: function () {
    wx.navigateTo({
      url: '/pages/ucenter/promotion-detail/promotion-detail'
    });
  },

  /**
   * 跳转到我的收益页面
   */
  goToEarnings: function () {
    wx.navigateTo({
      url: '/pages/ucenter/earnings/earnings'
    });
  },

  /**
   * 跳转到推广海报页面
   */
  goToPromotionPoster: function () {
    if (!this.data.hasLogin) {
      wx.navigateTo({
        url: '/pages/auth/login/login'
      });
      return;
    }

    wx.navigateTo({
      url: '/pages/ucenter/promotion-poster/promotion-poster'
    });
  },

  /**
   * 跳转到团队管理页面
   */
  goToTeamManagement: function () {
    // 检查用户权限
    const userInfo = this.data.userInfo;
    if (userInfo && (userInfo.userLevelId == 1 || userInfo.userLevelId == 2)) {
      wx.navigateTo({
        url: '/pages/ucenter/team/team'
      });
    } else {
      wx.showToast({
        title: '您没有团队管理权限',
        icon: 'none'
      });
    }
  },

  /**
   * 分享给好友
   */
  shareToFriend: function () {
    if (!this.data.hasLogin) {
      wx.showToast({
        title: '请先登录',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 显示分享提示
    wx.showModal({
      title: '分享给好友',
      content: '点击右上角菜单选择"转发"，好友通过您的分享链接进入将直接跳转到登录页面，登录成功后您将获得推广奖励！',
      showCancel: false,
      confirmText: '知道了',
      success: (res) => {
        if (res.confirm) {
          // 可以在这里添加一些统计或其他逻辑
          console.log('用户确认了分享提示');
        }
      }
    });
  },
})
