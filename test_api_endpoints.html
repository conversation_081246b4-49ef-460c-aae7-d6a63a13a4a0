<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单统计API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 3px;
            cursor: pointer;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .result {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin-top: 10px;
            border-radius: 3px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
    </style>
</head>
<body>
    <h1>订单统计API测试工具</h1>
    
    <div class="test-section">
        <h2>基础统计接口测试</h2>
        <button class="test-button" onclick="testBasicAPI()">测试基础数据接口</button>
        <button class="test-button" onclick="testTrendAPI()">测试趋势数据接口</button>
        <button class="test-button" onclick="testChannelAPI()">测试来源分析接口</button>
        <button class="test-button" onclick="testTypeAPI()">测试类型分析接口</button>
        <div id="basic-result" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>新增接口测试</h2>
        <button class="test-button" onclick="testRealtimeAPI()">测试实时数据接口</button>
        <button class="test-button" onclick="testStatusAPI()">测试订单状态接口</button>
        <button class="test-button" onclick="testHotProductsAPI()">测试热销商品接口</button>
        <div id="new-result" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>批量测试</h2>
        <button class="test-button" onclick="testAllAPIs()">测试所有接口</button>
        <div id="all-result" class="result" style="display: none;"></div>
    </div>

    <script>
        const BASE_URL = 'http://localhost:8080/adminapi/statistic';
        const TEST_DATE_RANGE = '2025/01/01-2025/01/07';

        // 模拟token，实际使用时需要从localStorage或其他地方获取
        const TOKEN = 'your-auth-token-here';

        async function makeRequest(url, resultElementId) {
            const resultElement = document.getElementById(resultElementId);
            resultElement.style.display = 'block';
            resultElement.className = 'result';
            resultElement.textContent = '请求中...';

            try {
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${TOKEN}`,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    resultElement.className = 'result success';
                    resultElement.textContent = `✅ 请求成功\n\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultElement.className = 'result error';
                    resultElement.textContent = `❌ 请求失败\n状态码: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultElement.className = 'result error';
                resultElement.textContent = `❌ 网络错误\n${error.message}`;
            }
        }

        // 基础接口测试
        function testBasicAPI() {
            makeRequest(`${BASE_URL}/order/get_basic?data=${TEST_DATE_RANGE}`, 'basic-result');
        }

        function testTrendAPI() {
            makeRequest(`${BASE_URL}/order/get_trend?data=${TEST_DATE_RANGE}`, 'basic-result');
        }

        function testChannelAPI() {
            makeRequest(`${BASE_URL}/order/get_channel?data=${TEST_DATE_RANGE}`, 'basic-result');
        }

        function testTypeAPI() {
            makeRequest(`${BASE_URL}/order/get_type?data=${TEST_DATE_RANGE}`, 'basic-result');
        }

        // 新增接口测试
        function testRealtimeAPI() {
            makeRequest(`${BASE_URL}/order/get_realtime`, 'new-result');
        }

        function testStatusAPI() {
            makeRequest(`${BASE_URL}/order/get_status?data=${TEST_DATE_RANGE}`, 'new-result');
        }

        function testHotProductsAPI() {
            makeRequest(`${BASE_URL}/order/get_hot_products?limit=10`, 'new-result');
        }

        // 批量测试
        async function testAllAPIs() {
            const resultElement = document.getElementById('all-result');
            resultElement.style.display = 'block';
            resultElement.className = 'result';
            resultElement.textContent = '开始批量测试...\n\n';

            const apis = [
                { name: '基础数据', url: `${BASE_URL}/order/get_basic?data=${TEST_DATE_RANGE}` },
                { name: '趋势数据', url: `${BASE_URL}/order/get_trend?data=${TEST_DATE_RANGE}` },
                { name: '来源分析', url: `${BASE_URL}/order/get_channel?data=${TEST_DATE_RANGE}` },
                { name: '类型分析', url: `${BASE_URL}/order/get_type?data=${TEST_DATE_RANGE}` },
                { name: '实时数据', url: `${BASE_URL}/order/get_realtime` },
                { name: '订单状态', url: `${BASE_URL}/order/get_status?data=${TEST_DATE_RANGE}` },
                { name: '热销商品', url: `${BASE_URL}/order/get_hot_products?limit=10` }
            ];

            let results = [];

            for (const api of apis) {
                try {
                    const response = await fetch(api.url, {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${TOKEN}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    const data = await response.json();
                    
                    if (response.ok) {
                        results.push(`✅ ${api.name}: 成功`);
                    } else {
                        results.push(`❌ ${api.name}: 失败 (${response.status})`);
                    }
                } catch (error) {
                    results.push(`❌ ${api.name}: 网络错误 (${error.message})`);
                }

                // 更新进度
                resultElement.textContent = `测试进度: ${results.length}/${apis.length}\n\n${results.join('\n')}`;
            }

            resultElement.textContent += '\n\n测试完成！';
        }

        // 页面加载时的提示
        window.onload = function() {
            console.log('订单统计API测试工具已加载');
            console.log('请确保后端服务已启动，并且TOKEN已正确配置');
        };
    </script>
</body>
</html>
