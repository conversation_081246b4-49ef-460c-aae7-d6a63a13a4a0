# CategoryDialog 分类对话框组件

## 概述

CategoryDialog 是一个用于图片分类新增与编辑的弹窗组件，提供了完整的分类管理功能。

## 功能特性

- ✅ 新增分类（支持根分类和子分类）
- ✅ 编辑分类信息
- ✅ 级联选择器选择上级分类
- ✅ 表单验证
- ✅ 响应式设计
- ✅ 自动刷新分类树

## 使用方法

### 1. 导入组件

```javascript
import CategoryDialog from '@/components/CategoryDialog'

export default {
  components: {
    CategoryDialog
  }
}
```

### 2. 在模板中使用

```vue
<template>
  <div>
    <!-- 其他内容 -->
    
    <!-- 分类对话框 -->
    <CategoryDialog ref="categoryDialog" @success="handleSuccess" />
  </div>
</template>
```

### 3. 调用方法

```javascript
methods: {
  // 新增根分类
  addRootCategory() {
    this.$refs.categoryDialog.openAdd(0)
  },
  
  // 新增子分类
  addChildCategory(parentId) {
    this.$refs.categoryDialog.openAdd(parentId)
  },
  
  // 编辑分类
  editCategory(category) {
    this.$refs.categoryDialog.openEdit(category)
  },
  
  // 处理成功回调
  handleSuccess() {
    this.$message.success('操作成功！')
    // 刷新分类列表等操作
  }
}
```

## API

### 方法

| 方法名 | 说明 | 参数 |
|--------|------|------|
| openAdd | 打开新增对话框 | parentId: number (父分类ID，0表示根分类) |
| openEdit | 打开编辑对话框 | category: object (分类对象) |

### 事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| success | 操作成功时触发 | - |

### 分类对象结构

```javascript
{
  id: 1,                    // 分类ID
  parentId: 0,              // 父分类ID（0表示根分类）
  title: '分类名称',         // 分类名称
  description: '分类描述',   // 分类描述（可选）
  sortOrder: 0              // 排序权重
}
```

## 表单字段

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| 上级分类 | 级联选择器 | 否 | 选择父分类，可为空表示根分类 |
| 分类名称 | 文本输入 | 是 | 1-50个字符 |
| 分类描述 | 多行文本 | 否 | 最多200个字符 |
| 排序权重 | 数字输入 | 否 | 0-9999，数值越大排序越靠前 |

## 验证规则

- 分类名称：必填，长度1-50个字符
- 分类描述：可选，最多200个字符
- 排序权重：可选，必须为数字，范围0-9999

## 样式定制

组件使用了 Element UI 的样式，可以通过以下方式进行定制：

```scss
// 自定义对话框宽度
.el-dialog {
  width: 600px;
}

// 自定义表单项间距
.el-form-item {
  margin-bottom: 25px;
}
```

## 依赖

- Element UI
- Vue 2.x
- 后端API接口：
  - `POST /file/category` - 创建分类
  - `PUT /file/category` - 更新分类
  - `GET /file/category` - 获取分类列表

## 注意事项

1. 确保后端API接口已正确实现
2. 组件依赖Element UI，请确保已正确引入
3. 级联选择器的数据来源于分类列表API
4. 操作成功后会自动关闭对话框并触发success事件

## 示例

完整的使用示例可以参考 `web/src/views/test/CategoryTest.vue` 文件。
