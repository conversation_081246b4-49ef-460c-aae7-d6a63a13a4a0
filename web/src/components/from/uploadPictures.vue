<template>
  <div class="uploader-box">
    <div class="upload-box">
      <el-upload
        class="avatar-uploader"
        action="#"
        :show-file-list="false"
        :http-request="customUpload"
        :before-upload="beforeAvatarUpload"
        :on-error="handleError"
      >
        <div class="upload-btn">
          <i class="el-icon-plus avatar-uploader-icon"></i>
          <span class="upload-text">点击上传图片</span>
        </div>
      </el-upload>
    </div>
    <div class="images-box">
      <div class="images-list">
        <div class="image-item" v-for="(item, index) in imageList" :key="index" @click="handleSelect(item)">
          <img :src="formatImageUrl(item.src)" alt="图片" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { uploadFileApi } from '@/api/product';
import { formatImageUrl, isPicUpload } from '@/utils';

export default {
  name: 'uploadPictures',
  props: {
    isChoice: {
      type: Boolean,
      default: false
    },
    gridBtn: {
      type: Object,
      default: () => {}
    },
    gridPic: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      imageList: []
    };
  },
  created() {
    this.getImageList();
  },
  methods: {
    formatImageUrl,
    // Get image list from uploads directory
    getImageList() {
      // In a real implementation, you would fetch the list of uploaded images
      // For now, we'll just use the images from the uploads directory
      this.imageList = [];
    },
    // Custom upload method
    customUpload(options) {
      const formData = new FormData();
      formData.append('file', options.file);

      this.$message.info('正在上传...');

      uploadFileApi(formData)
        .then(res => {
          if (res.success) {
            this.imageList.unshift({
              src: res.data.src,
              filename: res.data.filename,
              id: res.data.id
            });
            this.$message.success('上传成功');
            options.onSuccess && options.onSuccess(res);
          } else {
            this.$message.error(res.message || '上传失败');
            options.onError && options.onError(res);
          }
        })
        .catch(err => {
          console.error('Upload error:', err);
          this.$message.error('上传失败：' + (err.message || '未知错误'));
          options.onError && options.onError(err);
        });
    },
    // Handle upload error
    handleError(err) {
      this.$message.error('上传失败：' + err);
    },
    // Validate file before upload
    beforeAvatarUpload(file) {
      const isImage = isPicUpload(file);
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!');
      }
      return isImage && isLt2M;
    },
    // Handle image selection
    handleSelect(item) {
      if (this.isChoice) {
        this.$emit('getPicD', item);
      } else {
        this.$emit('getPic', item);
      }
    },
    // Handle successful upload
    handleAvatarSuccess(res) {
      if (res.success) {
        this.imageList.unshift({
          src: res.data.src,
          filename: res.data.filename,
          id: res.data.id
        });
        this.$message.success('上传成功');
      } else {
        this.$message.error(res.message || '上传失败');
      }
    }
  }
};
</script>

<style scoped>
.uploader-box {
  display: flex;
  flex-direction: column;
  height: 500px;
}
.upload-box {
  padding: 20px;
  border-bottom: 1px solid #eee;
}
.avatar-uploader {
  display: flex;
  justify-content: center;
}
.upload-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 150px;
  height: 150px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
}
.upload-btn:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
}
.upload-text {
  margin-top: 10px;
  font-size: 14px;
  color: #606266;
}
.images-box {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}
.images-list {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 15px;
}
.image-item {
  height: 150px;
  border: 1px solid #eee;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
}
.image-item:hover {
  border-color: #409EFF;
}
.image-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
</style>
