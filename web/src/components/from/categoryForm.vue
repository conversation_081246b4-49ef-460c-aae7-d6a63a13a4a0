<template>
  <div>
    <el-dialog :visible.sync="dialogVisible" :title="isEdit ? '编辑分类' : '添加分类'" width="500px" @closed="closeDialog">
      <el-form :model="formData" :rules="rules" ref="categoryForm" label-width="100px">
        <el-form-item label="父级分类：" prop="parentId">
          <el-select v-model="formData.parentId" placeholder="请选择父级分类" class="form-width">
            <el-option :value="0" label="顶级分类"></el-option>
            <el-option v-for="item in parentCategories" :key="item.id" :value="item.id" :label="item.name"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="分类名称：" prop="name">
          <el-input v-model="formData.name" placeholder="请输入分类名称" class="form-width"></el-input>
        </el-form-item>
        <el-form-item label="排序：" prop="sortOrder">
          <el-input-number v-model="formData.sortOrder" :min="0" :max="999" class="form-width"></el-input-number>
        </el-form-item>
        <el-form-item label="关键词：" prop="keywords">
          <el-input v-model="formData.keywords" placeholder="请输入关键词" class="form-width"></el-input>
        </el-form-item>
        <el-form-item label="前端描述：" prop="frontDesc">
          <el-input v-model="formData.frontDesc" placeholder="请输入前端描述" class="form-width"></el-input>
        </el-form-item>
        <el-form-item label="图标：" prop="iconUrl">
          <el-upload
            class="avatar-uploader"
            action="/adminapi/file/upload"
            :show-file-list="false"
            :on-success="handleIconSuccess"
            :before-upload="beforeUpload"
            name="file"
          >
            <img
              v-if="formData.iconUrl"
              :src="formatImageUrl(formData.iconUrl)"
              class="avatar"
              :key="formData.iconUrl"
            />
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
          <div v-if="formData.iconUrl" class="image-url">
            图片路径: {{ formData.iconUrl }}
          </div>
        </el-form-item>
        <el-form-item label="是否显示：" prop="isShow">
          <el-switch v-model="formData.isShow" :active-value="true" :inactive-value="false"></el-switch>
        </el-form-item>
        <el-form-item label="首页显示：" prop="showIndex">
          <el-switch v-model="formData.showIndex" :active-value="true" :inactive-value="false"></el-switch>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" v-db-click @click="submitForm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { categoryUpdateApi, categorySaveApi, treeListApi } from '@/api/product';
import { formatImageUrl } from "@/utils";

export default {
  name: 'CategoryForm',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    category: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: false,
      formData: {
        id: null,
        name: '',
        keywords: '',
        frontDesc: '',
        parentId: 0,
        sortOrder: 0,
        showIndex: false,
        isShow: true,
        iconUrl: ''
      },
      parentCategories: [],
      rules: {
        name: [
          { required: true, message: '请输入分类名称', trigger: 'blur' }
        ]
      }
    };
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val) {
        this.loadParentCategories();
        if (this.isEdit && this.category) {
          this.initFormData();
        }
      }
    },
    'formData.iconUrl': {
      handler(newVal) {
        if (newVal) {
          // Force DOM update when iconUrl changes
          this.$nextTick(() => {
            this.$forceUpdate();
          });
        }
      },
      immediate: true
    }
  },
  methods: {
    formatImageUrl,
    initFormData() {
      // Copy category data to form
      this.formData = { ...this.category };
    },
    loadParentCategories() {
      treeListApi(0).then(res => {
        if (res.data) {
          this.parentCategories = res.data.filter(item => {
            // Filter out the current category if editing (to prevent circular reference)
            return !this.isEdit || item.id !== this.formData.id;
          });
        }
      }).catch(err => {
        this.$message.error(err.msg || '获取父级分类失败');
      });
    },
    closeDialog() {
      this.$emit('update:visible', false);
      this.resetForm();
    },
    resetForm() {
      if (this.$refs.categoryForm) {
        this.$refs.categoryForm.resetFields();
      }
      this.formData = {
        id: null,
        name: '',
        keywords: '',
        frontDesc: '',
        parentId: 0,
        sortOrder: 0,
        showIndex: false,
        isShow: true,
        iconUrl: ''
      };
    },
    beforeUpload(file) {
      const isImage = file.type.startsWith('image/');
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isImage) {
        this.$message.error('上传图标只能是图片格式!');
      }
      if (!isLt2M) {
        this.$message.error('上传图标大小不能超过 2MB!');
      }
      return isImage && isLt2M;
    },
    handleIconSuccess(res, file) {
      if (res.code == 200) {
        console.log('Upload success, image URL:', res.data.src);
        this.formData.iconUrl = res.data.src;
        // Force the component to re-render the image
        this.$nextTick(() => {
          this.$forceUpdate();
        });
      } else {
        this.$message.error(res.msg || '上传失败');
      }
    },
    submitForm() {
      this.$refs.categoryForm.validate(valid => {
        if (valid) {
          const api = this.isEdit ? categoryUpdateApi : categorySaveApi;
          api(this.formData).then(res => {
            this.$message.success(this.isEdit ? '更新成功' : '添加成功');
            this.dialogVisible = false;
            this.$emit('success');
          }).catch(err => {
            this.$message.error(err.msg || (this.isEdit ? '更新失败' : '添加失败'));
          });
        }
      });
    }
  }
};
</script>

<style scoped>
.form-width {
  width: 100%;
}
.avatar-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 100px;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.avatar-uploader:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}
.avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.image-url {
  margin-top: 5px;
  font-size: 12px;
  color: #909399;
  word-break: break-all;
}
</style>
