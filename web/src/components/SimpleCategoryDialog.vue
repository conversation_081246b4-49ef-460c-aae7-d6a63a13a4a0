<template>
  <el-dialog
    title="简单分类对话框测试"
    :visible.sync="dialogVisible"
    width="400px"
    :modal="true"
    :modal-append-to-body="true"
    :append-to-body="true"
    :close-on-click-modal="false"
  >
    <el-form ref="form" :model="form" label-width="80px">
      <el-form-item label="分类名称">
        <el-input v-model="form.title" placeholder="请输入分类名称" />
      </el-form-item>
      <el-form-item label="描述">
        <el-input v-model="form.description" placeholder="请输入描述" />
      </el-form-item>
    </el-form>

    <div slot="footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'SimpleCategoryDialog',
  data() {
    return {
      dialogVisible: false,
      form: {
        title: '',
        description: ''
      }
    }
  },
  methods: {
    open() {
      console.log('SimpleCategoryDialog open called')
      this.form.title = ''
      this.form.description = ''
      this.dialogVisible = true
      console.log('Form data:', this.form)
    },

    handleSubmit() {
      console.log('Submit form:', this.form)
      this.$message.success('提交成功: ' + this.form.title)
      this.dialogVisible = false
    }
  }
}
</script>
