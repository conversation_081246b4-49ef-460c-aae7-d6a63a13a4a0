<template>
  <div class="category-manager">
    <!-- 分类操作栏 -->
    <div class="category-toolbar">
      <el-button type="primary" size="small" icon="el-icon-plus" @click="showAddDialog">
        新增分类
      </el-button>
      <el-button 
        type="danger" 
        size="small" 
        icon="el-icon-delete" 
        :disabled="selectedCategories.length === 0"
        @click="batchDelete"
      >
        批量删除
      </el-button>
      <el-input
        v-model="searchKeyword"
        placeholder="搜索分类"
        size="small"
        style="width: 200px; margin-left: 10px;"
        @input="searchCategories"
        clearable
      >
        <i slot="suffix" class="el-icon-search"></i>
      </el-input>
    </div>

    <!-- 分类树 -->
    <div class="category-tree-container">
      <el-tree
        ref="categoryTree"
        :data="categoryTreeData"
        :props="treeProps"
        node-key="id"
        :default-expand-all="true"
        :expand-on-click-node="false"
        :check-strictly="true"
        show-checkbox
        draggable
        :allow-drop="allowDrop"
        :allow-drag="allowDrag"
        @node-drop="handleNodeDrop"
        @check-change="handleCheckChange"
        @node-click="handleNodeClick"
      >
        <span class="custom-tree-node" slot-scope="{ node, data }">
          <span class="node-content">
            <i :class="getNodeIcon(data)" class="node-icon"></i>
            <span class="node-title">{{ data.title }}</span>
            <span class="node-count" v-if="data.fileCount > 0">({{ data.fileCount }})</span>
          </span>
          <span class="node-actions">
            <el-tooltip content="新增子分类" placement="top">
              <el-button
                type="text"
                size="mini"
                icon="el-icon-plus"
                @click.stop="showAddDialog(data)"
              ></el-button>
            </el-tooltip>
            <el-tooltip content="编辑分类" placement="top">
              <el-button
                type="text"
                size="mini"
                icon="el-icon-edit"
                @click.stop="showEditDialog(data)"
              ></el-button>
            </el-tooltip>
            <el-tooltip content="删除分类" placement="top">
              <el-button
                type="text"
                size="mini"
                icon="el-icon-delete"
                @click.stop="deleteCategory(data)"
              ></el-button>
            </el-tooltip>
          </span>
        </span>
      </el-tree>
    </div>

    <!-- 分类表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="500px"
      @close="resetForm"
    >
      <el-form
        ref="categoryForm"
        :model="categoryForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="分类名称" prop="title">
          <el-input
            v-model="categoryForm.title"
            placeholder="请输入分类名称"
            maxlength="50"
            show-word-limit
          ></el-input>
        </el-form-item>
        
        <el-form-item label="父级分类" prop="parentId">
          <el-cascader
            v-model="categoryForm.parentId"
            :options="categoryOptions"
            :props="cascaderProps"
            placeholder="请选择父级分类"
            clearable
            style="width: 100%"
          ></el-cascader>
        </el-form-item>
        
        <el-form-item label="排序" prop="sortOrder">
          <el-input-number
            v-model="categoryForm.sortOrder"
            :min="0"
            :max="9999"
            placeholder="排序值"
            style="width: 100%"
          ></el-input-number>
        </el-form-item>
        
        <el-form-item label="分类描述" prop="description">
          <el-input
            v-model="categoryForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入分类描述"
            maxlength="200"
            show-word-limit
          ></el-input>
        </el-form-item>
        
        <el-form-item label="分类图标" prop="icon">
          <el-input
            v-model="categoryForm.icon"
            placeholder="请输入图标类名，如：el-icon-folder"
          ></el-input>
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { 
  getCategoryListApi, 
  createApi, 
  categoryEditApi,
  categoryDelApi 
} from '@/api/uploadPictures';

export default {
  name: 'CategoryManager',
  props: {
    // 是否显示文件数量
    showFileCount: {
      type: Boolean,
      default: true
    },
    // 是否允许拖拽
    allowDrag: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      categoryTreeData: [],
      categoryOptions: [],
      selectedCategories: [],
      searchKeyword: '',
      dialogVisible: false,
      dialogTitle: '',
      isEdit: false,
      submitting: false,
      categoryForm: {
        id: null,
        title: '',
        parentId: 0,
        sortOrder: 0,
        description: '',
        icon: ''
      },
      treeProps: {
        children: 'children',
        label: 'title'
      },
      cascaderProps: {
        value: 'id',
        label: 'title',
        children: 'children',
        checkStrictly: true,
        emitPath: false
      },
      formRules: {
        title: [
          { required: true, message: '请输入分类名称', trigger: 'blur' },
          { min: 1, max: 50, message: '分类名称长度在 1 到 50 个字符', trigger: 'blur' }
        ],
        sortOrder: [
          { type: 'number', message: '排序必须为数字值', trigger: 'blur' }
        ],
        description: [
          { max: 200, message: '描述长度不能超过 200 个字符', trigger: 'blur' }
        ]
      }
    };
  },
  computed: {
    allowDrop() {
      return this.allowDrag;
    }
  },
  mounted() {
    this.loadCategoryTree();
  },
  methods: {
    // 加载分类树
    async loadCategoryTree() {
      try {
        const response = await getCategoryListApi();
        this.categoryTreeData = this.buildTree(response.data);
        this.categoryOptions = this.buildCascaderOptions(response.data);
      } catch (error) {
        this.$message.error('加载分类失败：' + error.message);
      }
    },

    // 构建树形结构
    buildTree(categories) {
      const map = {};
      const roots = [];
      
      // 创建映射
      categories.forEach(category => {
        map[category.id] = { ...category, children: [] };
      });
      
      // 构建树
      categories.forEach(category => {
        if (category.parentId === 0) {
          roots.push(map[category.id]);
        } else if (map[category.parentId]) {
          map[category.parentId].children.push(map[category.id]);
        }
      });
      
      return roots;
    },

    // 构建级联选择器选项
    buildCascaderOptions(categories) {
      const options = [{ id: 0, title: '根分类', children: [] }];
      const tree = this.buildTree(categories);
      options[0].children = tree;
      return options;
    },

    // 获取节点图标
    getNodeIcon(data) {
      if (data.icon) {
        return data.icon;
      }
      return data.children && data.children.length > 0 ? 'el-icon-folder' : 'el-icon-folder-opened';
    },

    // 显示新增对话框
    showAddDialog(parentCategory = null) {
      this.isEdit = false;
      this.dialogTitle = '新增分类';
      this.categoryForm = {
        id: null,
        title: '',
        parentId: parentCategory ? parentCategory.id : 0,
        sortOrder: 0,
        description: '',
        icon: ''
      };
      this.dialogVisible = true;
    },

    // 显示编辑对话框
    async showEditDialog(category) {
      this.isEdit = true;
      this.dialogTitle = '编辑分类';
      
      try {
        const response = await categoryEditApi(category.id);
        this.categoryForm = { ...response.data };
        this.dialogVisible = true;
      } catch (error) {
        this.$message.error('获取分类信息失败：' + error.message);
      }
    },

    // 提交表单
    async submitForm() {
      try {
        await this.$refs.categoryForm.validate();
        this.submitting = true;
        
        if (this.isEdit) {
          // 更新分类
          await this.updateCategory();
        } else {
          // 创建分类
          await this.createCategory();
        }
        
        this.dialogVisible = false;
        this.loadCategoryTree();
        this.$message.success(this.isEdit ? '更新成功' : '创建成功');
      } catch (error) {
        if (error.message) {
          this.$message.error(error.message);
        }
      } finally {
        this.submitting = false;
      }
    },

    // 创建分类
    async createCategory() {
      // 这里需要调用实际的创建API
      // 暂时使用现有的API结构
      const response = await createApi({ id: this.categoryForm.parentId });
      // 实际应该是 POST 请求创建分类
    },

    // 更新分类
    async updateCategory() {
      // 这里需要调用实际的更新API
      // 暂时使用现有的API结构
    },

    // 删除分类
    async deleteCategory(category) {
      try {
        await this.$confirm(`确定要删除分类"${category.title}"吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });
        
        await categoryDelApi(category.id);
        this.$message.success('删除成功');
        this.loadCategoryTree();
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败：' + error.message);
        }
      }
    },

    // 批量删除
    async batchDelete() {
      if (this.selectedCategories.length === 0) {
        this.$message.warning('请选择要删除的分类');
        return;
      }
      
      try {
        await this.$confirm(`确定要删除选中的 ${this.selectedCategories.length} 个分类吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });
        
        // 这里需要调用批量删除API
        for (const category of this.selectedCategories) {
          await categoryDelApi(category.id);
        }
        
        this.$message.success('批量删除成功');
        this.selectedCategories = [];
        this.loadCategoryTree();
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('批量删除失败：' + error.message);
        }
      }
    },

    // 搜索分类
    searchCategories() {
      if (!this.searchKeyword.trim()) {
        this.loadCategoryTree();
        return;
      }
      
      // 实现搜索逻辑
      // 这里需要调用搜索API
    },

    // 处理节点拖拽
    async handleNodeDrop(draggingNode, dropNode, dropType) {
      if (dropType === 'inner') {
        // 移动到目标节点内部
        try {
          // 调用移动API
          // await moveCategory(draggingNode.data.id, dropNode.data.id);
          this.$message.success('移动成功');
          this.loadCategoryTree();
        } catch (error) {
          this.$message.error('移动失败：' + error.message);
          this.loadCategoryTree(); // 重新加载以恢复原状态
        }
      }
    },

    // 处理复选框变化
    handleCheckChange() {
      this.selectedCategories = this.$refs.categoryTree.getCheckedNodes();
    },

    // 处理节点点击
    handleNodeClick(data) {
      this.$emit('category-selected', data);
    },

    // 重置表单
    resetForm() {
      if (this.$refs.categoryForm) {
        this.$refs.categoryForm.resetFields();
      }
    }
  }
};
</script>

<style scoped lang="scss">
.category-manager {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.category-toolbar {
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 10px;
}

.category-tree-container {
  flex: 1;
  overflow-y: auto;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.node-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.node-icon {
  margin-right: 5px;
  color: #409eff;
}

.node-title {
  margin-right: 5px;
}

.node-count {
  color: #909399;
  font-size: 12px;
}

.node-actions {
  display: none;
}

.custom-tree-node:hover .node-actions {
  display: block;
}

.dialog-footer {
  text-align: right;
}
</style>
