{"name": "from-crmeb-admin", "version": "5.6.0", "author": "sugar1569<<EMAIL>>", "private": false, "scripts": {"serve": "node src/libs/start.js && vue-cli-service serve --open --mode=dev", "dev": "node src/libs/start.js && vue-cli-service serve --open --mode=dev", "build": "vue-cli-service build --mode=production", "eslint:comment": "使用 ESLint 检查并自动修复 src 目录下所有扩展名为 .js 和 .vue 的文件", "eslint": "eslint --ext .js,.vue,.ts --ignore-path .gitignore --fix src", "prettier:comment": "自动格式化当前目录下的所有文件", "prettier": "prettier --write ."}, "dependencies": {"@babel/polyfill": "^7.12.1", "@babel/runtime": "^7.2.0", "@better-scroll/core": "^2.0.5", "@form-create/element-ui": "^2.5.31", "async-validator": "^3.4.0", "awe-dnd": "^0.3.4", "better-scroll": "^1.15.2", "clipboard": "^2.0.0", "codemirror": "^5.38.0", "core-js": "^3.32.2", "cos-js-sdk-v5": "^0.5.26", "countup": "^1.8.2", "cropperjs": "^1.2.2", "crypto-js": "^4.1.1", "dayjs": "^1.7.7", "echarts": "^4.8.0", "editor": "^1.0.0", "element-ui": "2.15.6", "emoji-awesome": "0.0.2", "exceljs": "^4.3.0", "file-saver": "^2.0.5", "html2canvas": "^1.0.0-alpha.12", "js-cookie": "^2.2.0", "js-md5": "^0.7.3", "lodash": "^4.17.10", "moment": "^2.29.1", "monaco-editor": "^0.28.1", "monaco-editor-webpack-plugin": "^4.2.0", "oss": "0.0.1", "print-js": "^1.6.0", "qiniu-js": "^2.5.5", "qrcodejs2": "0.0.2", "qs": "^6.6.0", "quill": "^1.3.6", "screenfull": "^5.0.2", "sortablejs": "^1.15.0", "swiper": "^5.4.5", "tree-table-vue": "^1.1.0", "uglifyjs-webpack-plugin": "^2.2.0", "v-org-tree": "^1.0.6", "v-viewer": "^1.5.1", "vue": "^2.5.10", "vue-awesome-swiper": "^4.1.1", "vue-clipboard2": "^0.3.3", "vue-codemirror": "^4.0.6", "vue-cropper": "^0.5.11", "vue-happy-scroll": "^2.1.1", "vue-i18n": "^7.8.0", "vue-pickers": "^2.5.3", "vue-puzzle-vcode": "^1.1.9", "vue-router": "^3.0.1", "vue-tree-list": "^1.5.0", "vue-ydui": "^1.2.6", "vuedraggable": "^2.16.0", "vuescroll": "^4.16.1", "vuex": "^3.0.1", "vuex-persist": "^2.2.0", "vxe-table": "^3.4.15", "wangeditor": "^4.7.15", "xe-utils": "^3.5.4", "xlsx": "^0.13.5"}, "devDependencies": {"@babel/cli": "^7.21.0", "@babel/core": "^7.21.0", "@babel/node": "^7.20.7", "@babel/plugin-transform-runtime": "^7.2.0", "@babel/preset-env": "^7.20.2", "@vue/cli-plugin-babel": "^4.4.6", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-unit-jest": "^3.2.3", "@vue/cli-plugin-unit-mocha": "^3.0.1", "@vue/cli-service": "^3.0.1", "@vue/eslint-config-prettier": "^7.0.0", "@vue/eslint-config-standard": "^3.0.0-beta.10", "axios": "^0.18.1", "compression-webpack-plugin": "^6.1.1", "eslint": "^7.15.0", "eslint-loader": "^4.0.2", "eslint-plugin-cypress": "^2.0.1", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^7.2.0", "jsencrypt": "^3.3.2", "lint-staged": "^6.0.0", "prettier": "^2.5.1", "sass": "^1.69.5", "sass-loader": "^10.4.1", "script-loader": "^0.7.2", "svg-sprite-loader": "^3.8.0", "text-loader": "0.0.1", "vue-lazyload": "^1.3.3", "vue-template-compiler": "^2.5.13", "vue-waterfall-easy": "^2.4.4"}, "engines": {"node": ">14.0.0 < 18.0.0", "npm": ">=6"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.js": ["vue-cli-service lint", "git add"], "*.vue": ["vue-cli-service lint", "git add"]}}