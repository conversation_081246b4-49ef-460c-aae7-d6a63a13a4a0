<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>积分记录管理接口测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #333;
            margin-top: 0;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .result {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .input-group {
            margin-bottom: 10px;
        }
        .input-group label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        .input-group input {
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
            width: 200px;
        }
    </style>
</head>
<body>
    <h1>积分记录管理接口测试</h1>
    
    <div class="test-section">
        <h3>1. 获取积分记录列表</h3>
        <div class="input-group">
            <label>页码:</label>
            <input type="number" id="page" value="1" min="1">
        </div>
        <div class="input-group">
            <label>每页数量:</label>
            <input type="number" id="limit" value="20" min="1" max="100">
        </div>
        <div class="input-group">
            <label>时间范围:</label>
            <input type="text" id="time" placeholder="2024/01/01-2024/12/31">
        </div>
        <div class="input-group">
            <label>交易类型:</label>
            <input type="text" id="trading_type" placeholder="earn, use, refund">
        </div>
        <div class="input-group">
            <label>关键词:</label>
            <input type="text" id="keywords" placeholder="用户昵称或备注关键词">
        </div>
        <button class="test-button" onclick="testGetPointRecordList()">获取积分记录列表</button>
        <div id="result1" class="result"></div>
    </div>

    <div class="test-section">
        <h3>2. 修改积分记录备注</h3>
        <div class="input-group">
            <label>记录ID:</label>
            <input type="number" id="recordId" placeholder="积分记录ID">
        </div>
        <div class="input-group">
            <label>备注内容:</label>
            <input type="text" id="remark" placeholder="输入备注内容">
        </div>
        <button class="test-button" onclick="testUpdatePointRecordRemark()">修改备注</button>
        <div id="result2" class="result"></div>
    </div>

    <div class="test-section">
        <h3>3. 订单详情查看功能测试</h3>
        <div class="input-group">
            <label>订单ID:</label>
            <input type="number" id="testOrderId" placeholder="输入订单ID进行测试">
        </div>
        <button class="test-button" onclick="testOrderDetailView()">测试订单详情查看</button>
        <button class="test-button" onclick="testOrderLinkClick()">模拟点击关联订单</button>
        <div id="result4" class="result"></div>
    </div>

    <div class="test-section">
        <h3>4. 测试数据状态</h3>
        <button class="test-button" onclick="testGetStatus()">检查API状态</button>
        <button class="test-button" onclick="testSampleData()">获取示例数据</button>
        <div id="result3" class="result"></div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8080'; // 根据实际后端地址调整
        
        function displayResult(elementId, data) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(data, null, 2);
        }

        function displayError(elementId, error) {
            const element = document.getElementById(elementId);
            element.textContent = `错误: ${error.message || error}`;
            element.style.color = 'red';
        }

        async function testGetPointRecordList() {
            try {
                const page = document.getElementById('page').value;
                const limit = document.getElementById('limit').value;
                const time = document.getElementById('time').value;
                const trading_type = document.getElementById('trading_type').value;
                const keywords = document.getElementById('keywords').value;

                const params = new URLSearchParams();
                if (page) params.append('page', page);
                if (limit) params.append('limit', limit);
                if (time) params.append('time', time);
                if (trading_type) params.append('trading_type', trading_type);
                if (keywords) params.append('keywords', keywords);

                const url = `${API_BASE_URL}/admin/marketing/point_record?${params.toString()}`;
                console.log('请求URL:', url);

                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        // 'Authorization': 'Bearer your-token-here' // 如果需要认证
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                displayResult('result1', data);
            } catch (error) {
                console.error('获取积分记录列表失败:', error);
                displayError('result1', error);
            }
        }

        async function testUpdatePointRecordRemark() {
            try {
                const recordId = document.getElementById('recordId').value;
                const remark = document.getElementById('remark').value;

                if (!recordId) {
                    throw new Error('请输入记录ID');
                }

                const url = `${API_BASE_URL}/admin/marketing/point_record/remark/${recordId}`;
                console.log('请求URL:', url);

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        // 'Authorization': 'Bearer your-token-here' // 如果需要认证
                    },
                    body: JSON.stringify({
                        mark: remark || ''
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                displayResult('result2', data);
            } catch (error) {
                console.error('修改备注失败:', error);
                displayError('result2', error);
            }
        }

        async function testOrderDetailView() {
            try {
                const orderId = document.getElementById('testOrderId').value;

                if (!orderId) {
                    throw new Error('请输入订单ID');
                }

                // 模拟订单详情查看
                const url = `${API_BASE_URL}/order/info/${orderId}`;
                console.log('测试订单详情接口:', url);

                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const testResult = {
                    测试类型: '订单详情查看',
                    订单ID: orderId,
                    接口状态: response.status,
                    请求成功: response.ok
                };

                if (response.ok) {
                    const data = await response.json();
                    testResult.返回数据 = data;
                    testResult.测试结果 = '订单详情获取成功';
                } else {
                    testResult.错误信息 = await response.text();
                    testResult.测试结果 = '订单详情获取失败';
                }

                displayResult('result4', testResult);
            } catch (error) {
                console.error('测试订单详情失败:', error);
                displayError('result4', error);
            }
        }

        async function testOrderLinkClick() {
            const mockData = {
                模拟场景: '点击关联订单链接',
                测试数据: [
                    {
                        relation: '订单:12345',
                        点击预期: '打开订单详情弹窗',
                        提取的订单ID: '12345'
                    },
                    {
                        relation: '-',
                        点击预期: '显示无关联订单提示',
                        提取的订单ID: 'null'
                    },
                    {
                        relation: 'manual:system',
                        点击预期: '显示该记录无关联订单提示',
                        提取的订单ID: 'null'
                    }
                ],
                功能说明: {
                    可点击条件: 'relation字段包含"订单:"并且不为"-"',
                    点击效果: '打开订单详情弹窗',
                    样式设计: '蓝色链接字体，下划线，鼠标悬停变色'
                }
            };

            displayResult('result4', mockData);
        }

        async function testGetStatus() {
            try {
                const url = `${API_BASE_URL}/admin/marketing/point_record?page=1&limit=1`;
                console.log('测试API状态:', url);

                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const status = {
                    httpStatus: response.status,
                    statusText: response.statusText,
                    contentType: response.headers.get('content-type'),
                    timestamp: new Date().toISOString()
                };

                if (response.ok) {
                    const data = await response.json();
                    status.responseData = data;
                    status.message = 'API 正常工作';
                } else {
                    status.error = await response.text();
                    status.message = 'API 返回错误';
                }

                displayResult('result3', status);
            } catch (error) {
                console.error('检查API状态失败:', error);
                displayError('result3', error);
            }
        }

        async function testSampleData() {
            const sampleData = {
                示例请求: {
                    获取积分记录: {
                        方法: 'GET',
                        路径: '/admin/marketing/point_record',
                        参数: {
                            page: 1,
                            limit: 20,
                            time: '2024/01/01-2024/12/31',
                            trading_type: 'earn',
                            keywords: '测试用户'
                        }
                    },
                    修改备注: {
                        方法: 'POST',
                        路径: '/admin/marketing/point_record/remark/{id}',
                        请求体: {
                            mark: '管理员备注内容'
                        }
                    }
                },
                示例响应: {
                    获取积分记录: {
                        code: 200,
                        message: 'success',
                        data: {
                            list: [
                                {
                                    id: 1,
                                    relation: '订单:12345',
                                    add_time: '2024-01-01 10:00:00',
                                    number: 100,
                                    pm: true,
                                    nickname: '测试用户',
                                    type_name: '消费获得',
                                    mark: '订单消费获得积分'
                                }
                            ],
                            count: 1,
                            status: {
                                '': '全部',
                                earn: '获得积分',
                                use: '消费积分',
                                refund: '退回积分'
                            }
                        }
                    }
                }
            };

            displayResult('result3', sampleData);
        }

        // 页面加载完成后自动测试API状态
        window.addEventListener('load', function() {
            console.log('页面加载完成，准备测试接口');
        });
    </script>
</body>
</html>