// 测试用户名编码解码功能
console.log('=== 测试用户名编码解码功能 ===');

// 模拟测试数据
const testUserNames = [
  '测试用户',
  '张三',
  'User123',
  '用户_测试',
  '中文+英文 User',
  '特殊字符!@#$%'
];

console.log('\n原始用户名 -> URL编码 -> 解码后用户名');
console.log('----------------------------------------');

testUserNames.forEach(userName => {
  // 模拟URL编码（团队页面跳转时）
  const encodedUserName = encodeURIComponent(userName);
  
  // 模拟URL解码（收入明细页面接收时）
  const decodedUserName = decodeURIComponent(encodedUserName);
  
  console.log(`"${userName}" -> "${encodedUserName}" -> "${decodedUserName}"`);
  
  // 验证解码后是否与原始一致
  if (userName === decodedUserName) {
    console.log('  ✅ 编码解码成功');
  } else {
    console.log('  ❌ 编码解码失败');
  }
  console.log('');
});

console.log('=== 测试完成 ===');
console.log('说明：团队页面跳转时使用 encodeURIComponent() 编码用户名');
console.log('收入明细页面使用 decodeURIComponent() 解码用户名');
console.log('这样可以确保中文字符在URL传递过程中不会出现乱码');