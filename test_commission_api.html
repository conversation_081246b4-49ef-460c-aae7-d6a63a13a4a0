<!DOCTYPE html>
<html>
<head>
    <title>佣金管理接口测试</title>
    <meta charset="utf-8">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .test-button { padding: 8px 15px; margin: 5px; background: #007bff; color: white; border: none; cursor: pointer; }
        .test-button:hover { background: #0056b3; }
        .result { margin: 10px 0; padding: 10px; background: #f8f9fa; border-left: 4px solid #007bff; }
        .error { border-left-color: #dc3545; background: #f8d7da; color: #721c24; }
        pre { white-space: pre-wrap; }
    </style>
</head>
<body>
    <h1>佣金管理接口测试</h1>
    
    <div class="test-section">
        <h2>1. 佣金记录列表</h2>
        <button class="test-button" onclick="testCommissionList()">测试佣金记录列表</button>
        <div id="commissionListResult" class="result" style="display:none;"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 用户佣金详情</h2>
        <label>用户ID: <input type="number" id="userId" value="1" /></label>
        <button class="test-button" onclick="testCommissionDetail()">测试用户佣金详情</button>
        <div id="commissionDetailResult" class="result" style="display:none;"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 用户提取记录</h2>
        <label>用户ID: <input type="number" id="extractUserId" value="1" /></label>
        <button class="test-button" onclick="testExtractList()">测试用户提取记录</button>
        <div id="extractListResult" class="result" style="display:none;"></div>
    </div>

    <script>
        // 基础URL配置
        const BASE_URL = '/api'; // 根据实际情况调整
        
        // 显示结果
        function showResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            element.className = isError ? 'result error' : 'result';
            element.style.display = 'block';
        }
        
        // 测试佣金记录列表
        async function testCommissionList() {
            try {
                const response = await axios.get(`${BASE_URL}/finance/finance/commission_list`, {
                    params: {
                        page: 1,
                        limit: 20
                    }
                });
                showResult('commissionListResult', response.data);
            } catch (error) {
                showResult('commissionListResult', error.response?.data || error.message, true);
            }
        }
        
        // 测试用户佣金详情
        async function testCommissionDetail() {
            const userId = document.getElementById('userId').value;
            try {
                const response = await axios.get(`${BASE_URL}/finance/finance/user_info/${userId}`);
                showResult('commissionDetailResult', response.data);
            } catch (error) {
                showResult('commissionDetailResult', error.response?.data || error.message, true);
            }
        }
        
        // 测试用户提取记录
        async function testExtractList() {
            const userId = document.getElementById('extractUserId').value;
            try {
                const response = await axios.get(`${BASE_URL}/finance/finance/extract_list/${userId}`, {
                    params: {
                        page: 1,
                        limit: 20
                    }
                });
                showResult('extractListResult', response.data);
            } catch (error) {
                showResult('extractListResult', error.response?.data || error.message, true);
            }
        }
    </script>
</body>
</html>