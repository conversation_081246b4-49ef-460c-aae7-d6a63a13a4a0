-- 推三返本查询功能验证SQL脚本

-- 1. 检查阶梯推广商品配置表
SELECT '=== 阶梯推广商品配置检查 ===' as info;
SELECT 
    id,
    goods_id,
    goods_name,
    tier1_rate,
    tier2_rate, 
    tier3_rate,
    tier4_plus_rate,
    is_active,
    create_time
FROM weshop_tiered_promotion_goods 
ORDER BY create_time DESC;

-- 2. 检查活跃的阶梯推广商品数量
SELECT '=== 活跃阶梯推广商品统计 ===' as info;
SELECT 
    COUNT(*) as total_tiered_goods,
    COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_tiered_goods,
    COUNT(CASE WHEN is_active = 0 THEN 1 END) as inactive_tiered_goods
FROM weshop_tiered_promotion_goods;

-- 3. 验证推三返本查询逻辑
SELECT '=== 推三返本商品查询结果 ===' as info;
SELECT 
    g.id,
    g.name,
    g.retail_price,
    g.list_pic_url,
    g.is_delete,
    g.is_on_sale,
    tpg.description as tiered_promotion_desc,
    tpg.tier1_rate,
    tpg.tier3_rate as return_rate
FROM weshop_goods g
INNER JOIN weshop_tiered_promotion_goods tpg ON g.id = tpg.goods_id
WHERE tpg.is_active = 1 
  AND g.is_delete = 0
ORDER BY g.sort_order, g.id;

-- 4. 检查商品表中的相关字段
SELECT '=== 商品表字段检查 ===' as info;
SELECT 
    COUNT(*) as total_goods,
    COUNT(CASE WHEN is_delete = 0 THEN 1 END) as active_goods,
    COUNT(CASE WHEN is_on_sale = 1 THEN 1 END) as on_sale_goods,
    COUNT(CASE WHEN is_newly = 1 THEN 1 END) as newly_goods,
    COUNT(CASE WHEN is_hot = 1 THEN 1 END) as hot_goods
FROM weshop_goods;

-- 5. 模拟前端查询参数的SQL
SELECT '=== 模拟推三返本筛选查询 ===' as info;
-- 这个查询模拟后端的筛选逻辑
SELECT 
    g.id,
    g.name,
    g.category_id,
    g.list_pic_url,
    g.unit_price,
    g.goods_unit,
    g.retail_price,
    'tiered_promotion' as activity_type,
    '推三返本' as activity_name
FROM weshop_goods g
WHERE g.is_delete = 0
  AND g.id IN (
    SELECT goods_id 
    FROM weshop_tiered_promotion_goods 
    WHERE is_active = 1
  )
ORDER BY g.sort_order
LIMIT 20;

-- 6. 检查是否有重复的阶梯推广配置
SELECT '=== 重复配置检查 ===' as info;
SELECT 
    goods_id,
    COUNT(*) as config_count
FROM weshop_tiered_promotion_goods
WHERE is_active = 1
GROUP BY goods_id
HAVING COUNT(*) > 1;

-- 7. 检查阶梯推广商品是否都存在于商品表中
SELECT '=== 数据一致性检查 ===' as info;
SELECT 
    tpg.goods_id,
    tpg.goods_name,
    CASE WHEN g.id IS NULL THEN '商品不存在' ELSE '商品存在' END as goods_status,
    CASE WHEN g.is_delete = 1 THEN '已删除' ELSE '正常' END as delete_status
FROM weshop_tiered_promotion_goods tpg
LEFT JOIN weshop_goods g ON tpg.goods_id = g.id
WHERE tpg.is_active = 1;

-- 8. 如果没有测试数据，创建一些测试数据
-- 注意：只在测试环境执行，生产环境请谨慎使用

/*
-- 创建测试用的阶梯推广商品配置
INSERT INTO weshop_tiered_promotion_goods 
(goods_id, goods_name, tier1_rate, tier2_rate, tier3_rate, tier4_plus_rate, description, is_active, create_time, update_time)
SELECT 
    id as goods_id,
    name as goods_name,
    15.00 as tier1_rate,
    25.00 as tier2_rate,
    35.00 as tier3_rate,
    50.00 as tier4_plus_rate,
    '推三返本活动商品' as description,
    1 as is_active,
    NOW() as create_time,
    NOW() as update_time
FROM weshop_goods 
WHERE is_delete = 0 
  AND is_on_sale = 1
  AND id NOT IN (SELECT goods_id FROM weshop_tiered_promotion_goods WHERE is_active = 1)
LIMIT 5;
*/

-- 9. 性能测试查询
SELECT '=== 查询性能测试 ===' as info;
EXPLAIN SELECT 
    g.id,
    g.name,
    g.retail_price
FROM weshop_goods g
WHERE g.is_delete = 0
  AND g.id IN (
    SELECT goods_id 
    FROM weshop_tiered_promotion_goods 
    WHERE is_active = 1
  )
ORDER BY g.sort_order
LIMIT 20;

-- 10. 验证完成提示
SELECT '=== 验证完成 ===' as info;
SELECT 
    '推三返本查询功能数据验证完成' as message,
    NOW() as check_time;