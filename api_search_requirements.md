# API搜索功能需求说明

## 问题描述
用户反馈：`getPromotionUserDetail`接口需要支持用户名称和手机号模糊搜索

## 当前实现

### 1. 前端搜索参数传递
在前端代码中，搜索关键词已经正确传递到API：

**普通页面搜索** ([`loadTeamList`](app/wjhx/pages/ucenter/team/team.js:152)):
```javascript
let params = {
  pageNum: this.data.page,
  pageSize: this.data.pageSize,
  keyword: this.data.searchKeyword  // 搜索关键词
};
```

**下钻页面搜索** ([`loadDrillDownTeamList`](app/wjhx/pages/ucenter/team/team.js:211)):
```javascript
util.request(api.GetPromotionUserDetail, {
  userId: userId,
  keyword: this.data.searchKeyword  // 搜索关键词
}, 'POST')
```

### 2. 涉及的API接口
根据代码分析，涉及搜索的API接口有：

1. **AdminUserList** ([api.js:140](app/wjhx/config/api.js:140)) - 管理员用户列表
   - 路径: `/weshop-wjhx/wechat/admin/user/list2`
   - 当前支持 `keyword` 参数

2. **GetPromotionUserDetail** ([api.js:25](app/wjhx/config/api.js:25)) - 获取推广用户详情
   - 路径: `/weshop-wjhx/wechat/user/getPromotionUserDetail`
   - 需要支持 `keyword` 参数进行模糊搜索

## 后端API需求

### GetPromotionUserDetail接口需要支持：
1. **用户昵称模糊搜索** - 支持对 `nickname` 字段的模糊匹配
2. **手机号模糊搜索** - 支持对 `mobile` 字段的模糊匹配
3. **用户名模糊搜索** - 支持对 `username` 字段的模糊匹配

### 建议搜索逻辑：
```sql
-- 示例SQL查询逻辑
SELECT * FROM users 
WHERE (promoter_id = :userId) 
AND (
  nickname LIKE CONCAT('%', :keyword, '%') OR
  mobile LIKE CONCAT('%', :keyword, '%') OR 
  username LIKE CONCAT('%', :keyword, '%')
)
```

## 前端已完成的准备工作

### 1. 参数传递
- ✅ 搜索关键词已正确传递到 `keyword` 参数
- ✅ 支持普通页面和下钻页面的搜索
- ✅ 搜索状态保持和恢复功能正常

### 2. 用户界面
- ✅ 搜索输入框显示正常 ([team.wxml:32](app/wjhx/pages/ucenter/team/team.wxml:32))
- ✅ 搜索按钮功能正常 ([team.wxml:33](app/wjhx/pages/ucenter/team/team.wxml:33))
- ✅ 搜索关键词状态管理正常

## 测试验证
前端搜索功能已经过全面测试：
- ✅ 普通页面搜索参数构建正确
- ✅ 下钻页面搜索参数构建正确  
- ✅ 搜索状态保持正常
- ✅ 搜索方法选择逻辑正确

## 下一步工作
需要后端开发团队对 `GetPromotionUserDetail` 接口进行升级：

1. **添加keyword参数支持**
2. **实现用户昵称模糊搜索**
3. **实现手机号模糊搜索**
4. **实现用户名模糊搜索**
5. **测试接口搜索功能**

一旦后端接口支持模糊搜索，前端的搜索功能将完全正常工作。