<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>余额管理功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .section h3 { margin-top: 0; color: #333; }
        .api-test { background-color: #f9f9f9; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .result { margin-top: 10px; padding: 10px; border-radius: 4px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; }
        input, select { padding: 8px; margin: 5px; }
        table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>余额管理功能测试</h1>
    
    <!-- 接口测试部分 -->
    <div class="section">
        <h3>1. API接口测试</h3>
        
        <!-- 获取余额列表接口测试 -->
        <div class="api-test">
            <h4>获取余额记录列表</h4>
            <p><strong>接口：</strong> GET /adminapi/finance/balance/list</p>
            
            <div>
                <label>交易类型：</label>
                <select id="trading_type">
                    <option value="">全部</option>
                    <option value="recharge">余额充值</option>
                    <option value="use">余额使用</option>
                    <option value="refund">余额退回</option>
                    <option value="adjust">手动调整</option>
                </select>
                
                <label>时间范围：</label>
                <input type="date" id="start_date" placeholder="开始日期">
                <input type="date" id="end_date" placeholder="结束日期">
                
                <label>关键词：</label>
                <input type="text" id="keywords" placeholder="用户昵称或备注">
                
                <label>页码：</label>
                <input type="number" id="page" value="1" min="1">
                
                <label>每页数量：</label>
                <input type="number" id="limit" value="20" min="1" max="100">
                
                <br><br>
                <button onclick="testBalanceList()">测试获取列表</button>
            </div>
            
            <div id="balance_list_result" class="result" style="display:none;"></div>
        </div>
        
        <!-- 设置备注接口测试 -->
        <div class="api-test">
            <h4>设置余额记录备注</h4>
            <p><strong>接口：</strong> POST /adminapi/finance/balance/set_mark/{id}</p>
            
            <div>
                <label>记录ID：</label>
                <input type="number" id="mark_id" placeholder="余额记录ID">
                
                <label>备注内容：</label>
                <input type="text" id="mark_content" placeholder="备注内容">
                
                <br><br>
                <button onclick="testSetMark()">测试设置备注</button>
            </div>
            
            <div id="set_mark_result" class="result" style="display:none;"></div>
        </div>
    </div>
    
    <!-- 前端功能测试 -->
    <div class="section">
        <h3>2. 前端功能测试指南</h3>
        
        <h4>测试步骤：</h4>
        <ol>
            <li><strong>页面访问测试：</strong>
                <ul>
                    <li>访问 <code>/admin/finance/balance</code> 页面</li>
                    <li>检查页面是否正常加载，无JS错误</li>
                    <li>验证页面布局是否合理，响应式效果</li>
                </ul>
            </li>
            
            <li><strong>数据筛选功能：</strong>
                <ul>
                    <li>测试时间范围筛选：选择不同时间范围，验证数据筛选</li>
                    <li>测试交易类型筛选：选择不同类型，验证筛选结果</li>
                    <li>测试关键词搜索：输入用户昵称或备注进行搜索</li>
                    <li>测试重置功能：点击重置按钮，验证表单清空</li>
                </ul>
            </li>
            
            <li><strong>数据统计显示：</strong>
                <ul>
                    <li>验证今日余额变动统计显示正确</li>
                    <li>验证今日充值金额统计显示正确</li>
                    <li>验证今日使用金额统计显示正确</li>
                    <li>验证记录总数统计显示正确</li>
                </ul>
            </li>
            
            <li><strong>表格显示功能：</strong>
                <ul>
                    <li>验证余额记录列表正常显示</li>
                    <li>验证金额显示（正数绿色，负数红色）</li>
                    <li>验证交易类型标签显示</li>
                    <li>验证关联信息标签显示</li>
                    <li>验证分页功能正常工作</li>
                </ul>
            </li>
            
            <li><strong>备注编辑功能：</strong>
                <ul>
                    <li>点击备注按钮，打开备注编辑对话框</li>
                    <li>输入备注内容，保存并验证</li>
                    <li>测试备注内容长度限制（200字符）</li>
                    <li>测试取消操作</li>
                </ul>
            </li>
            
            <li><strong>其他功能：</strong>
                <ul>
                    <li>测试刷新按钮功能</li>
                    <li>测试导出功能（显示开发中提示）</li>
                    <li>测试移动端适配效果</li>
                </ul>
            </li>
        </ol>
    </div>
    
    <!-- 数据库验证 -->
    <div class="section">
        <h3>3. 数据库验证</h3>
        
        <h4>需要验证的数据表：</h4>
        <ul>
            <li><strong>weshop_balance_record</strong> - 余额记录表</li>
            <li><strong>weshop_user</strong> - 用户表（余额字段）</li>
        </ul>
        
        <h4>验证SQL语句：</h4>
        <pre><code>
-- 查看余额记录表结构
DESCRIBE weshop_balance_record;

-- 查看最近的余额记录
SELECT br.*, u.nickname, u.balance 
FROM weshop_balance_record br 
LEFT JOIN weshop_user u ON br.user_id = u.id 
ORDER BY br.create_time DESC 
LIMIT 10;

-- 统计今日余额变动
SELECT 
    type,
    COUNT(*) as count,
    SUM(amount) as total_amount
FROM weshop_balance_record 
WHERE DATE(create_time) = CURDATE()
GROUP BY type;

-- 检查用户余额一致性（示例用户ID=1）
SELECT 
    u.balance as user_balance,
    COALESCE(SUM(br.amount), 0) as calculated_balance
FROM weshop_user u
LEFT JOIN weshop_balance_record br ON u.id = br.user_id
WHERE u.id = 1;
        </code></pre>
    </div>
    
    <!-- 测试数据表格 -->
    <div class="section">
        <h3>4. 测试结果记录</h3>
        <table id="test_results">
            <thead>
                <tr>
                    <th>测试项目</th>
                    <th>测试状态</th>
                    <th>结果说明</th>
                    <th>测试时间</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>页面加载</td>
                    <td id="page_load_status">-</td>
                    <td id="page_load_desc">-</td>
                    <td id="page_load_time">-</td>
                </tr>
                <tr>
                    <td>获取列表接口</td>
                    <td id="list_api_status">-</td>
                    <td id="list_api_desc">-</td>
                    <td id="list_api_time">-</td>
                </tr>
                <tr>
                    <td>设置备注接口</td>
                    <td id="mark_api_status">-</td>
                    <td id="mark_api_desc">-</td>
                    <td id="mark_api_time">-</td>
                </tr>
                <tr>
                    <td>筛选功能</td>
                    <td id="filter_status">-</td>
                    <td id="filter_desc">-</td>
                    <td id="filter_time">-</td>
                </tr>
                <tr>
                    <td>统计显示</td>
                    <td id="stats_status">-</td>
                    <td id="stats_desc">-</td>
                    <td id="stats_time">-</td>
                </tr>
            </tbody>
        </table>
        
        <br>
        <button onclick="exportTestResults()">导出测试结果</button>
        <button onclick="clearTestResults()">清空测试结果</button>
    </div>
    
    <script>
        // API基础地址（需要根据实际环境修改）
        const API_BASE = 'http://localhost:8080/adminapi';
        
        // 测试获取余额列表
        async function testBalanceList() {
            const resultDiv = document.getElementById('balance_list_result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '测试中...';
            
            try {
                const params = new URLSearchParams();
                
                const trading_type = document.getElementById('trading_type').value;
                const start_date = document.getElementById('start_date').value;
                const end_date = document.getElementById('end_date').value;
                const keywords = document.getElementById('keywords').value;
                const page = document.getElementById('page').value;
                const limit = document.getElementById('limit').value;
                
                if (trading_type) params.append('trading_type', trading_type);
                if (start_date && end_date) {
                    params.append('time', `${start_date.replace(/-/g, '/')}-${end_date.replace(/-/g, '/')}`);
                }
                if (keywords) params.append('keywords', keywords);
                params.append('page', page);
                params.append('limit', limit);
                
                const response = await fetch(`${API_BASE}/finance/balance/list?${params}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        // 'Authorization': 'Bearer YOUR_TOKEN' // 需要添加认证token
                    }
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>测试成功！</strong><br>
                        响应数据：<pre>${JSON.stringify(result, null, 2)}</pre>
                    `;
                    updateTestStatus('list_api_status', '✓ 通过', '接口正常返回数据');
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <strong>测试失败！</strong><br>
                        状态码：${response.status}<br>
                        错误信息：${result.message || '未知错误'}
                    `;
                    updateTestStatus('list_api_status', '✗ 失败', `状态码${response.status}`);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <strong>测试异常！</strong><br>
                    错误信息：${error.message}
                `;
                updateTestStatus('list_api_status', '✗ 异常', error.message);
            }
        }
        
        // 测试设置备注
        async function testSetMark() {
            const resultDiv = document.getElementById('set_mark_result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '测试中...';
            
            const id = document.getElementById('mark_id').value;
            const mark = document.getElementById('mark_content').value;
            
            if (!id) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '<strong>请输入记录ID！</strong>';
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/finance/balance/set_mark/${id}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        // 'Authorization': 'Bearer YOUR_TOKEN' // 需要添加认证token
                    },
                    body: JSON.stringify({ mark: mark })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>测试成功！</strong><br>
                        响应数据：<pre>${JSON.stringify(result, null, 2)}</pre>
                    `;
                    updateTestStatus('mark_api_status', '✓ 通过', '备注设置成功');
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <strong>测试失败！</strong><br>
                        状态码：${response.status}<br>
                        错误信息：${result.message || '未知错误'}
                    `;
                    updateTestStatus('mark_api_status', '✗ 失败', `状态码${response.status}`);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <strong>测试异常！</strong><br>
                    错误信息：${error.message}
                `;
                updateTestStatus('mark_api_status', '✗ 异常', error.message);
            }
        }
        
        // 更新测试状态
        function updateTestStatus(statusId, status, description) {
            document.getElementById(statusId).textContent = status;
            document.getElementById(statusId.replace('_status', '_desc')).textContent = description;
            document.getElementById(statusId.replace('_status', '_time')).textContent = new Date().toLocaleString();
        }
        
        // 导出测试结果
        function exportTestResults() {
            const table = document.getElementById('test_results');
            let csv = '测试项目,测试状态,结果说明,测试时间\n';
            
            for (let i = 1; i < table.rows.length; i++) {
                const row = table.rows[i];
                csv += `${row.cells[0].textContent},${row.cells[1].textContent},${row.cells[2].textContent},${row.cells[3].textContent}\n`;
            }
            
            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `balance_test_results_${new Date().getTime()}.csv`;
            link.click();
        }
        
        // 清空测试结果
        function clearTestResults() {
            const statusCells = document.querySelectorAll('#test_results td[id$="_status"]');
            const descCells = document.querySelectorAll('#test_results td[id$="_desc"]');
            const timeCells = document.querySelectorAll('#test_results td[id$="_time"]');
            
            statusCells.forEach(cell => cell.textContent = '-');
            descCells.forEach(cell => cell.textContent = '-');
            timeCells.forEach(cell => cell.textContent = '-');
        }
        
        // 页面加载完成后设置默认日期
        window.onload = function() {
            const today = new Date();
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);
            
            document.getElementById('start_date').value = yesterday.toISOString().split('T')[0];
            document.getElementById('end_date').value = today.toISOString().split('T')[0];
        };
    </script>
</body>
</html>