-- 测试订单取消时用户表字段更新的SQL脚本
-- 用于验证余额和积分字段是否正确更新

-- ==========================================
-- 1. 创建测试数据
-- ==========================================

-- 创建测试用户（如果不存在）
INSERT IGNORE INTO `weshop_user` (
    `id`, `username`, `nickname`, `balance`, `points`, 
    `register_time`, `wechat_open_id`
) VALUES 
(999, 'test_cancel_user', '测试取消订单用户', 100.00, 1000, NOW(), 'test_cancel_openid');

-- 创建测试订单（使用了余额和积分）
INSERT IGNORE INTO `weshop_order` (
    `id`, `order_sn`, `user_id`, `order_status`, `pay_status`,
    `consignee`, `mobile`, `address`, `goods_price`, `freight_price`,
    `coupon_price`, `integral`, `integral_money`, `balance_price`,
    `actual_price`, `create_time`
) VALUES 
(999, 'TEST_CANCEL_ORDER_999', 999, 0, 0,
 '测试收货人', '13800138000', '测试地址',
 50.00, 0.00, 0.00, 200, 2.00, 10.00, 38.00, NOW());

-- 创建积分使用记录
INSERT IGNORE INTO `weshop_points_record` (
    `id`, `user_id`, `type`, `points`, `source`, `source_id`,
    `description`, `create_time`, `update_time`
) VALUES 
(999, 999, 'use', -200, 'order', 999, '订单使用积分抵扣', NOW(), NOW());

-- 创建余额使用记录
INSERT IGNORE INTO `weshop_balance_record` (
    `id`, `user_id`, `type`, `amount`, `balance_before`, `balance_after`,
    `source`, `source_id`, `description`, `create_time`, `update_time`
) VALUES 
(999, 999, 'use', -10.00, 100.00, 90.00, 'order', 999, '订单使用余额支付', NOW(), NOW());

-- 更新用户余额和积分（模拟订单提交后的状态）
UPDATE `weshop_user` 
SET `balance` = 90.00, `points` = 800 
WHERE `id` = 999;

-- ==========================================
-- 2. 显示取消前的状态
-- ==========================================

SELECT '=== 订单取消前的状态 ===' as info;

SELECT 
    '用户信息' as type,
    id, username, balance, points
FROM `weshop_user` 
WHERE `id` = 999;

SELECT 
    '订单信息' as type,
    id, order_sn, user_id, order_status, integral, integral_money, balance_price, actual_price
FROM `weshop_order` 
WHERE `id` = 999;

SELECT 
    '积分记录' as type,
    id, user_id, type, points, source, source_id, description
FROM `weshop_points_record` 
WHERE `user_id` = 999 AND `source_id` = 999;

SELECT 
    '余额记录' as type,
    id, user_id, type, amount, balance_before, balance_after, source, source_id, description
FROM `weshop_balance_record` 
WHERE `user_id` = 999 AND `source_id` = 999;

-- ==========================================
-- 3. 模拟订单取消操作
-- ==========================================

-- 注意：这里只是模拟SQL操作，实际应该通过Java服务层调用
-- 实际测试时应该调用 OrderService.cancel(999) 方法

-- 1. 更新订单状态为已取消
UPDATE `weshop_order` 
SET `order_status` = 4 
WHERE `id` = 999;

-- 2. 创建积分退回记录
INSERT INTO `weshop_points_record` (
    `user_id`, `type`, `points`, `source`, `source_id`,
    `description`, `create_time`, `update_time`
) VALUES 
(999, 'refund', 200, 'order_cancel', 999, '订单取消退回积分', NOW(), NOW());

-- 3. 更新用户积分
UPDATE `weshop_user` 
SET `points` = (
    SELECT COALESCE(SUM(points), 0) 
    FROM `weshop_points_record` 
    WHERE `user_id` = 999
)
WHERE `id` = 999;

-- 4. 创建余额退回记录
INSERT INTO `weshop_balance_record` (
    `user_id`, `type`, `amount`, `balance_before`, `balance_after`,
    `source`, `source_id`, `description`, `create_time`, `update_time`
) VALUES 
(999, 'refund', 10.00, 90.00, 100.00, 'order_cancel', 999, '订单取消退回余额', NOW(), NOW());

-- 5. 更新用户余额
UPDATE `weshop_user` 
SET `balance` = 100.00 
WHERE `id` = 999;

-- ==========================================
-- 4. 显示取消后的状态
-- ==========================================

SELECT '=== 订单取消后的状态 ===' as info;

SELECT 
    '用户信息' as type,
    id, username, balance, points
FROM `weshop_user` 
WHERE `id` = 999;

SELECT 
    '订单信息' as type,
    id, order_sn, user_id, order_status, integral, integral_money, balance_price, actual_price
FROM `weshop_order` 
WHERE `id` = 999;

SELECT 
    '积分记录' as type,
    id, user_id, type, points, source, source_id, description
FROM `weshop_points_record` 
WHERE `user_id` = 999 
ORDER BY `create_time`;

SELECT 
    '余额记录' as type,
    id, user_id, type, amount, balance_before, balance_after, source, source_id, description
FROM `weshop_balance_record` 
WHERE `user_id` = 999 
ORDER BY `create_time`;

-- ==========================================
-- 5. 验证结果
-- ==========================================

SELECT '=== 验证结果 ===' as info;

-- 验证用户余额是否正确恢复
SELECT 
    CASE 
        WHEN balance = 100.00 THEN '✓ 余额恢复正确'
        ELSE '✗ 余额恢复错误'
    END as balance_check,
    balance as current_balance
FROM `weshop_user` 
WHERE `id` = 999;

-- 验证用户积分是否正确恢复
SELECT 
    CASE 
        WHEN points = 1000 THEN '✓ 积分恢复正确'
        ELSE '✗ 积分恢复错误'
    END as points_check,
    points as current_points
FROM `weshop_user` 
WHERE `id` = 999;

-- 验证订单状态是否正确更新
SELECT 
    CASE 
        WHEN order_status = 4 THEN '✓ 订单状态更新正确'
        ELSE '✗ 订单状态更新错误'
    END as order_status_check,
    order_status as current_status
FROM `weshop_order` 
WHERE `id` = 999;

-- ==========================================
-- 6. 清理测试数据
-- ==========================================

-- 取消注释以下代码来清理测试数据
/*
DELETE FROM `weshop_balance_record` WHERE `user_id` = 999;
DELETE FROM `weshop_points_record` WHERE `user_id` = 999;
DELETE FROM `weshop_order` WHERE `id` = 999;
DELETE FROM `weshop_user` WHERE `id` = 999;

SELECT '测试数据已清理' as cleanup_message;
*/

SELECT 
    '测试完成！' as final_message,
    '请检查上述验证结果' as instruction,
    NOW() as test_time;
