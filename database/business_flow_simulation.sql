-- 完整业务流程模拟脚本
-- 模拟用户从注册 → 充值 → 获得优惠券 → 购物 → 使用优惠券和余额的完整流程

-- ==========================================
-- 1. 创建测试商品数据（如果商品表存在）
-- ==========================================

-- 注意：这部分需要根据实际的商品表结构调整
/*
INSERT IGNORE INTO weshop_goods (id, name, brief, pic_url, gallery, unit, keywords, category_id, brand_id, price, counter_price, retail_price, detail, specification, parameter, is_on_sale, is_new, is_hot, sort_order, deleted, add_time, update_time) VALUES
(9001, '测试商品A', '用于测试优惠券的商品A', '/static/goods/test_a.jpg', '[]', '件', '测试', 1, 1, 60.00, 80.00, 60.00, '测试商品详情', '[]', '[]', 1, 1, 0, 100, 0, NOW(), NOW()),
(9002, '测试商品B', '用于测试优惠券的商品B', '/static/goods/test_b.jpg', '[]', '件', '测试', 1, 1, 120.00, 150.00, 120.00, '测试商品详情', '[]', '[]', 1, 1, 0, 100, 0, NOW(), NOW()),
(9003, '测试商品C', '用于测试优惠券的商品C', '/static/goods/test_c.jpg', '[]', '件', '测试', 1, 1, 200.00, 250.00, 200.00, '测试商品详情', '[]', '[]', 1, 1, 0, 100, 0, NOW(), NOW());
*/

-- ==========================================
-- 2. 创建业务流程测试用户
-- ==========================================

INSERT IGNORE INTO weshop_user (id, username, nickname, mobile, balance, register_time, wechat_open_id) VALUES
(888, 'flowtest', '业务流程测试用户', '13888888888', 0.00, NOW(), 'openid_flow_test');

-- ==========================================
-- 3. 流程步骤1：用户充值
-- ==========================================

-- 第一次充值：500元
INSERT INTO weshop_recharge_record (
    user_id, amount, coupon_count, coupon_amount, status, payment_method,
    wx_order_id, wx_transaction_id, create_time, pay_time, remark
) VALUES (
    888, 500.00, 5, 10.00, 1, 1,
    'FLOW_TEST_500_' || UNIX_TIMESTAMP(NOW()),
    'wx_flow_' || UNIX_TIMESTAMP(NOW()),
    DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_SUB(NOW(), INTERVAL 5 DAY),
    '业务流程测试：首次充值500元'
);

-- 更新余额
UPDATE weshop_user SET balance = 500.00 WHERE id = 888;

-- 生成10元优惠券
INSERT INTO weshop_user_coupon (
    user_id, coupon_id, coupon_number, title, description, type, type_name,
    amount, min_amount, status, create_time, start_time, end_time
) VALUES
(888, 1, 'FLOW_TEST_10_001', '10元消费券', '充值500元赠送，满50元可用', 1, '满减券', 10.00, 50.00, 0, DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_ADD(NOW(), INTERVAL 85 DAY)),
(888, 1, 'FLOW_TEST_10_002', '10元消费券', '充值500元赠送，满50元可用', 1, '满减券', 10.00, 50.00, 0, DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_ADD(NOW(), INTERVAL 85 DAY)),
(888, 1, 'FLOW_TEST_10_003', '10元消费券', '充值500元赠送，满50元可用', 1, '满减券', 10.00, 50.00, 0, DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_ADD(NOW(), INTERVAL 85 DAY)),
(888, 1, 'FLOW_TEST_10_004', '10元消费券', '充值500元赠送，满50元可用', 1, '满减券', 10.00, 50.00, 0, DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_ADD(NOW(), INTERVAL 85 DAY)),
(888, 1, 'FLOW_TEST_10_005', '10元消费券', '充值500元赠送，满50元可用', 1, '满减券', 10.00, 50.00, 0, DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_ADD(NOW(), INTERVAL 85 DAY));

-- ==========================================
-- 4. 流程步骤2：第一次购物（使用10元券）
-- ==========================================

-- 模拟购物车数据（如果购物车表存在）
/*
INSERT INTO weshop_cart (id, user_id, goods_id, goods_sn, goods_name, product_id, price, number, specifications, checked, pic_url, add_time, update_time, deleted) VALUES
(8001, 888, 9001, 'TEST_A_001', '测试商品A', 1, 60.00, 1, '[]', 1, '/static/goods/test_a.jpg', DATE_SUB(NOW(), INTERVAL 3 DAY), DATE_SUB(NOW(), INTERVAL 3 DAY), 0);
*/

-- 模拟订单数据（订单金额60元，使用10元券，余额支付50元）
/*
INSERT INTO weshop_order (
    id, order_sn, user_id, order_status, pay_status, consignee, mobile, address,
    goods_price, freight_price, coupon_price, integral_price, groupon_price, order_price, actual_price,
    pay_time, ship_time, confirm_time, add_time, end_time, deleted
) VALUES
(8001, 'FLOW_ORDER_001_' || UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 3 DAY)), 888, 401, 2, '业务流程测试用户', '13888888888', '测试收货地址',
 60.00, 0.00, 10.00, 0.00, 0.00, 50.00, 50.00,
 DATE_SUB(NOW(), INTERVAL 3 DAY), DATE_SUB(NOW(), INTERVAL 3 DAY), DATE_SUB(NOW(), INTERVAL 3 DAY),
 DATE_SUB(NOW(), INTERVAL 3 DAY), DATE_SUB(NOW(), INTERVAL 3 DAY), 0);
*/

-- 标记一张10元券为已使用
UPDATE weshop_user_coupon 
SET status = 1, use_time = DATE_SUB(NOW(), INTERVAL 3 DAY), order_id = 8001
WHERE user_id = 888 AND coupon_number = 'FLOW_TEST_10_001';

-- 扣除余额（60元商品 - 10元券 = 50元余额支付）
UPDATE weshop_user SET balance = balance - 50.00 WHERE id = 888;

-- ==========================================
-- 5. 流程步骤3：第二次充值（1000元）
-- ==========================================

SELECT SLEEP(1);

INSERT INTO weshop_recharge_record (
    user_id, amount, coupon_count, coupon_amount, status, payment_method,
    wx_order_id, wx_transaction_id, create_time, pay_time, remark
) VALUES (
    888, 1000.00, 5, 20.00, 1, 1,
    'FLOW_TEST_1000_' || UNIX_TIMESTAMP(NOW()),
    'wx_flow_' || UNIX_TIMESTAMP(NOW()),
    DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY),
    '业务流程测试：第二次充值1000元'
);

-- 更新余额
UPDATE weshop_user SET balance = balance + 1000.00 WHERE id = 888;

-- 生成20元优惠券
INSERT INTO weshop_user_coupon (
    user_id, coupon_id, coupon_number, title, description, type, type_name,
    amount, min_amount, status, create_time, start_time, end_time
) VALUES
(888, 2, 'FLOW_TEST_20_001', '20元消费券', '充值1000元赠送，满100元可用', 1, '满减券', 20.00, 100.00, 0, DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_ADD(NOW(), INTERVAL 89 DAY)),
(888, 2, 'FLOW_TEST_20_002', '20元消费券', '充值1000元赠送，满100元可用', 1, '满减券', 20.00, 100.00, 0, DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_ADD(NOW(), INTERVAL 89 DAY)),
(888, 2, 'FLOW_TEST_20_003', '20元消费券', '充值1000元赠送，满100元可用', 1, '满减券', 20.00, 100.00, 0, DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_ADD(NOW(), INTERVAL 89 DAY)),
(888, 2, 'FLOW_TEST_20_004', '20元消费券', '充值1000元赠送，满100元可用', 1, '满减券', 20.00, 100.00, 0, DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_ADD(NOW(), INTERVAL 89 DAY)),
(888, 2, 'FLOW_TEST_20_005', '20元消费券', '充值1000元赠送，满100元可用', 1, '满减券', 20.00, 100.00, 0, DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_ADD(NOW(), INTERVAL 89 DAY));

-- ==========================================
-- 6. 流程步骤4：第二次购物（使用20元券+余额）
-- ==========================================

-- 模拟第二次购物（订单金额150元，使用20元券，余额支付130元）
/*
INSERT INTO weshop_order (
    id, order_sn, user_id, order_status, pay_status, consignee, mobile, address,
    goods_price, freight_price, coupon_price, integral_price, groupon_price, order_price, actual_price,
    pay_time, ship_time, confirm_time, add_time, end_time, deleted
) VALUES
(8002, 'FLOW_ORDER_002_' || UNIX_TIMESTAMP(NOW()), 888, 401, 2, '业务流程测试用户', '13888888888', '测试收货地址',
 150.00, 0.00, 20.00, 0.00, 0.00, 130.00, 130.00,
 NOW(), NOW(), NOW(), NOW(), NOW(), 0);
*/

-- 标记一张20元券为已使用
UPDATE weshop_user_coupon 
SET status = 1, use_time = NOW(), order_id = 8002
WHERE user_id = 888 AND coupon_number = 'FLOW_TEST_20_001';

-- 扣除余额（150元商品 - 20元券 = 130元余额支付）
UPDATE weshop_user SET balance = balance - 130.00 WHERE id = 888;

-- ==========================================
-- 7. 创建一张过期的优惠券（用于测试）
-- ==========================================

INSERT INTO weshop_user_coupon (
    user_id, coupon_id, coupon_number, title, description, type, type_name,
    amount, min_amount, status, create_time, start_time, end_time
) VALUES
(888, 1, 'FLOW_TEST_EXPIRED', '10元消费券', '已过期的测试券', 1, '满减券', 10.00, 50.00, 2, DATE_SUB(NOW(), INTERVAL 10 DAY), DATE_SUB(NOW(), INTERVAL 10 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY));

-- ==========================================
-- 8. 业务流程验证和总结
-- ==========================================

-- 查看用户当前状态
SELECT 
    id, username, nickname, balance,
    CONCAT('当前余额：', balance, '元') as current_balance
FROM weshop_user WHERE id = 888;

-- 查看充值历史
SELECT 
    amount as '充值金额',
    coupon_count as '赠送券数',
    coupon_amount as '券面额',
    DATE_FORMAT(create_time, '%Y-%m-%d') as '充值日期',
    remark as '备注'
FROM weshop_recharge_record 
WHERE user_id = 888 
ORDER BY create_time;

-- 查看优惠券使用情况
SELECT 
    title as '优惠券名称',
    amount as '面额',
    min_amount as '最低消费',
    CASE 
        WHEN status = 0 THEN '可用'
        WHEN status = 1 THEN '已使用'
        WHEN status = 2 THEN '已过期'
    END as '状态',
    CASE 
        WHEN status = 1 THEN DATE_FORMAT(use_time, '%Y-%m-%d')
        WHEN status = 2 THEN '已过期'
        ELSE DATE_FORMAT(end_time, '%Y-%m-%d')
    END as '使用/过期日期'
FROM weshop_user_coupon 
WHERE user_id = 888
ORDER BY create_time;

-- 统计汇总
SELECT 
    '业务流程测试完成' as '状态',
    (SELECT balance FROM weshop_user WHERE id = 888) as '当前余额',
    (SELECT COUNT(*) FROM weshop_user_coupon WHERE user_id = 888 AND status = 0) as '可用优惠券数量',
    (SELECT SUM(amount) FROM weshop_user_coupon WHERE user_id = 888 AND status = 0) as '可用优惠券总价值',
    (SELECT COUNT(*) FROM weshop_user_coupon WHERE user_id = 888 AND status = 1) as '已使用优惠券数量',
    (SELECT COUNT(*) FROM weshop_user_coupon WHERE user_id = 888 AND status = 2) as '已过期优惠券数量';

-- ==========================================
-- 9. 测试场景建议
-- ==========================================

SELECT 
    '完整业务流程测试数据创建完成！' as message;

SELECT 
    '用户888的完整流程：' as flow_summary,
    '1. 充值500元 → 获得5张10元券' as step1,
    '2. 购买60元商品 → 使用1张10元券 + 50元余额' as step2,
    '3. 充值1000元 → 获得5张20元券' as step3,
    '4. 购买150元商品 → 使用1张20元券 + 130元余额' as step4,
    '5. 当前状态：余额1320元，8张可用券，2张已用券，1张过期券' as current_status;

SELECT 
    '可以测试的场景：' as test_scenarios,
    '1. 查看用户优惠券列表（可用/已用/过期）' as scenario1,
    '2. 测试购物车结算（不同金额使用不同券）' as scenario2,
    '3. 测试余额+优惠券组合支付' as scenario3,
    '4. 测试优惠券使用规则验证' as scenario4;