-- 阶梯推广商品页面UI演示数据
-- 用于测试商品页面的阶梯推广提示功能

-- 1. 确保有测试商品（如果不存在则创建）
INSERT IGNORE INTO `weshop_goods` 
(`id`, `goods_sn`, `name`, `category_id`, `brand_id`, `gallery`, `keywords`, `goods_brief`, `goods_desc`, `is_on_sale`, `is_delete`, `sort_order`, `create_time`, `update_time`, `retail_price`, `market_price`, `share_url`, `is_new`, `is_hot`, `unit_price`, `promotion_desc`, `promotion_tag`, `app_exclusive_price`, `app_exclusive_price_start`, `app_exclusive_price_end`, `counter_price`, `extra_price`, `primary_pic_url`, `list_pic_url`, `goods_number`, `detail_tag`)
VALUES 
(1, 'DEMO_TIERED_001', '【阶梯推广】高端智能手表', 1, 1, 
 '["https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400", "https://images.unsplash.com/photo-1546868871-7041f2a55e12?w=400"]', 
 '智能手表,阶梯推广,高端', 
 '**限时阶梯推广商品** 分享给好友购买，享受20%-50%高额返现！采用最新科技，续航持久，功能丰富。', 
 '<p><strong>产品特色：</strong></p><ul><li>🎯 <span style="color:#ef4444;font-weight:bold">阶梯推广商品，分享即可获得高额返现</span></li><li>⌚ 1.4英寸高清彩屏，视觉体验极佳</li><li>💓 24小时心率监测，健康管理专家</li><li>🏃 50+运动模式，专业运动伴侣</li><li>🔋 7天超长续航，告别频繁充电</li><li>💧 5ATM防水等级，游泳佩戴无忧</li></ul>', 
 1, 0, 100, NOW(), NOW(), 299.00, 599.00, '', 1, 1, 299.00, 
 '🎯阶梯推广爆款！分享返现20%-50%', '阶梯推广', 299.00, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), 599.00, 0.00, 
 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400', 
 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=300', 
 1000, '阶梯推广,限时特惠,高端品质');

-- 2. 配置为阶梯推广商品
INSERT IGNORE INTO `weshop_tiered_promotion_goods` 
(`goods_id`, `goods_name`, `is_active`, `tier1_rate`, `tier2_rate`, `tier3_rate`, `tier4_plus_rate`, `description`, `create_time`, `update_time`)
VALUES 
(1, '【阶梯推广】高端智能手表', 1, 20.00, 30.00, 50.00, 10.00, 
 '热门阶梯推广商品：第1笔返现20%，第2笔返现30%，第3笔返现50%，第4笔及以后返现10%。分享给好友购买相同商品即可获得对应阶梯的高额返现！', 
 NOW(), NOW());

-- 3. 创建商品规格（如果需要）
INSERT IGNORE INTO `weshop_product` 
(`id`, `goods_id`, `goods_specification_ids`, `goods_sn`, `retail_price`, `market_price`, `goods_number`, `primary_pic_url`)
VALUES 
(1, 1, '', 'DEMO_TIERED_001_DEFAULT', 299.00, 599.00, 1000, 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400');

-- 4. 创建演示用户（如果不存在）
INSERT IGNORE INTO `weshop_user` 
(`id`, `username`, `nickname`, `password`, `wechat_open_id`, `mobile`, `avatar`, `gender`, `register_time`, `register_ip`, `last_login_time`, `last_login_ip`, `promotion_code`)
VALUES 
(999, 'demo_promoter', '演示推广者', '', 'wx_demo_promoter', '13800000999', 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100', 1, NOW(), '127.0.0.1', NOW(), '127.0.0.1', 'promo_999');

-- 5. 创建演示推广统计数据
INSERT IGNORE INTO `weshop_user_goods_promotion_stats` 
(`id`, `promoter_id`, `goods_id`, `total_promotion_count`, `tier1_count`, `tier2_count`, `tier3_count`, `tier4_plus_count`, `total_commission`, `first_promotion_time`, `last_promotion_time`, `create_time`, `update_time`)
VALUES 
(999, 999, 1, 2, 1, 1, 0, 0, 149.70, DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY), NOW(), NOW());

-- 6. 创建普通商品作为对比
INSERT IGNORE INTO `weshop_goods` 
(`id`, `goods_sn`, `name`, `category_id`, `brand_id`, `gallery`, `keywords`, `goods_brief`, `goods_desc`, `is_on_sale`, `is_delete`, `sort_order`, `create_time`, `update_time`, `retail_price`, `market_price`, `share_url`, `is_new`, `is_hot`, `unit_price`, `promotion_desc`, `promotion_tag`, `app_exclusive_price`, `app_exclusive_price_start`, `app_exclusive_price_end`, `counter_price`, `extra_price`, `primary_pic_url`, `list_pic_url`, `goods_number`, `detail_tag`)
VALUES 
(2, 'DEMO_NORMAL_001', '普通推广商品 - 蓝牙耳机', 1, 1, 
 '["https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400"]', 
 '蓝牙耳机,普通推广', 
 '高品质蓝牙耳机，音质清晰，佩戴舒适。普通推广商品，分享返现10%。', 
 '<p><strong>产品特色：</strong></p><ul><li>🎵 HiFi音质，享受纯净音乐</li><li>🔋 长续航设计，持久陪伴</li><li>📱 快速连接，稳定传输</li><li>💧 防汗防水，运动无忧</li></ul>', 
 1, 0, 90, NOW(), NOW(), 99.00, 199.00, '', 0, 0, 99.00, 
 '普通推广商品，分享返现10%', '热销', 99.00, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), 199.00, 0.00, 
 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400', 
 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=300', 
 1000, '热销推荐');

-- 7. 创建普通商品规格
INSERT IGNORE INTO `weshop_product` 
(`id`, `goods_id`, `goods_specification_ids`, `goods_sn`, `retail_price`, `market_price`, `goods_number`, `primary_pic_url`)
VALUES 
(2, 2, '', 'DEMO_NORMAL_001_DEFAULT', 99.00, 199.00, 1000, 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400');

-- 8. 查看演示数据
SELECT '=== 演示商品信息 ===' as info;
SELECT 
    g.id, g.name, g.retail_price,
    CASE WHEN tpg.goods_id IS NOT NULL THEN '阶梯推广商品' ELSE '普通商品' END as promotion_type,
    CASE WHEN tpg.goods_id IS NOT NULL THEN CONCAT(tpg.tier1_rate, '%/', tpg.tier2_rate, '%/', tpg.tier3_rate, '%/', tpg.tier4_plus_rate, '%') ELSE '10%' END as commission_rates
FROM `weshop_goods` g
LEFT JOIN `weshop_tiered_promotion_goods` tpg ON g.id = tpg.goods_id AND tpg.is_active = 1
WHERE g.id IN (1, 2)
ORDER BY g.id;

SELECT '=== 阶梯推广配置 ===' as info;
SELECT * FROM `weshop_tiered_promotion_goods` WHERE `goods_id` = 1;

SELECT '=== 演示用户推广统计 ===' as info;
SELECT 
    ugps.*,
    u.nickname as promoter_nickname
FROM `weshop_user_goods_promotion_stats` ugps
LEFT JOIN `weshop_user` u ON ugps.promoter_id = u.id
WHERE ugps.promoter_id = 999 AND ugps.goods_id = 1;

-- 9. 测试说明
SELECT '=== 测试说明 ===' as info;
SELECT '
测试步骤：
1. 访问商品ID为1的商品页面 - 应该显示阶梯推广提示
2. 访问商品ID为2的商品页面 - 应该不显示阶梯推广提示
3. 使用用户ID为999的账号登录 - 应该显示个人推广统计
4. 点击"计算预期佣金"按钮 - 应该显示第3笔订单50%的返现比例

预期效果：
- 商品1：显示金黄色阶梯推广提示卡片，包含完整的规则说明
- 商品2：不显示阶梯推广提示，正常显示商品信息
- 登录用户999：显示已推广2笔，总佣金149.70元，下一笔为第3笔50%返现
' as test_instructions;

-- 10. 清理演示数据的脚本（注释掉，需要时手动执行）
/*
-- 清理演示数据
DELETE FROM `weshop_user_goods_promotion_stats` WHERE `promoter_id` = 999;
DELETE FROM `weshop_product` WHERE `goods_id` IN (1, 2);
DELETE FROM `weshop_tiered_promotion_goods` WHERE `goods_id` = 1;
DELETE FROM `weshop_goods` WHERE `id` IN (1, 2);
DELETE FROM `weshop_user` WHERE `id` = 999;
*/