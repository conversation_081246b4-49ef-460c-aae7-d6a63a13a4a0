-- 商品限购功能测试数据
-- 测试商品限购逻辑的各种场景

-- 1. 创建测试用户
INSERT IGNORE INTO `weshop_user` 
(`id`, `username`, `nickname`, `password`, `wechat_open_id`, `mobile`, `avatar`, `gender`, `register_time`, `register_ip`, `last_login_time`, `last_login_ip`, `promotion_code`)
VALUES 
(8001, 'limit_test_user1', '限购测试用户1', '', 'wx_limit_test_user1', '13800008001', 'https://example.com/avatar1.jpg', 1, NOW(), '127.0.0.1', NOW(), '127.0.0.1', 'limit_8001'),
(8002, 'limit_test_user2', '限购测试用户2', '', 'wx_limit_test_user2', '13800008002', 'https://example.com/avatar2.jpg', 1, NOW(), '127.0.0.1', NOW(), '127.0.0.1', 'limit_8002'),
(8003, 'limit_test_user3', '限购测试用户3', '', 'wx_limit_test_user3', '13800008003', 'https://example.com/avatar3.jpg', 1, NOW(), '127.0.0.1', NOW(), '127.0.0.1', 'limit_8003');

-- 2. 创建测试商品
-- 限购商品1：限购3件
INSERT IGNORE INTO `weshop_goods` 
(`id`, `goods_sn`, `name`, `category_id`, `brand_id`, `gallery`, `keywords`, `goods_brief`, `goods_desc`, `is_on_sale`, `is_delete`, `sort_order`, `create_time`, `update_time`, `retail_price`, `market_price`, `share_url`, `is_new`, `is_hot`, `unit_price`, `promotion_desc`, `promotion_tag`, `app_exclusive_price`, `app_exclusive_price_start`, `app_exclusive_price_end`, `counter_price`, `extra_price`, `primary_pic_url`, `list_pic_url`, `goods_number`, `detail_tag`, `is_limited`, `limit_num`)
VALUES 
(8001, 'LIMIT_TEST_001', '限购商品1（限3件）', 1, 1, '["https://example.com/limit1.jpg"]', '限购,测试', '每人限购3件的测试商品', '<p>限购商品详情</p>', 1, 0, 100, NOW(), NOW(), 100.00, 150.00, '', 1, 1, 100.00, '限购商品', '限购', 100.00, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), 150.00, 0.00, 'https://example.com/limit1.jpg', 'https://example.com/limit1.jpg', 1000, '限购', 1, 3);

-- 限购商品2：限购1件
INSERT IGNORE INTO `weshop_goods` 
(`id`, `goods_sn`, `name`, `category_id`, `brand_id`, `gallery`, `keywords`, `goods_brief`, `goods_desc`, `is_on_sale`, `is_delete`, `sort_order`, `create_time`, `update_time`, `retail_price`, `market_price`, `share_url`, `is_new`, `is_hot`, `unit_price`, `promotion_desc`, `promotion_tag`, `app_exclusive_price`, `app_exclusive_price_start`, `app_exclusive_price_end`, `counter_price`, `extra_price`, `primary_pic_url`, `list_pic_url`, `goods_number`, `detail_tag`, `is_limited`, `limit_num`)
VALUES 
(8002, 'LIMIT_TEST_002', '限购商品2（限1件）', 1, 1, '["https://example.com/limit2.jpg"]', '限购,测试', '每人限购1件的测试商品', '<p>限购商品详情</p>', 1, 0, 100, NOW(), NOW(), 200.00, 300.00, '', 1, 1, 200.00, '限购商品', '限购', 200.00, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), 300.00, 0.00, 'https://example.com/limit2.jpg', 'https://example.com/limit2.jpg', 1000, '限购', 1, 1);

-- 非限购商品
INSERT IGNORE INTO `weshop_goods` 
(`id`, `goods_sn`, `name`, `category_id`, `brand_id`, `gallery`, `keywords`, `goods_brief`, `goods_desc`, `is_on_sale`, `is_delete`, `sort_order`, `create_time`, `update_time`, `retail_price`, `market_price`, `share_url`, `is_new`, `is_hot`, `unit_price`, `promotion_desc`, `promotion_tag`, `app_exclusive_price`, `app_exclusive_price_start`, `app_exclusive_price_end`, `counter_price`, `extra_price`, `primary_pic_url`, `list_pic_url`, `goods_number`, `detail_tag`, `is_limited`, `limit_num`)
VALUES 
(8003, 'NORMAL_TEST_001', '普通商品（无限购）', 1, 1, '["https://example.com/normal1.jpg"]', '普通,测试', '无限购的普通商品', '<p>普通商品详情</p>', 1, 0, 100, NOW(), NOW(), 50.00, 80.00, '', 1, 0, 50.00, '普通商品', '普通', 50.00, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), 80.00, 0.00, 'https://example.com/normal1.jpg', 'https://example.com/normal1.jpg', 1000, '普通', 0, NULL);

-- 3. 创建商品规格
INSERT IGNORE INTO `weshop_product` 
(`id`, `goods_id`, `goods_specification_ids`, `goods_sn`, `retail_price`, `market_price`, `goods_number`, `primary_pic_url`)
VALUES 
(8001, 8001, '', 'LIMIT_TEST_001_DEFAULT', 100.00, 150.00, 1000, 'https://example.com/limit1.jpg'),
(8002, 8002, '', 'LIMIT_TEST_002_DEFAULT', 200.00, 300.00, 1000, 'https://example.com/limit2.jpg'),
(8003, 8003, '', 'NORMAL_TEST_001_DEFAULT', 50.00, 80.00, 1000, 'https://example.com/normal1.jpg');

-- 4. 创建测试地址
INSERT IGNORE INTO `weshop_address` 
(`id`, `name`, `user_id`, `province_id`, `city_id`, `district_id`, `address`, `mobile`, `is_default`)
VALUES 
(8001, '测试地址1', 8001, 110000, 110100, 110101, '测试街道1号', '13800008001', 1),
(8002, '测试地址2', 8002, 110000, 110100, 110101, '测试街道2号', '13800008002', 1),
(8003, '测试地址3', 8003, 110000, 110100, 110101, '测试街道3号', '13800008003', 1);

-- 5. 创建历史购买记录
-- 用户8001已购买限购商品1两次，共2件
INSERT IGNORE INTO `weshop_order` 
(`id`, `order_sn`, `user_id`, `order_status`, `pay_status`, `consignee`, `mobile`, `address`, `goods_price`, `freight_price`, `coupon_price`, `integral`, `integral_money`, `balance_price`, `order_price`, `actual_price`, `pay_time`, `create_time`, `update_time`)
VALUES 
(8001, 'LIMIT_ORDER_001', 8001, 'WAIT_SEND', 'PAID', '限购测试用户1', '13800008001', '测试地址', 100.00, 0.00, 0.00, 0, 0.00, 0.00, 100.00, 100.00, DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_SUB(NOW(), INTERVAL 2 DAY), NOW()),
(8002, 'LIMIT_ORDER_002', 8001, 'WAIT_SEND', 'PAID', '限购测试用户1', '13800008001', '测试地址', 100.00, 0.00, 0.00, 0, 0.00, 0.00, 100.00, 100.00, DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY), NOW());

INSERT IGNORE INTO `weshop_order_goods` 
(`id`, `order_id`, `goods_id`, `goods_sn`, `product_id`, `goods_name`, `list_pic_url`, `market_price`, `retail_price`, `number`, `goods_specification_name_value`, `goods_specification_ids`, `is_real`)
VALUES 
(8001, 8001, 8001, 'LIMIT_TEST_001', 8001, '限购商品1（限3件）', 'https://example.com/limit1.jpg', 150.00, 100.00, 1, '', '', 1),
(8002, 8002, 8001, 'LIMIT_TEST_001', 8001, '限购商品1（限3件）', 'https://example.com/limit1.jpg', 150.00, 100.00, 1, '', '', 1);

-- 用户8002已购买限购商品2一次，共1件（已达上限）
INSERT IGNORE INTO `weshop_order` 
(`id`, `order_sn`, `user_id`, `order_status`, `pay_status`, `consignee`, `mobile`, `address`, `goods_price`, `freight_price`, `coupon_price`, `integral`, `integral_money`, `balance_price`, `order_price`, `actual_price`, `pay_time`, `create_time`, `update_time`)
VALUES 
(8003, 'LIMIT_ORDER_003', 8002, 'WAIT_SEND', 'PAID', '限购测试用户2', '13800008002', '测试地址', 200.00, 0.00, 0.00, 0, 0.00, 0.00, 200.00, 200.00, DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY), NOW());

INSERT IGNORE INTO `weshop_order_goods` 
(`id`, `order_id`, `goods_id`, `goods_sn`, `product_id`, `goods_name`, `list_pic_url`, `market_price`, `retail_price`, `number`, `goods_specification_name_value`, `goods_specification_ids`, `is_real`)
VALUES 
(8003, 8003, 8002, 'LIMIT_TEST_002', 8002, '限购商品2（限1件）', 'https://example.com/limit2.jpg', 300.00, 200.00, 1, '', '', 1);

-- 6. 测试限购检查逻辑
SELECT '=== 商品限购功能测试 ===' as info;

-- 测试场景1：用户8001对限购商品1的购买状态（已购2件，限购3件，还可购买1件）
SELECT 
    '用户8001 - 限购商品1购买状态' as test_case,
    COUNT(*) as purchased_count,
    3 as limit_num,
    (3 - COUNT(*)) as remaining_count,
    CASE WHEN COUNT(*) < 3 THEN '可以购买' ELSE '已达上限' END as purchase_status
FROM weshop_order o 
INNER JOIN weshop_order_goods og ON o.id = og.order_id 
WHERE o.user_id = 8001 
AND og.goods_id = 8001 
AND o.pay_status = 'PAID' 
AND o.order_status != 'CANCELLED';

-- 测试场景2：用户8002对限购商品2的购买状态（已购1件，限购1件，已达上限）
SELECT 
    '用户8002 - 限购商品2购买状态' as test_case,
    COUNT(*) as purchased_count,
    1 as limit_num,
    (1 - COUNT(*)) as remaining_count,
    CASE WHEN COUNT(*) < 1 THEN '可以购买' ELSE '已达上限' END as purchase_status
FROM weshop_order o 
INNER JOIN weshop_order_goods og ON o.id = og.order_id 
WHERE o.user_id = 8002 
AND og.goods_id = 8002 
AND o.pay_status = 'PAID' 
AND o.order_status != 'CANCELLED';

-- 测试场景3：用户8003对所有商品的购买状态（新用户，无购买记录）
SELECT 
    '用户8003 - 新用户购买状态' as test_case,
    '限购商品1' as goods_name,
    0 as purchased_count,
    3 as limit_num,
    3 as remaining_count,
    '可以购买' as purchase_status
UNION ALL
SELECT 
    '用户8003 - 新用户购买状态' as test_case,
    '限购商品2' as goods_name,
    0 as purchased_count,
    1 as limit_num,
    1 as remaining_count,
    '可以购买' as purchase_status
UNION ALL
SELECT 
    '用户8003 - 新用户购买状态' as test_case,
    '普通商品' as goods_name,
    0 as purchased_count,
    NULL as limit_num,
    NULL as remaining_count,
    '无限制' as purchase_status;

-- 7. 验证商品限购配置
SELECT 
    '商品限购配置验证' as test_case,
    id, name, is_limited, limit_num,
    CASE 
        WHEN is_limited = 1 AND limit_num > 0 THEN CONCAT('限购', limit_num, '件')
        ELSE '无限购'
    END as limit_description
FROM weshop_goods 
WHERE id BETWEEN 8001 AND 8003
ORDER BY id;

-- 8. 模拟API响应数据
SELECT '=== API响应模拟 ===' as info;

-- 限购检查API响应（用户8001，限购商品1）
SELECT 
    'checkPurchaseLimit API - 用户8001限购商品1' as api_test,
    JSON_OBJECT(
        'isLimited', true,
        'limitNum', 3,
        'purchasedCount', 2,
        'remainingCount', 1,
        'canPurchase', true,
        'message', '限购3件，您已购买2件，还可购买1件'
    ) as api_response;

-- 限购检查API响应（用户8002，限购商品2）
SELECT 
    'checkPurchaseLimit API - 用户8002限购商品2' as api_test,
    JSON_OBJECT(
        'isLimited', true,
        'limitNum', 1,
        'purchasedCount', 1,
        'remainingCount', 0,
        'canPurchase', false,
        'message', '限购1件，您已购买1件，已达购买上限'
    ) as api_response;

-- 商品限购信息API响应（限购商品1）
SELECT 
    'getGoodsLimitInfo API - 限购商品1' as api_test,
    JSON_OBJECT(
        'isLimited', true,
        'limitNum', 3,
        'canPurchase', true,
        'message', '限购商品，每人限购3件'
    ) as api_response;

-- 商品限购信息API响应（普通商品）
SELECT 
    'getGoodsLimitInfo API - 普通商品' as api_test,
    JSON_OBJECT(
        'isLimited', false,
        'canPurchase', true,
        'message', '非限购商品'
    ) as api_response;

-- 9. 购买数量验证测试
SELECT '=== 购买数量验证测试 ===' as info;

-- 验证用户8001购买限购商品1的各种数量
SELECT 
    '用户8001购买限购商品1数量验证' as validation_test,
    purchase_count,
    CASE 
        WHEN (2 + purchase_count) <= 3 THEN '可以购买'
        ELSE '超出限制'
    END as validation_result
FROM (
    SELECT 1 as purchase_count UNION ALL
    SELECT 2 as purchase_count UNION ALL
    SELECT 3 as purchase_count
) t;

-- 验证用户8002购买限购商品2的各种数量
SELECT 
    '用户8002购买限购商品2数量验证' as validation_test,
    purchase_count,
    CASE 
        WHEN (1 + purchase_count) <= 1 THEN '可以购买'
        ELSE '超出限制'
    END as validation_result
FROM (
    SELECT 1 as purchase_count UNION ALL
    SELECT 2 as purchase_count
) t;

-- 10. 业务场景总结
SELECT '=== 业务场景测试总结 ===' as info;
SELECT '
测试场景覆盖：
1. ✅ 限购商品首次购买（用户8003）
2. ✅ 限购商品部分购买（用户8001已购2/3件）
3. ✅ 限购商品达到上限（用户8002已购1/1件）
4. ✅ 非限购商品无限制购买
5. ✅ 购买数量验证（1件、2件、3件等）
6. ✅ API接口响应格式验证
7. ✅ 数据库查询逻辑验证

业务价值：
- 防止用户恶意囤货
- 保证商品公平分配
- 提升用户购买体验
- 支持营销活动限购策略
' as test_summary;

-- 11. 清理测试数据（注释掉，需要时手动执行）
/*
-- 清理测试数据
DELETE FROM `weshop_order_goods` WHERE `id` BETWEEN 8001 AND 8003;
DELETE FROM `weshop_order` WHERE `id` BETWEEN 8001 AND 8003;
DELETE FROM `weshop_address` WHERE `id` BETWEEN 8001 AND 8003;
DELETE FROM `weshop_product` WHERE `id` BETWEEN 8001 AND 8003;
DELETE FROM `weshop_goods` WHERE `id` BETWEEN 8001 AND 8003;
DELETE FROM `weshop_user` WHERE `id` BETWEEN 8001 AND 8003;
*/