-- 快速测试积分系统

-- 1. 检查积分系统表是否存在
SELECT 
    'weshop_points_record' as table_name,
    COUNT(*) as record_count
FROM `weshop_points_record`
UNION ALL
SELECT 
    'weshop_points_config' as table_name,
    COUNT(*) as record_count
FROM `weshop_points_config`;

-- 2. 检查用户表是否有积分字段
DESCRIBE `weshop_user`;

-- 3. 查看积分配置
SELECT 
    '当前积分配置' as info,
    earn_rate as '消费多少元获得1积分',
    use_rate as '多少积分抵扣1元',
    min_use_points as '最少使用积分',
    max_use_ratio as '最大使用比例(%)',
    is_enabled as '是否启用'
FROM `weshop_points_config` 
WHERE id = 1;

-- 4. 检查是否有用户数据
SELECT 
    '用户积分统计' as info,
    COUNT(*) as total_users,
    COUNT(CASE WHEN points > 0 THEN 1 END) as users_with_points,
    MAX(points) as max_points,
    AVG(points) as avg_points
FROM `weshop_user`;

-- 5. 检查积分记录
SELECT 
    '积分记录统计' as info,
    COUNT(*) as total_records,
    COUNT(CASE WHEN type = 'earn' THEN 1 END) as earn_records,
    COUNT(CASE WHEN type = 'use' THEN 1 END) as use_records,
    SUM(CASE WHEN type = 'earn' THEN points ELSE 0 END) as total_earned,
    SUM(CASE WHEN type = 'use' THEN ABS(points) ELSE 0 END) as total_used
FROM `weshop_points_record`;

-- 6. 如果没有配置，插入默认配置
INSERT IGNORE INTO `weshop_points_config` 
(`earn_rate`, `use_rate`, `min_use_points`, `max_use_ratio`, `is_enabled`) 
VALUES (1.00, 100.00, 100, 100.00, 1);

-- 7. 显示测试结果
SELECT 
    '=== 积分系统测试完成 ===' as result,
    CASE 
        WHEN (SELECT COUNT(*) FROM `weshop_points_config`) > 0 THEN '✓ 积分配置正常'
        ELSE '✗ 积分配置缺失'
    END as config_status,
    CASE 
        WHEN (SELECT is_enabled FROM `weshop_points_config` WHERE id = 1) = 1 THEN '✓ 积分系统已启用'
        ELSE '✗ 积分系统未启用'
    END as system_status;