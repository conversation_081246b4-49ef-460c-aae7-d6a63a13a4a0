-- 用户评论积分奖励功能测试SQL

-- 1. 查看测试用户当前积分
SELECT 
    id,
    nickname,
    points,
    add_time
FROM weshop_user 
WHERE id IN (1, 2, 3)  -- 替换为实际测试用户ID
ORDER BY id;

-- 2. 查看用户积分记录
SELECT 
    id,
    user_id,
    type,
    points,
    source,
    description,
    create_time
FROM weshop_points_record 
WHERE user_id IN (1, 2, 3)  -- 替换为实际测试用户ID
ORDER BY user_id, create_time DESC;

-- 3. 查看用户评论记录
SELECT 
    id,
    user_id,
    value_id as goods_id,
    rating,
    LENGTH(content) as content_length,
    add_time
FROM weshop_comment 
WHERE user_id IN (1, 2, 3)  -- 替换为实际测试用户ID
ORDER BY user_id, add_time DESC;

-- 4. 查看评论图片记录
SELECT 
    cp.id,
    cp.comment_id,
    c.user_id,
    cp.pic_url,
    cp.sort_order,
    c.add_time
FROM weshop_comment_picture cp
JOIN weshop_comment c ON cp.comment_id = c.id
WHERE c.user_id IN (1, 2, 3)  -- 替换为实际测试用户ID
ORDER BY c.user_id, c.add_time DESC;

-- 5. 统计评价积分奖励情况
SELECT 
    user_id,
    COUNT(*) as reward_count,
    SUM(points) as total_reward_points
FROM weshop_points_record 
WHERE description LIKE '%评价奖励%'
GROUP BY user_id
ORDER BY user_id;

-- 6. 查看最近的评价积分奖励记录
SELECT 
    pr.id,
    pr.user_id,
    u.nickname,
    pr.points,
    pr.description,
    pr.create_time
FROM weshop_points_record pr
JOIN weshop_user u ON pr.user_id = u.id
WHERE pr.description LIKE '%评价奖励%'
ORDER BY pr.create_time DESC
LIMIT 20;

-- 7. 验证积分数据一致性
SELECT 
    u.id,
    u.nickname,
    u.points as user_table_points,
    COALESCE(SUM(pr.points), 0) as calculated_points,
    (u.points - COALESCE(SUM(pr.points), 0)) as difference
FROM weshop_user u
LEFT JOIN weshop_points_record pr ON u.id = pr.user_id
WHERE u.id IN (1, 2, 3)  -- 替换为实际测试用户ID
GROUP BY u.id, u.nickname, u.points
HAVING difference != 0;  -- 只显示不一致的记录

-- 8. 创建测试数据（可选）
-- 注意：执行前请确认测试环境，避免影响生产数据

-- 插入测试用户（如果不存在）
INSERT IGNORE INTO weshop_user (id, username, nickname, points, add_time, update_time)
VALUES 
(999, 'test_user_999', '测试用户999', 0, NOW(), NOW()),
(998, 'test_user_998', '测试用户998', 50, NOW(), NOW());

-- 插入测试商品（如果不存在）
INSERT IGNORE INTO weshop_goods (id, goods_name, retail_price, add_time, update_time)
VALUES (999, '测试商品', 99.00, NOW(), NOW());

-- 9. 清理测试数据（测试完成后执行）
-- DELETE FROM weshop_points_record WHERE user_id IN (999, 998) AND description LIKE '%评价奖励%';
-- DELETE FROM weshop_comment_picture WHERE comment_id IN (SELECT id FROM weshop_comment WHERE user_id IN (999, 998));
-- DELETE FROM weshop_comment WHERE user_id IN (999, 998);
-- DELETE FROM weshop_user WHERE id IN (999, 998);
-- DELETE FROM weshop_goods WHERE id = 999;

-- 10. 性能测试查询
-- 查看评价积分奖励的性能统计
SELECT 
    DATE(create_time) as reward_date,
    COUNT(*) as reward_count,
    SUM(points) as total_points
FROM weshop_points_record 
WHERE description LIKE '%评价奖励%'
    AND create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(create_time)
ORDER BY reward_date DESC;