-- 创建会员日提示信息表
CREATE TABLE IF NOT EXISTS `member_day_tip` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tip_text` varchar(500) NOT NULL COMMENT '提示文本内容',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 (0-禁用, 1-启用)',
  `display_position` varchar(50) NOT NULL DEFAULT 'goods' COMMENT '显示位置 (goods-商品页, index-首页, all-全部)',
  `priority` int(11) NOT NULL DEFAULT '1' COMMENT '优先级 (数字越大优先级越高)',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(100) DEFAULT 'system' COMMENT '创建者',
  `update_by` varchar(100) DEFAULT 'system' COMMENT '更新者',
  PRIMARY KEY (`id`),
  KEY `idx_position_enabled` (`display_position`, `is_enabled`),
  KEY `idx_priority` (`priority`),
  KEY `idx_time_range` (`start_time`, `end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员日提示信息表';

-- 插入默认的会员日提示数据
INSERT INTO `member_day_tip` (`tip_text`, `is_enabled`, `display_position`, `priority`, `start_time`, `end_time`, `create_by`, `update_by`) VALUES
('happy星期五，购物嗨到底！会员日购买任意单件商品送500积分', 1, 'goods', 10, NULL, NULL, 'system', 'system'),
('会员专享福利！每周五购物享受额外积分奖励', 1, 'all', 5, NULL, NULL, 'system', 'system'),
('限时优惠！会员日单笔订单满额即送积分大礼包', 0, 'goods', 8, NULL, NULL, 'system', 'system');