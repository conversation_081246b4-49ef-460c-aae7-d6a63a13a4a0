-- 测试积分兑换完整业务流程

-- 1. 创建测试用户（如果不存在）
INSERT IGNORE INTO `weshop_user` 
(`username`, `nickname`, `mobile`, `avatar`, `gender`, `balance`, `points`, `register_time`, `last_login_time`) 
VALUES 
('test_user_points', '积分测试用户', '13800138000', '', 1, 1000.00, 0, NOW(), NOW());

-- 获取测试用户ID
SET @test_user_id = (SELECT id FROM `weshop_user` WHERE username = 'test_user_points' LIMIT 1);

-- 2. 模拟用户购物100元，获得100积分
INSERT INTO `weshop_points_record` 
(`user_id`, `type`, `points`, `source`, `source_id`, `description`, `create_time`, `update_time`)
VALUES 
(@test_user_id, 'earn', 100, 'order', 1001, '订单消费100元获得100积分', NOW(), NOW());

-- 更新用户积分
UPDATE `weshop_user` 
SET `points` = (
    SELECT COALESCE(SUM(points), 0) 
    FROM `weshop_points_record` 
    WHERE `user_id` = @test_user_id
)
WHERE `id` = @test_user_id;

-- 3. 模拟用户再次购物200元，获得200积分
INSERT INTO `weshop_points_record` 
(`user_id`, `type`, `points`, `source`, `source_id`, `description`, `create_time`, `update_time`)
VALUES 
(@test_user_id, 'earn', 200, 'order', 1002, '订单消费200元获得200积分', NOW(), NOW());

-- 更新用户积分
UPDATE `weshop_user` 
SET `points` = (
    SELECT COALESCE(SUM(points), 0) 
    FROM `weshop_points_record` 
    WHERE `user_id` = @test_user_id
)
WHERE `id` = @test_user_id;

-- 4. 模拟用户使用100积分抵扣1元
INSERT INTO `weshop_points_record` 
(`user_id`, `type`, `points`, `source`, `source_id`, `description`, `create_time`, `update_time`)
VALUES 
(@test_user_id, 'use', -100, 'order', 1003, '订单使用100积分抵扣1元', NOW(), NOW());

-- 更新用户积分
UPDATE `weshop_user` 
SET `points` = (
    SELECT COALESCE(SUM(points), 0) 
    FROM `weshop_points_record` 
    WHERE `user_id` = @test_user_id
)
WHERE `id` = @test_user_id;

-- 5. 验证结果
SELECT 
    '=== 用户积分信息 ===' as info,
    u.id as user_id,
    u.username,
    u.points as current_points,
    u.balance as current_balance,
    CASE 
        WHEN u.points >= 100 THEN CONCAT('可抵扣 ', FLOOR(u.points/100), ' 元')
        ELSE '积分不足，无法抵扣'
    END as deduction_info
FROM `weshop_user` u 
WHERE u.id = @test_user_id;

-- 6. 查看积分明细
SELECT 
    '=== 积分明细 ===' as info,
    pr.id,
    pr.type,
    pr.points,
    pr.source,
    pr.description,
    pr.create_time
FROM `weshop_points_record` pr 
WHERE pr.user_id = @test_user_id 
ORDER BY pr.create_time DESC;

-- 7. 验证积分配置
SELECT 
    '=== 积分配置 ===' as info,
    pc.earn_rate as '消费多少元获得1积分',
    pc.use_rate as '多少积分抵扣1元',
    pc.min_use_points as '最少使用积分',
    pc.max_use_ratio as '最大使用比例(%)',
    pc.is_enabled as '是否启用'
FROM `weshop_points_config` pc 
WHERE pc.id = 1;

-- 8. 计算示例
SELECT 
    '=== 计算示例 ===' as info,
    '购物100元' as scenario,
    '获得100积分' as earn_result,
    '100积分可抵扣1元' as use_result,
    '实际支付99元' as final_payment;

-- 清理测试数据（可选，取消注释以清理）
-- DELETE FROM `weshop_points_record` WHERE user_id = @test_user_id;
-- DELETE FROM `weshop_user` WHERE id = @test_user_id;