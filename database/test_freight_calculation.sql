-- 特定地区运费加收功能测试数据
-- 用于测试港、澳、台、新疆、西藏、海南地区5元运费加收功能

-- 1. 查看当前地区数据
SELECT id, name, type FROM weshop_region WHERE type = 1 AND name IN ('香港', '澳门', '台湾', '新疆', '西藏', '海南');

-- 2. 查看特殊地区的完整名称（可能包含"省"、"自治区"等后缀）
SELECT id, name, type FROM weshop_region 
WHERE type = 1 AND (
    name LIKE '%香港%' OR 
    name LIKE '%澳门%' OR 
    name LIKE '%台湾%' OR 
    name LIKE '%新疆%' OR 
    name LIKE '%西藏%' OR 
    name LIKE '%海南%'
);

-- 3. 创建测试用户（如果不存在）
INSERT IGNORE INTO weshop_user (id, username, password, gender, birthday, last_login_time, last_login_ip, user_level_id, nickname, mobile, avatar, weixin_openid, session_key, status, create_time, update_time, balance, points)
VALUES (9999, 'test_freight_user', 'test123', 1, '1990-01-01', NOW(), '127.0.0.1', 1, '运费测试用户', '13800138000', '', 'test_openid_freight', 'test_session', 1, NOW(), NOW(), 1000.00, 5000);

-- 4. 创建特殊地区测试地址
-- 新疆地址
INSERT IGNORE INTO weshop_address (id, name, user_id, country_id, province_id, city_id, district_id, address, mobile, is_default)
SELECT 9991, '张三', 9999, 1, r.id, 0, 0, '乌鲁木齐市天山区解放南路123号', '13800138001', 0
FROM weshop_region r WHERE r.name LIKE '%新疆%' AND r.type = 1 LIMIT 1;

-- 西藏地址
INSERT IGNORE INTO weshop_address (id, name, user_id, country_id, province_id, city_id, district_id, address, mobile, is_default)
SELECT 9992, '李四', 9999, 1, r.id, 0, 0, '拉萨市城关区北京中路456号', '13800138002', 0
FROM weshop_region r WHERE r.name LIKE '%西藏%' AND r.type = 1 LIMIT 1;

-- 海南地址
INSERT IGNORE INTO weshop_address (id, name, user_id, country_id, province_id, city_id, district_id, address, mobile, is_default)
SELECT 9993, '王五', 9999, 1, r.id, 0, 0, '海口市龙华区国贸大道789号', '13800138003', 0
FROM weshop_region r WHERE r.name LIKE '%海南%' AND r.type = 1 LIMIT 1;

-- 香港地址
INSERT IGNORE INTO weshop_address (id, name, user_id, country_id, province_id, city_id, district_id, address, mobile, is_default)
SELECT 9994, '陈六', 9999, 1, r.id, 0, 0, '香港岛中环皇后大道中100号', '13800138004', 0
FROM weshop_region r WHERE r.name LIKE '%香港%' AND r.type = 1 LIMIT 1;

-- 澳门地址
INSERT IGNORE INTO weshop_address (id, name, user_id, country_id, province_id, city_id, district_id, address, mobile, is_default)
SELECT 9995, '赵七', 9999, 1, r.id, 0, 0, '澳门半岛新马路200号', '13800138005', 0
FROM weshop_region r WHERE r.name LIKE '%澳门%' AND r.type = 1 LIMIT 1;

-- 台湾地址
INSERT IGNORE INTO weshop_address (id, name, user_id, country_id, province_id, city_id, district_id, address, mobile, is_default)
SELECT 9996, '孙八', 9999, 1, r.id, 0, 0, '台北市信义区市府路300号', '13800138006', 0
FROM weshop_region r WHERE r.name LIKE '%台湾%' AND r.type = 1 LIMIT 1;

-- 5. 创建普通地区测试地址
-- 北京地址
INSERT IGNORE INTO weshop_address (id, name, user_id, country_id, province_id, city_id, district_id, address, mobile, is_default)
SELECT 9997, '普通用户1', 9999, 1, r.id, 0, 0, '北京市朝阳区建国门外大街400号', '13800138007', 1
FROM weshop_region r WHERE r.name = '北京' AND r.type = 1 LIMIT 1;

-- 上海地址
INSERT IGNORE INTO weshop_address (id, name, user_id, country_id, province_id, city_id, district_id, address, mobile, is_default)
SELECT 9998, '普通用户2', 9999, 1, r.id, 0, 0, '上海市浦东新区陆家嘴环路500号', '13800138008', 0
FROM weshop_region r WHERE r.name = '上海' AND r.type = 1 LIMIT 1;

-- 广东地址
INSERT IGNORE INTO weshop_address (id, name, user_id, country_id, province_id, city_id, district_id, address, mobile, is_default)
SELECT 9999, '普通用户3', 9999, 1, r.id, 0, 0, '广东省深圳市南山区深南大道600号', '13800138009', 0
FROM weshop_region r WHERE r.name = '广东' AND r.type = 1 LIMIT 1;

-- 6. 查看创建的测试地址
SELECT 
    a.id,
    a.name,
    a.mobile,
    r.name as province_name,
    a.address,
    a.is_default
FROM weshop_address a
LEFT JOIN weshop_region r ON a.province_id = r.id
WHERE a.user_id = 9999
ORDER BY a.id;

-- 7. 创建测试商品（如果不存在）
INSERT IGNORE INTO weshop_goods (id, category_id, goods_sn, name, brand_id, goods_number, keywords, goods_brief, goods_desc, is_on_sale, create_time, update_time, is_delete, attribute_category, counter_price, extra_price, is_new, goods_unit, list_pic_url, retail_price, sell_volume, primary_product_id, unit_price, promotion_desc, promotion_tag, app_exclusive_price, is_app_exclusive, is_limited, is_hot)
VALUES (9999, 1, 'TEST_FREIGHT_001', '运费测试商品', 1, 1000, '测试,运费', '用于测试运费计算的商品', '<p>运费测试商品详情</p>', 1, NOW(), NOW(), 0, '', 150.00, 0.00, 1, '件', '/static/images/test.jpg', 100.00, 0, 1, 100.00, '', '', 0.00, 0, 0, 0);

-- 8. 创建测试商品规格
INSERT IGNORE INTO weshop_product (id, goods_id, goods_specification_ids, goods_sn, retail_price, counter_price, goods_number)
VALUES (9999, 9999, '', 'TEST_FREIGHT_001', 100.00, 150.00, 1000);

-- 9. 创建测试优惠券
INSERT IGNORE INTO weshop_coupon_config (id, title, description, tag, total, discount, min, len, type, status, create_time, update_time, deleted, start_time, end_time, amount)
VALUES (9999, '运费测试优惠券', '用于测试运费计算的优惠券', '测试', 1000, 0, 50.00, 30, 1, 1, NOW(), NOW(), 0, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), 10.00);

-- 10. 为测试用户发放优惠券
INSERT IGNORE INTO weshop_user_coupon (id, user_id, coupon_id, status, used_time, start_time, end_time, order_id, create_time, update_time, title, amount, min_amount, description)
SELECT 9999, 9999, 9999, 0, NULL, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), NULL, NOW(), NOW(), '运费测试优惠券', 10.00, 50.00, '用于测试运费计算的优惠券'
WHERE NOT EXISTS (SELECT 1 FROM weshop_user_coupon WHERE id = 9999);

-- 11. 测试查询：验证特殊地区地址
SELECT 
    '特殊地区地址测试' as test_type,
    a.id as address_id,
    a.name as recipient_name,
    r.name as province_name,
    a.address,
    CASE 
        WHEN r.name LIKE '%香港%' OR r.name LIKE '%澳门%' OR r.name LIKE '%台湾%' OR 
             r.name LIKE '%新疆%' OR r.name LIKE '%西藏%' OR r.name LIKE '%海南%' 
        THEN '5.00' 
        ELSE '0.00' 
    END as expected_freight
FROM weshop_address a
LEFT JOIN weshop_region r ON a.province_id = r.id
WHERE a.user_id = 9999 AND a.id BETWEEN 9991 AND 9996;

-- 12. 测试查询：验证普通地区地址
SELECT 
    '普通地区地址测试' as test_type,
    a.id as address_id,
    a.name as recipient_name,
    r.name as province_name,
    a.address,
    CASE 
        WHEN r.name LIKE '%香港%' OR r.name LIKE '%澳门%' OR r.name LIKE '%台湾%' OR 
             r.name LIKE '%新疆%' OR r.name LIKE '%西藏%' OR r.name LIKE '%海南%' 
        THEN '5.00' 
        ELSE '0.00' 
    END as expected_freight
FROM weshop_address a
LEFT JOIN weshop_region r ON a.province_id = r.id
WHERE a.user_id = 9999 AND a.id BETWEEN 9997 AND 9999;

-- 13. 模拟订单运费计算测试
SELECT 
    '订单运费计算模拟' as test_type,
    a.id as address_id,
    r.name as province_name,
    100.00 as goods_total_price,
    CASE 
        WHEN r.name LIKE '%香港%' OR r.name LIKE '%澳门%' OR r.name LIKE '%台湾%' OR 
             r.name LIKE '%新疆%' OR r.name LIKE '%西藏%' OR r.name LIKE '%海南%' 
        THEN 5.00 
        ELSE 0.00 
    END as freight_price,
    (100.00 + CASE 
        WHEN r.name LIKE '%香港%' OR r.name LIKE '%澳门%' OR r.name LIKE '%台湾%' OR 
             r.name LIKE '%新疆%' OR r.name LIKE '%西藏%' OR r.name LIKE '%海南%' 
        THEN 5.00 
        ELSE 0.00 
    END) as total_price
FROM weshop_address a
LEFT JOIN weshop_region r ON a.province_id = r.id
WHERE a.user_id = 9999
ORDER BY a.id;

-- 14. 清理测试数据的脚本（可选执行）
/*
-- 清理测试地址
DELETE FROM weshop_address WHERE user_id = 9999;

-- 清理测试用户优惠券
DELETE FROM weshop_user_coupon WHERE user_id = 9999;

-- 清理测试优惠券配置
DELETE FROM weshop_coupon_config WHERE id = 9999;

-- 清理测试商品规格
DELETE FROM weshop_product WHERE id = 9999;

-- 清理测试商品
DELETE FROM weshop_goods WHERE id = 9999;

-- 清理测试用户
DELETE FROM weshop_user WHERE id = 9999;
*/

-- 15. 验证地区数据完整性
SELECT 
    '地区数据验证' as check_type,
    COUNT(*) as total_provinces,
    SUM(CASE WHEN name LIKE '%香港%' OR name LIKE '%澳门%' OR name LIKE '%台湾%' OR 
                  name LIKE '%新疆%' OR name LIKE '%西藏%' OR name LIKE '%海南%' 
             THEN 1 ELSE 0 END) as special_regions_count
FROM weshop_region 
WHERE type = 1;

-- 16. 查看所有省级地区，便于确认特殊地区名称
SELECT id, name, type 
FROM weshop_region 
WHERE type = 1 
ORDER BY name;