-- 测试快递公司接口功能 - 使用现有的weshop_shipper表

-- 1. 查看快递公司表数据
SELECT * FROM weshop_shipper ORDER BY sort, id;

-- 2. 查看启用的快递公司
SELECT id, shipper_name, shipper_code, short_name, sort 
FROM weshop_shipper 
WHERE enabled = 1 
ORDER BY sort, id;

-- 3. 统计快递公司数量
SELECT 
    COUNT(*) as total_count,
    COUNT(CASE WHEN enabled = 1 THEN 1 END) as enabled_count,
    COUNT(CASE WHEN enabled = 0 THEN 1 END) as disabled_count
FROM weshop_shipper;

-- 4. 查看快递公司编码是否有重复
SELECT shipper_code, COUNT(*) as count 
FROM weshop_shipper 
GROUP BY shipper_code 
HAVING COUNT(*) > 1;

-- 5. 模拟API返回的数据格式
SELECT 
    shipper_name as value,
    shipper_code as code,
    id,
    sort
FROM weshop_shipper 
WHERE enabled = 1 
ORDER BY sort, id;

-- 6. 检查表结构
DESCRIBE weshop_shipper;

-- 7. 如果表中没有数据，插入一些测试数据
INSERT IGNORE INTO weshop_shipper (shipper_name, shipper_code, enabled, sort, add_time) VALUES
('顺丰速运', 'SF', 1, 1, NOW()),
('中通快递', 'ZTO', 1, 2, NOW()),
('圆通速递', 'YTO', 1, 3, NOW()),
('申通快递', 'STO', 1, 4, NOW()),
('韵达速递', 'YD', 1, 5, NOW()),
('百世快递', 'HTKY', 1, 6, NOW()),
('德邦快递', 'DBL', 1, 7, NOW()),
('京东快递', 'JD', 1, 8, NOW()),
('邮政快递包裹', 'YZPY', 1, 9, NOW()),
('EMS', 'EMS', 1, 10, NOW());

-- 8. 验证插入的数据
SELECT COUNT(*) as inserted_count FROM weshop_shipper WHERE enabled = 1;