-- 完整的测试数据脚本：从账户充值到优惠券生成
-- 执行前请确保已经创建了所有必要的表

-- ==========================================
-- 1. 创建测试用户数据
-- ==========================================

-- 清理现有测试数据（可选）
-- DELETE FROM weshop_user_coupon WHERE user_id IN (1, 2, 3);
-- DELETE FROM weshop_recharge_record WHERE user_id IN (1, 2, 3);
-- UPDATE weshop_user SET balance = 0.00 WHERE id IN (1, 2, 3);

-- 确保测试用户存在并初始化余额
INSERT INTO weshop_user (id, username, nickname, mobile, balance, register_time, wechat_open_id) VALUES
(1, 'testuser1', '测试用户1', '13800138001', 0.00, NOW(), 'openid_test_001'),
(2, 'testuser2', '测试用户2', '13800138002', 0.00, NOW(), 'openid_test_002'),
(3, 'testuser3', '测试用户3', '13800138003', 0.00, NOW(), 'openid_test_003')
ON DUPLICATE KEY UPDATE 
    balance = 0.00,
    nickname = VALUES(nickname),
    mobile = VALUES(mobile);

-- ==========================================
-- 2. 模拟充值记录数据
-- ==========================================

-- 用户1：500元充值（应该获得5张10元券）
INSERT INTO weshop_recharge_record (
    user_id, amount, coupon_count, coupon_amount, status, payment_method, 
    wx_order_id, wx_transaction_id, create_time, pay_time, remark
) VALUES (
    1, 500.00, 5, 10.00, 1, 1,
    'RECHARGE_1_' || UNIX_TIMESTAMP(NOW()),
    'wx_trans_' || UNIX_TIMESTAMP(NOW()) || '_001',
    DATE_SUB(NOW(), INTERVAL 2 DAY),
    DATE_SUB(NOW(), INTERVAL 2 DAY),
    '充值500元赠送5张10元消费券'
);

-- 用户1：1000元充值（应该获得5张20元券）
INSERT INTO weshop_recharge_record (
    user_id, amount, coupon_count, coupon_amount, status, payment_method,
    wx_order_id, wx_transaction_id, create_time, pay_time, remark
) VALUES (
    1, 1000.00, 5, 20.00, 1, 1,
    'RECHARGE_1_' || (UNIX_TIMESTAMP(NOW()) + 1),
    'wx_trans_' || (UNIX_TIMESTAMP(NOW()) + 1) || '_002',
    DATE_SUB(NOW(), INTERVAL 1 DAY),
    DATE_SUB(NOW(), INTERVAL 1 DAY),
    '充值1000元赠送5张20元消费券'
);

-- 用户2：500元充值
INSERT INTO weshop_recharge_record (
    user_id, amount, coupon_count, coupon_amount, status, payment_method,
    wx_order_id, wx_transaction_id, create_time, pay_time, remark
) VALUES (
    2, 500.00, 5, 10.00, 1, 1,
    'RECHARGE_2_' || UNIX_TIMESTAMP(NOW()),
    'wx_trans_' || UNIX_TIMESTAMP(NOW()) || '_003',
    DATE_SUB(NOW(), INTERVAL 3 HOUR),
    DATE_SUB(NOW(), INTERVAL 3 HOUR),
    '充值500元赠送5张10元消费券'
);

-- 用户3：待支付的充值记录
INSERT INTO weshop_recharge_record (
    user_id, amount, coupon_count, coupon_amount, status, payment_method,
    wx_order_id, create_time, remark
) VALUES (
    3, 1000.00, 5, 20.00, 0, 1,
    'RECHARGE_3_' || UNIX_TIMESTAMP(NOW()),
    NOW(),
    '充值1000元赠送5张20元消费券'
);

-- ==========================================
-- 3. 更新用户余额（模拟充值成功后的余额增加）
-- ==========================================

UPDATE weshop_user SET balance = 1500.00 WHERE id = 1; -- 用户1充值了500+1000
UPDATE weshop_user SET balance = 500.00 WHERE id = 2;  -- 用户2充值了500
UPDATE weshop_user SET balance = 0.00 WHERE id = 3;    -- 用户3还未支付

-- ==========================================
-- 4. 生成对应的优惠券数据
-- ==========================================

-- 用户1的优惠券：5张10元券（来自500元充值）
INSERT INTO weshop_user_coupon (
    user_id, coupon_id, coupon_number, title, description, type, type_name,
    amount, min_amount, status, create_time, start_time, end_time
) VALUES
-- 10元券 x5
(1, 1, 'RECHARGE_' || UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 2 DAY)) || '_1_001', '10元消费券', '充值500元赠送，满50元可用', 1, '满减券', 10.00, 50.00, 0, DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_ADD(NOW(), INTERVAL 88 DAY)),
(1, 1, 'RECHARGE_' || UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 2 DAY)) || '_1_002', '10元消费券', '充值500元赠送，满50元可用', 1, '满减券', 10.00, 50.00, 0, DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_ADD(NOW(), INTERVAL 88 DAY)),
(1, 1, 'RECHARGE_' || UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 2 DAY)) || '_1_003', '10元消费券', '充值500元赠送，满50元可用', 1, '满减券', 10.00, 50.00, 0, DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_ADD(NOW(), INTERVAL 88 DAY)),
(1, 1, 'RECHARGE_' || UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 2 DAY)) || '_1_004', '10元消费券', '充值500元赠送，满50元可用', 1, '满减券', 10.00, 50.00, 1, DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_ADD(NOW(), INTERVAL 88 DAY)), -- 已使用
(1, 1, 'RECHARGE_' || UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 2 DAY)) || '_1_005', '10元消费券', '充值500元赠送，满50元可用', 1, '满减券', 10.00, 50.00, 0, DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_ADD(NOW(), INTERVAL 88 DAY));

-- 用户1的优惠券：5张20元券（来自1000元充值）
INSERT INTO weshop_user_coupon (
    user_id, coupon_id, coupon_number, title, description, type, type_name,
    amount, min_amount, status, create_time, start_time, end_time
) VALUES
-- 20元券 x5
(1, 2, 'RECHARGE_' || UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 DAY)) || '_1_006', '20元消费券', '充值1000元赠送，满100元可用', 1, '满减券', 20.00, 100.00, 0, DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_ADD(NOW(), INTERVAL 89 DAY)),
(1, 2, 'RECHARGE_' || UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 DAY)) || '_1_007', '20元消费券', '充值1000元赠送，满100元可用', 1, '满减券', 20.00, 100.00, 0, DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_ADD(NOW(), INTERVAL 89 DAY)),
(1, 2, 'RECHARGE_' || UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 DAY)) || '_1_008', '20元消费券', '充值1000元赠送，满100元可用', 1, '满减券', 20.00, 100.00, 0, DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_ADD(NOW(), INTERVAL 89 DAY)),
(1, 2, 'RECHARGE_' || UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 DAY)) || '_1_009', '20元消费券', '充值1000元赠送，满100元可用', 1, '满减券', 20.00, 100.00, 0, DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_ADD(NOW(), INTERVAL 89 DAY)),
(1, 2, 'RECHARGE_' || UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 DAY)) || '_1_010', '20元消费券', '充值1000元赠送，满100元可用', 1, '满减券', 20.00, 100.00, 0, DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_ADD(NOW(), INTERVAL 89 DAY));

-- 用户2的优惠券：5张10元券
INSERT INTO weshop_user_coupon (
    user_id, coupon_id, coupon_number, title, description, type, type_name,
    amount, min_amount, status, create_time, start_time, end_time
) VALUES
(2, 1, 'RECHARGE_' || UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 3 HOUR)) || '_2_001', '10元消费券', '充值500元赠送，满50元可用', 1, '满减券', 10.00, 50.00, 0, DATE_SUB(NOW(), INTERVAL 3 HOUR), DATE_SUB(NOW(), INTERVAL 3 HOUR), DATE_ADD(NOW(), INTERVAL 87 DAY)),
(2, 1, 'RECHARGE_' || UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 3 HOUR)) || '_2_002', '10元消费券', '充值500元赠送，满50元可用', 1, '满减券', 10.00, 50.00, 0, DATE_SUB(NOW(), INTERVAL 3 HOUR), DATE_SUB(NOW(), INTERVAL 3 HOUR), DATE_ADD(NOW(), INTERVAL 87 DAY)),
(2, 1, 'RECHARGE_' || UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 3 HOUR)) || '_2_003', '10元消费券', '充值500元赠送，满50元可用', 1, '满减券', 10.00, 50.00, 0, DATE_SUB(NOW(), INTERVAL 3 HOUR), DATE_SUB(NOW(), INTERVAL 3 HOUR), DATE_ADD(NOW(), INTERVAL 87 DAY)),
(2, 1, 'RECHARGE_' || UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 3 HOUR)) || '_2_004', '10元消费券', '充值500元赠送，满50元可用', 1, '满减券', 10.00, 50.00, 2, DATE_SUB(NOW(), INTERVAL 3 HOUR), DATE_SUB(NOW(), INTERVAL 3 HOUR), DATE_SUB(NOW(), INTERVAL 1 DAY)), -- 已过期
(2, 1, 'RECHARGE_' || UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 3 HOUR)) || '_2_005', '10元消费券', '充值500元赠送，满50元可用', 1, '满减券', 10.00, 50.00, 0, DATE_SUB(NOW(), INTERVAL 3 HOUR), DATE_SUB(NOW(), INTERVAL 3 HOUR), DATE_ADD(NOW(), INTERVAL 87 DAY));

-- ==========================================
-- 5. 创建一些历史订单数据（模拟优惠券使用场景）
-- ==========================================

-- 注意：这里假设订单表存在，如果不存在可以注释掉这部分
/*
INSERT INTO weshop_order (
    id, order_sn, user_id, order_status, pay_status, consignee, mobile, address,
    goods_price, freight_price, coupon_price, integral_price, groupon_price, order_price, actual_price,
    pay_time, ship_time, confirm_time, add_time, end_time, deleted
) VALUES
(1001, 'ORDER_' || UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 DAY)), 1, 401, 2, '测试用户1', '13800138001', '测试地址',
 80.00, 0.00, 10.00, 0.00, 0.00, 70.00, 70.00,
 DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY),
 DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY), 0);
*/

-- ==========================================
-- 6. 更新已使用优惠券的使用信息
-- ==========================================

UPDATE weshop_user_coupon 
SET use_time = DATE_SUB(NOW(), INTERVAL 1 DAY), order_id = 1001
WHERE user_id = 1 AND status = 1;

-- ==========================================
-- 7. 验证数据完整性
-- ==========================================

-- 查看用户余额
SELECT id, username, nickname, balance FROM weshop_user WHERE id IN (1, 2, 3);

-- 查看充值记录
SELECT user_id, amount, coupon_count, coupon_amount, status, create_time, pay_time, remark 
FROM weshop_recharge_record 
WHERE user_id IN (1, 2, 3) 
ORDER BY user_id, create_time;

-- 查看优惠券统计
SELECT 
    user_id,
    COUNT(*) as total_coupons,
    SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as available_coupons,
    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as used_coupons,
    SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as expired_coupons,
    SUM(CASE WHEN status = 0 THEN amount ELSE 0 END) as available_amount
FROM weshop_user_coupon 
WHERE user_id IN (1, 2, 3)
GROUP BY user_id
ORDER BY user_id;

-- 查看详细优惠券信息
SELECT 
    user_id, title, amount, min_amount, status,
    CASE 
        WHEN status = 0 THEN '可用'
        WHEN status = 1 THEN '已使用'
        WHEN status = 2 THEN '已过期'
    END as status_text,
    DATE_FORMAT(create_time, '%Y-%m-%d %H:%i') as create_time,
    DATE_FORMAT(end_time, '%Y-%m-%d') as expire_date
FROM weshop_user_coupon 
WHERE user_id IN (1, 2, 3)
ORDER BY user_id, create_time;

-- ==========================================
-- 8. 测试场景说明
-- ==========================================

/*
测试场景说明：

用户1（testuser1）：
- 充值记录：500元 + 1000元 = 1500元
- 账户余额：1500元
- 优惠券：4张可用10元券 + 5张可用20元券 + 1张已使用10元券 = 共10张券
- 可用优惠券总价值：4×10 + 5×20 = 140元

用户2（testuser2）：
- 充值记录：500元
- 账户余额：500元
- 优惠券：4张可用10元券 + 1张过期10元券 = 共5张券
- 可用优惠券总价值：4×10 = 40元

用户3（testuser3）：
- 充值记录：1000元（待支付）
- 账户余额：0元
- 优惠券：0张（因为充值未完成）

测试用例：
1. 用户1购买150元商品 → 可使用20元券（满100元）+ 余额支付
2. 用户1购买80元商品 → 可使用10元券（满50元）+ 余额支付
3. 用户2购买60元商品 → 可使用10元券（满50元）+ 余额支付
4. 用户2购买30元商品 → 无法使用优惠券（不满50元）
5. 用户3 → 需要先完成充值才能获得优惠券
*/

SELECT '测试数据创建完成！' as message,
       '用户1: 1500元余额 + 9张可用券' as user1_summary,
       '用户2: 500元余额 + 4张可用券' as user2_summary,
       '用户3: 0元余额 + 0张券（待充值）' as user3_summary;