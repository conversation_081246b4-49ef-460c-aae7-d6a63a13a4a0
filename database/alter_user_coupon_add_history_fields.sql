-- 在用户优惠券表中添加历史记录字段
-- 用于保存优惠券的使用历史，即使退回后也能查看原始使用信息

ALTER TABLE `weshop_user_coupon` 
ADD COLUMN `last_used_order_id` int(11) DEFAULT NULL COMMENT '最后使用的订单ID（历史记录）' AFTER `order_id`,
ADD COLUMN `last_use_time` datetime DEFAULT NULL COMMENT '最后使用时间（历史记录）' AFTER `use_time`,
ADD COLUMN `is_refunded` tinyint(1) DEFAULT 0 COMMENT '是否已退回：0-未退回，1-已退回' AFTER `last_use_time`,
ADD COLUMN `refund_time` datetime DEFAULT NULL COMMENT '退回时间' AFTER `is_refunded`;

-- 添加索引
ALTER TABLE `weshop_user_coupon` 
ADD INDEX `idx_last_used_order_id` (`last_used_order_id`),
ADD INDEX `idx_is_refunded` (`is_refunded`);

-- 注释说明字段用途
ALTER TABLE `weshop_user_coupon` 
MODIFY COLUMN `order_id` int(11) DEFAULT NULL COMMENT '当前关联订单ID（退回后清空）',
MODIFY COLUMN `use_time` datetime DEFAULT NULL COMMENT '当前使用时间（退回后清空）';
