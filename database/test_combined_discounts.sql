-- 测试积分、余额、优惠券组合使用功能
-- 验证三种抵扣方式可以同时使用且计算正确

-- 设置测试用户ID（请根据实际情况修改）
SET @test_user_id = 1;

-- 1. 检查测试用户的初始状态
SELECT 
    '=== 测试用户初始状态 ===' as step,
    u.id as user_id,
    u.username,
    u.balance as current_balance,
    u.points as current_points
FROM `weshop_user` u 
WHERE u.id = @test_user_id;

-- 2. 为测试用户准备充足的余额和积分
-- 确保用户有足够的余额
UPDATE `weshop_user` 
SET balance = GREATEST(balance, 100.00)
WHERE id = @test_user_id;

-- 确保用户有足够的积分
UPDATE `weshop_user` 
SET points = GREATEST(points, 5000)
WHERE id = @test_user_id;

-- 3. 创建测试优惠券（如果不存在）
INSERT IGNORE INTO `weshop_user_coupon` (`user_id`, `title`, `amount`, `min_amount`, `status`, `start_time`, `end_time`)
VALUES 
(@test_user_id, '组合测试优惠券-20元', 20.00, 50.00, 0, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY)),
(@test_user_id, '组合测试优惠券-30元', 30.00, 80.00, 0, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY)),
(@test_user_id, '组合测试优惠券-50元', 50.00, 100.00, 0, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY));

-- 4. 显示测试用户的完整状态
SELECT 
    '=== 测试用户完整状态 ===' as step,
    u.id as user_id,
    u.username,
    u.balance as current_balance,
    u.points as current_points,
    COUNT(uc.id) as available_coupons
FROM `weshop_user` u
LEFT JOIN `weshop_user_coupon` uc ON uc.user_id = u.id AND uc.status = 0
WHERE u.id = @test_user_id
GROUP BY u.id, u.username, u.balance, u.points;

-- 5. 显示可用优惠券
SELECT 
    '=== 可用优惠券 ===' as step,
    uc.id as coupon_id,
    uc.title,
    uc.amount as coupon_amount,
    uc.min_amount,
    uc.status
FROM `weshop_user_coupon` uc
WHERE uc.user_id = @test_user_id AND uc.status = 0
ORDER BY uc.amount DESC;

-- 6. 模拟不同组合抵扣场景
SELECT 
    '=== 组合抵扣场景测试 ===' as test_scenario,
    scenarios.scenario_name as '测试场景',
    scenarios.order_amount as '订单金额(元)',
    scenarios.coupon_amount as '优惠券抵扣(元)',
    scenarios.points_used as '使用积分',
    scenarios.points_deduction as '积分抵扣(元)',
    scenarios.balance_deduction as '余额抵扣(元)',
    (scenarios.coupon_amount + scenarios.points_deduction + scenarios.balance_deduction) as '总抵扣金额(元)',
    (scenarios.order_amount - scenarios.coupon_amount - scenarios.points_deduction - scenarios.balance_deduction) as '实付金额(元)',
    CASE 
        WHEN (scenarios.coupon_amount + scenarios.points_deduction + scenarios.balance_deduction) <= scenarios.order_amount 
        THEN '✓ 抵扣正常'
        ELSE '✗ 抵扣超额'
    END as '抵扣状态',
    CONCAT(
        CASE WHEN scenarios.coupon_amount > 0 THEN '优惠券 ' ELSE '' END,
        CASE WHEN scenarios.points_deduction > 0 THEN '积分 ' ELSE '' END,
        CASE WHEN scenarios.balance_deduction > 0 THEN '余额 ' ELSE '' END
    ) as '使用的抵扣方式'
FROM (
    -- 场景1：单独使用优惠券
    SELECT '单独使用优惠券' as scenario_name, 100.00 as order_amount, 20.00 as coupon_amount, 0 as points_used, 0.00 as points_deduction, 0.00 as balance_deduction
    UNION ALL
    -- 场景2：单独使用积分
    SELECT '单独使用积分' as scenario_name, 100.00 as order_amount, 0.00 as coupon_amount, 2000 as points_used, 20.00 as points_deduction, 0.00 as balance_deduction
    UNION ALL
    -- 场景3：单独使用余额
    SELECT '单独使用余额' as scenario_name, 100.00 as order_amount, 0.00 as coupon_amount, 0 as points_used, 0.00 as points_deduction, 30.00 as balance_deduction
    UNION ALL
    -- 场景4：优惠券+积分
    SELECT '优惠券+积分' as scenario_name, 100.00 as order_amount, 20.00 as coupon_amount, 2000 as points_used, 20.00 as points_deduction, 0.00 as balance_deduction
    UNION ALL
    -- 场景5：优惠券+余额
    SELECT '优惠券+余额' as scenario_name, 100.00 as order_amount, 20.00 as coupon_amount, 0 as points_used, 0.00 as points_deduction, 30.00 as balance_deduction
    UNION ALL
    -- 场景6：积分+余额
    SELECT '积分+余额' as scenario_name, 100.00 as order_amount, 0.00 as coupon_amount, 2000 as points_used, 20.00 as points_deduction, 30.00 as balance_deduction
    UNION ALL
    -- 场景7：三种组合使用
    SELECT '三种组合使用' as scenario_name, 150.00 as order_amount, 30.00 as coupon_amount, 2000 as points_used, 20.00 as points_deduction, 40.00 as balance_deduction
    UNION ALL
    -- 场景8：三种组合使用（刚好全额抵扣）
    SELECT '三种组合全额抵扣' as scenario_name, 100.00 as order_amount, 50.00 as coupon_amount, 3000 as points_used, 30.00 as points_deduction, 20.00 as balance_deduction
    UNION ALL
    -- 场景9：三种组合使用（超额抵扣，需要调整）
    SELECT '三种组合超额抵扣' as scenario_name, 80.00 as order_amount, 50.00 as coupon_amount, 3000 as points_used, 30.00 as points_deduction, 30.00 as balance_deduction
) scenarios;

-- 7. 按优先级调整后的组合抵扣计算
SELECT 
    '=== 按优先级调整后的组合抵扣 ===' as adjusted_calculation,
    test_data.scenario_name as '场景',
    test_data.order_amount as '订单金额',
    test_data.coupon_amount as '原始优惠券',
    test_data.points_deduction as '原始积分抵扣',
    test_data.balance_deduction as '原始余额抵扣',
    -- 调整后的优惠券（优先级最高，不调整）
    LEAST(test_data.coupon_amount, test_data.order_amount) as '调整后优惠券',
    -- 调整后的积分抵扣
    CASE 
        WHEN test_data.coupon_amount >= test_data.order_amount THEN 0
        WHEN (test_data.coupon_amount + test_data.points_deduction) > test_data.order_amount 
        THEN (test_data.order_amount - test_data.coupon_amount)
        ELSE test_data.points_deduction
    END as '调整后积分抵扣',
    -- 调整后的余额抵扣
    CASE 
        WHEN (test_data.coupon_amount + test_data.points_deduction) >= test_data.order_amount THEN 0
        WHEN (test_data.coupon_amount + test_data.points_deduction + test_data.balance_deduction) > test_data.order_amount 
        THEN GREATEST(0, test_data.order_amount - test_data.coupon_amount - LEAST(test_data.points_deduction, GREATEST(0, test_data.order_amount - test_data.coupon_amount)))
        ELSE test_data.balance_deduction
    END as '调整后余额抵扣',
    -- 最终实付金额
    GREATEST(0, 
        test_data.order_amount - 
        LEAST(test_data.coupon_amount, test_data.order_amount) - 
        CASE 
            WHEN test_data.coupon_amount >= test_data.order_amount THEN 0
            WHEN (test_data.coupon_amount + test_data.points_deduction) > test_data.order_amount 
            THEN (test_data.order_amount - test_data.coupon_amount)
            ELSE test_data.points_deduction
        END -
        CASE 
            WHEN (test_data.coupon_amount + test_data.points_deduction) >= test_data.order_amount THEN 0
            WHEN (test_data.coupon_amount + test_data.points_deduction + test_data.balance_deduction) > test_data.order_amount 
            THEN GREATEST(0, test_data.order_amount - test_data.coupon_amount - LEAST(test_data.points_deduction, GREATEST(0, test_data.order_amount - test_data.coupon_amount)))
            ELSE test_data.balance_deduction
        END
    ) as '最终实付金额'
FROM (
    SELECT '三种组合使用' as scenario_name, 150.00 as order_amount, 30.00 as coupon_amount, 20.00 as points_deduction, 40.00 as balance_deduction
    UNION ALL
    SELECT '三种组合全额抵扣' as scenario_name, 100.00 as order_amount, 50.00 as coupon_amount, 30.00 as points_deduction, 20.00 as balance_deduction
    UNION ALL
    SELECT '三种组合超额抵扣' as scenario_name, 80.00 as order_amount, 50.00 as coupon_amount, 30.00 as points_deduction, 30.00 as balance_deduction
) test_data;

-- 8. 检查实际订单中的组合抵扣使用情况
SELECT 
    '=== 实际订单组合抵扣情况 ===' as real_orders,
    o.id as order_id,
    o.order_sn,
    o.goods_price as '商品金额',
    o.freight_price as '运费',
    (o.goods_price + o.freight_price) as '订单基础金额',
    o.coupon_price as '优惠券抵扣',
    o.integral_money as '积分抵扣',
    o.balance_price as '余额抵扣',
    (o.coupon_price + o.integral_money + o.balance_price) as '总抵扣金额',
    o.actual_price as '实付金额',
    CASE 
        WHEN o.coupon_price > 0 AND o.integral_money > 0 AND o.balance_price > 0 THEN '三种组合'
        WHEN (o.coupon_price > 0 AND o.integral_money > 0) OR (o.coupon_price > 0 AND o.balance_price > 0) OR (o.integral_money > 0 AND o.balance_price > 0) THEN '两种组合'
        WHEN o.coupon_price > 0 OR o.integral_money > 0 OR o.balance_price > 0 THEN '单一抵扣'
        ELSE '无抵扣'
    END as '抵扣类型',
    CASE 
        WHEN (o.coupon_price + o.integral_money + o.balance_price) > (o.goods_price + o.freight_price) 
        THEN '✗ 抵扣超额'
        WHEN (o.goods_price + o.freight_price - o.coupon_price - o.integral_money - o.balance_price) != o.actual_price
        THEN '✗ 计算错误'
        ELSE '✓ 正常'
    END as '状态检查',
    o.create_time
FROM `weshop_order` o
WHERE o.user_id = @test_user_id
ORDER BY o.create_time DESC
LIMIT 10;

-- 9. 组合抵扣统计分析
SELECT 
    '=== 组合抵扣统计分析 ===' as analysis,
    COUNT(*) as '总订单数',
    COUNT(CASE WHEN coupon_price > 0 THEN 1 END) as '使用优惠券订单数',
    COUNT(CASE WHEN integral_money > 0 THEN 1 END) as '使用积分订单数',
    COUNT(CASE WHEN balance_price > 0 THEN 1 END) as '使用余额订单数',
    COUNT(CASE WHEN coupon_price > 0 AND integral_money > 0 AND balance_price > 0 THEN 1 END) as '三种组合订单数',
    COUNT(CASE WHEN (coupon_price > 0 AND integral_money > 0) OR (coupon_price > 0 AND balance_price > 0) OR (integral_money > 0 AND balance_price > 0) THEN 1 END) as '两种组合订单数',
    ROUND(AVG(coupon_price + integral_money + balance_price), 2) as '平均抵扣金额',
    ROUND(MAX(coupon_price + integral_money + balance_price), 2) as '最大抵扣金额'
FROM `weshop_order`
WHERE user_id = @test_user_id;

-- 10. 总结报告
SELECT 
    '=== 组合抵扣功能测试总结 ===' as summary,
    (SELECT balance FROM `weshop_user` WHERE id = @test_user_id) as '用户当前余额',
    (SELECT points FROM `weshop_user` WHERE id = @test_user_id) as '用户当前积分',
    (SELECT COUNT(*) FROM `weshop_user_coupon` WHERE user_id = @test_user_id AND status = 0) as '可用优惠券数量',
    '✓ 支持三种抵扣方式同时使用' as '功能状态',
    '✓ 抵扣金额按优先级自动调整' as '安全保障',
    '✓ 费用明细清晰显示' as '用户体验';
