-- 修复缺失推广者ID的订单脚本
-- 解决订单表中promoter_id为空导致推广佣金计算被跳过的问题

-- ==========================================
-- 1. 诊断当前问题
-- ==========================================

-- 检查最近7天的订单推广者设置情况
SELECT 
    '最近7天订单推广者统计' as check_type,
    COUNT(*) as total_orders,
    COUNT(o.promoter_id) as orders_with_promoter,
    COUNT(u.promoter_id) as users_with_promoter,
    COUNT(*) - COUNT(o.promoter_id) as missing_promoter_orders,
    ROUND(COUNT(o.promoter_id) * 100.0 / COUNT(*), 2) as promoter_set_rate,
    ROUND((COUNT(*) - COUNT(o.promoter_id)) * 100.0 / COUNT(*), 2) as missing_rate
FROM weshop_order o
LEFT JOIN weshop_user u ON o.user_id = u.id
WHERE o.create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY);

-- 查找有推广关系但订单缺少推广者ID的情况
SELECT 
    '需要修复的订单' as check_type,
    o.id as order_id,
    o.order_sn,
    o.user_id,
    o.promoter_id as order_promoter_id,
    u.promoter_id as user_promoter_id,
    o.pay_status,
    o.create_time,
    o.pay_time
FROM weshop_order o
JOIN weshop_user u ON o.user_id = u.id
WHERE o.promoter_id IS NULL 
AND u.promoter_id IS NOT NULL
AND o.create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)  -- 最近30天的订单
ORDER BY o.create_time DESC
LIMIT 20;

-- ==========================================
-- 2. 备份数据（可选）
-- ==========================================

-- 创建订单表备份（可选，如果担心数据安全）
-- CREATE TABLE weshop_order_backup_promoter AS 
-- SELECT id, order_sn, user_id, promoter_id, create_time, pay_time 
-- FROM weshop_order 
-- WHERE create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY);

-- ==========================================
-- 3. 修复缺失的推广者ID
-- ==========================================

-- 修复最近30天内缺失推广者ID的订单
UPDATE weshop_order o
JOIN weshop_user u ON o.user_id = u.id
SET o.promoter_id = u.promoter_id,
    o.update_time = NOW()
WHERE o.promoter_id IS NULL 
AND u.promoter_id IS NOT NULL
AND o.create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 获取修复结果
SELECT ROW_COUNT() as fixed_orders_count;

-- ==========================================
-- 4. 验证修复结果
-- ==========================================

-- 检查修复后的统计
SELECT 
    '修复后统计' as check_type,
    COUNT(*) as total_orders,
    COUNT(o.promoter_id) as orders_with_promoter,
    COUNT(u.promoter_id) as users_with_promoter,
    COUNT(*) - COUNT(o.promoter_id) as remaining_missing_orders,
    ROUND(COUNT(o.promoter_id) * 100.0 / COUNT(*), 2) as promoter_set_rate
FROM weshop_order o
LEFT JOIN weshop_user u ON o.user_id = u.id
WHERE o.create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY);

-- 检查修复的具体订单
SELECT 
    '修复的订单详情' as check_type,
    o.id as order_id,
    o.order_sn,
    o.user_id,
    o.promoter_id,
    o.pay_status,
    o.actual_price,
    o.create_time,
    o.pay_time,
    CASE 
        WHEN o.pay_status = 'PAID' THEN '需要重新计算佣金'
        ELSE '等待支付'
    END as action_needed
FROM weshop_order o
JOIN weshop_user u ON o.user_id = u.id
WHERE o.promoter_id = u.promoter_id  -- 刚刚修复的订单
AND o.create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
AND o.update_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)  -- 最近1小时更新的
ORDER BY o.update_time DESC
LIMIT 10;

-- ==========================================
-- 5. 重新计算已支付订单的推广佣金
-- ==========================================

-- 查找需要重新计算佣金的已支付订单
SELECT 
    '需要重新计算佣金的订单' as check_type,
    o.id as order_id,
    o.order_sn,
    o.user_id,
    o.promoter_id,
    o.actual_price,
    o.promotion_commission,
    o.pay_time,
    CASE 
        WHEN o.promotion_commission IS NULL OR o.promotion_commission = 0 THEN '未计算佣金'
        ELSE '已有佣金记录'
    END as commission_status
FROM weshop_order o
WHERE o.promoter_id IS NOT NULL
AND o.pay_status = 'PAID'
AND o.create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
AND o.update_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)  -- 刚刚修复的订单
AND (o.promotion_commission IS NULL OR o.promotion_commission = 0)  -- 未计算佣金的
ORDER BY o.pay_time DESC;

-- ==========================================
-- 6. 检查推广收益记录
-- ==========================================

-- 检查修复订单的推广收益记录情况
SELECT 
    '推广收益记录检查' as check_type,
    o.id as order_id,
    o.order_sn,
    o.promoter_id,
    pe.id as earnings_id,
    pe.commission_amount,
    pe.status as earnings_status,
    CASE 
        WHEN pe.id IS NULL THEN '缺少收益记录'
        ELSE '有收益记录'
    END as earnings_status_desc
FROM weshop_order o
LEFT JOIN weshop_promotion_earnings pe ON o.id = pe.order_id
WHERE o.promoter_id IS NOT NULL
AND o.pay_status = 'PAID'
AND o.create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
AND o.update_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)  -- 刚刚修复的订单
ORDER BY o.update_time DESC
LIMIT 10;

-- ==========================================
-- 7. 生成修复报告
-- ==========================================

-- 修复前后对比报告
SELECT 
    '修复报告' as report_type,
    '修复前订单推广者设置率' as metric_name,
    CONCAT(
        ROUND(
            (SELECT COUNT(*) FROM weshop_order_backup_promoter WHERE promoter_id IS NOT NULL) * 100.0 / 
            (SELECT COUNT(*) FROM weshop_order_backup_promoter), 2
        ), '%'
    ) as metric_value
WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'weshop_order_backup_promoter')

UNION ALL

SELECT 
    '修复报告' as report_type,
    '修复后订单推广者设置率' as metric_name,
    CONCAT(
        ROUND(
            (SELECT COUNT(*) FROM weshop_order WHERE promoter_id IS NOT NULL AND create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)) * 100.0 / 
            (SELECT COUNT(*) FROM weshop_order WHERE create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)), 2
        ), '%'
    ) as metric_value

UNION ALL

SELECT 
    '修复报告' as report_type,
    '需要重新计算佣金的订单数' as metric_name,
    COUNT(*) as metric_value
FROM weshop_order o
WHERE o.promoter_id IS NOT NULL
AND o.pay_status = 'PAID'
AND o.create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
AND (o.promotion_commission IS NULL OR o.promotion_commission = 0)

UNION ALL

SELECT 
    '修复报告' as report_type,
    '缺少推广收益记录的订单数' as metric_name,
    COUNT(*) as metric_value
FROM weshop_order o
LEFT JOIN weshop_promotion_earnings pe ON o.id = pe.order_id
WHERE o.promoter_id IS NOT NULL
AND o.pay_status = 'PAID'
AND o.create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
AND pe.id IS NULL;

-- ==========================================
-- 8. 后续处理建议
-- ==========================================

/*
修复完成后的后续处理：

1. 重新计算推广佣金：
   对于已支付但未计算佣金的订单，需要调用后端接口重新计算：
   POST /wechat/promotion/debug/recalculate-order/{orderId}

2. 检查推广统计数据：
   运行推广统计数据修复脚本：
   mysql < fix_promotion_stats_data.sql

3. 验证数据一致性：
   运行诊断脚本检查数据一致性：
   mysql < diagnose_promotion_stats_issue.sql

4. 监控后续订单：
   确保新订单的推广者ID正确设置，可以通过以下查询监控：
   
   SELECT 
       COUNT(*) as total_new_orders,
       COUNT(promoter_id) as orders_with_promoter,
       ROUND(COUNT(promoter_id) * 100.0 / COUNT(*), 2) as promoter_rate
   FROM weshop_order 
   WHERE create_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR);

5. 代码优化：
   - 增强推广关系建立的可靠性
   - 优化订单推广者设置逻辑
   - 添加推广关系监控和告警

使用说明：
1. 在生产环境执行前，请先在测试环境验证
2. 建议在业务低峰期执行
3. 执行前做好数据备份
4. 分步执行，每步都检查结果
5. 如果涉及大量数据，考虑分批处理
*/