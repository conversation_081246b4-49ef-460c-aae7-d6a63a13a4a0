-- 创建充值记录表
CREATE TABLE `weshop_recharge_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `amount` decimal(10,2) NOT NULL COMMENT '充值金额',
  `coupon_count` int(11) DEFAULT 0 COMMENT '赠送优惠券数量',
  `coupon_amount` decimal(10,2) DEFAULT 0.00 COMMENT '赠送优惠券面额',
  `status` int(1) NOT NULL DEFAULT 0 COMMENT '充值状态：0-待支付，1-已支付，2-已取消',
  `payment_method` int(1) DEFAULT 1 COMMENT '支付方式：1-微信支付',
  `wx_order_id` varchar(64) DEFAULT NULL COMMENT '微信支付订单号',
  `wx_transaction_id` varchar(64) DEFAULT NULL COMMENT '微信支付交易号',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='充值记录表';