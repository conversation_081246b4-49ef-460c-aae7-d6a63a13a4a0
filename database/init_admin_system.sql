-- 管理员系统初始化脚本
-- 创建管理员表、菜单表和权限表

-- 1. 创建管理员表
CREATE TABLE IF NOT EXISTS `weshop_admin` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(100) NOT NULL COMMENT '密码',
  `password_salt` varchar(20) DEFAULT NULL COMMENT '密码盐值',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `admin_role_id` int(11) DEFAULT NULL COMMENT '管理员角色ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';

-- 2. 创建系统菜单表
CREATE TABLE IF NOT EXISTS `weshop_system_menu` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `pid` int(11) NOT NULL DEFAULT '0' COMMENT '父级菜单ID',
  `path` varchar(255) NOT NULL COMMENT '菜单路径',
  `title` varchar(100) NOT NULL COMMENT '菜单标题',
  `icon` varchar(100) DEFAULT NULL COMMENT '菜单图标',
  `header` varchar(100) DEFAULT NULL COMMENT '头部标识',
  `is_header` tinyint(1) DEFAULT '0' COMMENT '是否为头部菜单',
  `is_show` tinyint(1) DEFAULT '1' COMMENT '是否显示',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_pid` (`pid`),
  KEY `idx_is_show` (`is_show`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统菜单表';

-- 3. 创建菜单权限表
CREATE TABLE IF NOT EXISTS `weshop_menu_permission` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `menu_id` int(11) NOT NULL COMMENT '菜单ID',
  `permission` varchar(100) NOT NULL COMMENT '权限标识',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_menu_id` (`menu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='菜单权限表';

-- 4. 插入默认管理员数据
INSERT INTO `weshop_admin` (`id`, `username`, `password`, `password_salt`, `avatar`, `admin_role_id`) VALUES
(1, 'admin', 'e10adc3949ba59abbe56e057f20f883e', 'salt123', NULL, 1)
ON DUPLICATE KEY UPDATE `username` = VALUES(`username`);

-- 5. 插入系统菜单数据
INSERT INTO `weshop_system_menu` (`id`, `pid`, `path`, `title`, `icon`, `header`, `is_header`, `is_show`, `sort_order`) VALUES
(1, 0, '/admin/index', '主页', 's-home', 'home', 1, 1, 1),
(2, 0, '/admin/product', '商品', 'goods', 'product', 1, 1, 2),
(3, 2, '/admin/product/list', '商品管理', '', 'product', 0, 1, 1),
(4, 2, '/admin/product/category', '商品分类', '', 'product', 0, 1, 2),
(5, 0, '/admin/order', '订单', 's-order', 'order', 1, 1, 3),
(6, 5, '/admin/order/list', '订单管理', '', 'order', 0, 1, 1),
(7, 0, '/admin/user', '用户', 'user-solid', 'user', 1, 1, 4),
(8, 7, '/admin/user/list', '用户管理', '', 'user', 0, 1, 1),
(9, 7, '/admin/user/level', '用户等级', '', 'user', 0, 1, 2),
(10, 0, '/admin/marketing', '营销', 'present', 'marketing', 1, 1, 5),
(11, 10, '/admin/marketing/coupon', '优惠券', '', 'marketing', 0, 1, 1),
(12, 10, '/admin/marketing/promotion', '推广管理', '', 'marketing', 0, 1, 2),
(13, 0, '/admin/finance', '财务', 'money', 'finance', 1, 1, 6),
(14, 13, '/admin/finance/order', '订单统计', '', 'finance', 0, 1, 1),
(15, 13, '/admin/finance/promotion', '推广统计', '', 'finance', 0, 1, 2),
(16, 0, '/admin/setting', '设置', 's-tools', 'setting', 1, 1, 7),
(17, 16, '/admin/setting/system', '系统设置', '', 'setting', 0, 1, 1),
(18, 16, '/admin/setting/admin', '管理员管理', '', 'setting', 0, 1, 2)
ON DUPLICATE KEY UPDATE `title` = VALUES(`title`);

-- 6. 插入菜单权限数据
INSERT INTO `weshop_menu_permission` (`menu_id`, `permission`) VALUES
(1, 'admin-home'),
(2, 'admin-product'),
(3, 'admin-product-list'),
(4, 'admin-product-category'),
(5, 'admin-order'),
(6, 'admin-order-list'),
(7, 'admin-user'),
(8, 'admin-user-list'),
(9, 'admin-user-level'),
(10, 'admin-marketing'),
(11, 'admin-marketing-coupon'),
(12, 'admin-marketing-promotion'),
(13, 'admin-finance'),
(14, 'admin-finance-order'),
(15, 'admin-finance-promotion'),
(16, 'admin-setting'),
(17, 'admin-setting-system'),
(18, 'admin-setting-admin')
ON DUPLICATE KEY UPDATE `permission` = VALUES(`permission`);

-- 说明：
-- 1. 默认管理员账号：admin，密码：123456（MD5加密后：e10adc3949ba59abbe56e057f20f883e）
-- 2. 菜单结构为树形结构，pid=0的为根菜单
-- 3. 权限表关联菜单，每个菜单都有对应的权限标识