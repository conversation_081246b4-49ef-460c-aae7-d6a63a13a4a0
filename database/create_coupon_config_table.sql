-- 创建优惠券配置表
CREATE TABLE IF NOT EXISTS `weshop_coupon_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `coupon_type` varchar(50) NOT NULL COMMENT '优惠券类型',
  `coupon_amount` decimal(10,2) NOT NULL COMMENT '优惠券金额',
  `min_amount` decimal(10,2) NOT NULL COMMENT '最低消费金额',
  `description` varchar(255) DEFAULT NULL COMMENT '使用说明',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_coupon_type` (`coupon_type`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='优惠券配置表';

-- 插入默认配置数据
INSERT INTO `weshop_coupon_config` (`coupon_type`, `coupon_amount`, `min_amount`, `description`, `is_active`) VALUES
('coupon_10', 10.00, 50.00, '10元优惠券，满50元可用', 1),
('coupon_20', 20.00, 100.00, '20元优惠券，满100元可用', 1);

-- 创建系统配置表（用于存储全局规则）
CREATE TABLE IF NOT EXISTS `weshop_system_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` varchar(500) NOT NULL COMMENT '配置值',
  `config_desc` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- 插入系统配置数据
INSERT INTO `weshop_system_config` (`config_key`, `config_value`, `config_desc`, `is_active`) VALUES
('max_coupons_per_order', '1', '每单最多使用优惠券数量', 1),
('coupon_balance_combine', 'true', '优惠券是否可以与余额组合使用', 1),
('balance_payment_enabled', 'true', '是否启用余额支付', 1);

-- 显示创建结果
SELECT 'Coupon config tables created successfully' as result;