-- 测试数据清理脚本
-- 用于清理测试数据，重置测试环境

-- ==========================================
-- 警告：此脚本将删除测试数据，请谨慎使用！
-- ==========================================

-- 设置安全模式（可选，防止误删）
-- SET SQL_SAFE_UPDATES = 1;

-- ==========================================
-- 1. 清理测试用户的优惠券数据
-- ==========================================

-- 删除测试用户的优惠券
DELETE FROM weshop_user_coupon 
WHERE user_id IN (1, 2, 3, 888, 999) 
   OR coupon_number LIKE 'RECHARGE_%'
   OR coupon_number LIKE 'QUICK_TEST_%'
   OR coupon_number LIKE 'FLOW_TEST_%';

SELECT CONCAT('已删除 ', ROW_COUNT(), ' 条优惠券记录') as coupon_cleanup;

-- ==========================================
-- 2. 清理测试用户的充值记录
-- ==========================================

-- 删除测试用户的充值记录
DELETE FROM weshop_recharge_record 
WHERE user_id IN (1, 2, 3, 888, 999)
   OR wx_order_id LIKE 'RECHARGE_%'
   OR wx_order_id LIKE 'QUICK_TEST_%'
   OR wx_order_id LIKE 'FLOW_TEST_%';

SELECT CONCAT('已删除 ', ROW_COUNT(), ' 条充值记录') as recharge_cleanup;

-- ==========================================
-- 3. 重置测试用户余额
-- ==========================================

-- 重置测试用户余额为0
UPDATE weshop_user 
SET balance = 0.00 
WHERE id IN (1, 2, 3, 888, 999);

SELECT CONCAT('已重置 ', ROW_COUNT(), ' 个用户的余额') as balance_reset;

-- ==========================================
-- 4. 清理测试订单数据（如果存在）
-- ==========================================

-- 删除测试订单（如果订单表存在）
/*
DELETE FROM weshop_order 
WHERE user_id IN (1, 2, 3, 888, 999)
   OR order_sn LIKE 'ORDER_%'
   OR order_sn LIKE 'FLOW_ORDER_%';

SELECT CONCAT('已删除 ', ROW_COUNT(), ' 条订单记录') as order_cleanup;
*/

-- ==========================================
-- 5. 清理测试购物车数据（如果存在）
-- ==========================================

-- 删除测试购物车（如果购物车表存在）
/*
DELETE FROM weshop_cart 
WHERE user_id IN (1, 2, 3, 888, 999);

SELECT CONCAT('已删除 ', ROW_COUNT(), ' 条购物车记录') as cart_cleanup;
*/

-- ==========================================
-- 6. 清理测试商品数据（如果存在）
-- ==========================================

-- 删除测试商品（如果需要）
/*
DELETE FROM weshop_goods 
WHERE id IN (9001, 9002, 9003);

SELECT CONCAT('已删除 ', ROW_COUNT(), ' 条测试商品') as goods_cleanup;
*/

-- ==========================================
-- 7. 可选：删除测试用户
-- ==========================================

-- 如果需要完全删除测试用户，取消注释以下代码
/*
DELETE FROM weshop_user 
WHERE id IN (1, 2, 3, 888, 999)
   OR username IN ('testuser1', 'testuser2', 'testuser3', 'quicktest', 'flowtest');

SELECT CONCAT('已删除 ', ROW_COUNT(), ' 个测试用户') as user_cleanup;
*/

-- ==========================================
-- 8. 验证清理结果
-- ==========================================

-- 检查剩余的测试数据
SELECT 
    '清理完成，剩余测试数据检查：' as cleanup_summary;

-- 检查优惠券表
SELECT 
    COUNT(*) as remaining_test_coupons,
    '剩余测试优惠券数量' as description
FROM weshop_user_coupon 
WHERE user_id IN (1, 2, 3, 888, 999)
   OR coupon_number LIKE '%TEST%';

-- 检查充值记录表
SELECT 
    COUNT(*) as remaining_test_recharges,
    '剩余测试充值记录数量' as description
FROM weshop_recharge_record 
WHERE user_id IN (1, 2, 3, 888, 999)
   OR wx_order_id LIKE '%TEST%';

-- 检查用户余额
SELECT 
    id, username, balance,
    CASE WHEN balance = 0.00 THEN '已重置' ELSE '未重置' END as balance_status
FROM weshop_user 
WHERE id IN (1, 2, 3, 888, 999);

-- ==========================================
-- 9. 重置自增ID（可选）
-- ==========================================

-- 如果需要重置自增ID，取消注释以下代码
/*
-- 重置优惠券表自增ID
ALTER TABLE weshop_user_coupon AUTO_INCREMENT = 1;

-- 重置充值记录表自增ID  
ALTER TABLE weshop_recharge_record AUTO_INCREMENT = 1;

SELECT '自增ID已重置' as auto_increment_reset;
*/

-- ==========================================
-- 10. 清理完成提示
-- ==========================================

SELECT 
    '测试数据清理完成！' as final_message,
    '可以重新运行测试数据脚本' as next_step,
    NOW() as cleanup_time;

-- 显示清理统计
SELECT 
    '清理统计：' as cleanup_stats,
    (SELECT COUNT(*) FROM weshop_user_coupon WHERE user_id IN (1, 2, 3, 888, 999)) as remaining_coupons,
    (SELECT COUNT(*) FROM weshop_recharge_record WHERE user_id IN (1, 2, 3, 888, 999)) as remaining_recharges,
    (SELECT SUM(balance) FROM weshop_user WHERE id IN (1, 2, 3, 888, 999)) as total_test_balance;