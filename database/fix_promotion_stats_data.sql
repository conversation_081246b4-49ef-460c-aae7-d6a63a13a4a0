-- 推广统计数据修复脚本
-- 用于修复缺失的weshop_user_goods_promotion_stats表数据

-- ==========================================
-- 1. 备份现有统计数据（可选）
-- ==========================================
-- CREATE TABLE weshop_user_goods_promotion_stats_backup AS 
-- SELECT * FROM weshop_user_goods_promotion_stats;

-- ==========================================
-- 2. 查找需要修复的数据
-- ==========================================
-- 查找有阶梯推广收益记录但缺少统计数据的情况
SELECT 
    pe.promoter_id,
    pe.goods_id,
    COUNT(*) as missing_stats_count,
    SUM(pe.commission_amount) as total_commission,
    MIN(pe.create_time) as first_promotion_time,
    MAX(pe.create_time) as last_promotion_time
FROM weshop_promotion_earnings pe
LEFT JOIN weshop_user_goods_promotion_stats ups 
    ON pe.promoter_id = ups.promoter_id AND pe.goods_id = ups.goods_id
WHERE pe.is_tiered_promotion = 1 
AND pe.status != 'cancelled'
AND ups.promoter_id IS NULL  -- 缺少统计记录
GROUP BY pe.promoter_id, pe.goods_id
ORDER BY pe.promoter_id, pe.goods_id;

-- ==========================================
-- 3. 修复缺失的统计数据
-- ==========================================
-- 为缺失的推广统计数据创建记录
INSERT INTO weshop_user_goods_promotion_stats (
    promoter_id,
    goods_id,
    total_promotion_count,
    tier1_count,
    tier2_count,
    tier3_count,
    tier4_plus_count,
    total_commission,
    first_promotion_time,
    last_promotion_time,
    create_time,
    update_time
)
SELECT 
    pe.promoter_id,
    pe.goods_id,
    COUNT(*) as total_promotion_count,
    SUM(CASE WHEN pe.promotion_order_count = 1 THEN 1 ELSE 0 END) as tier1_count,
    SUM(CASE WHEN pe.promotion_order_count = 2 THEN 1 ELSE 0 END) as tier2_count,
    SUM(CASE WHEN pe.promotion_order_count = 3 THEN 1 ELSE 0 END) as tier3_count,
    SUM(CASE WHEN pe.promotion_order_count >= 4 THEN 1 ELSE 0 END) as tier4_plus_count,
    SUM(pe.commission_amount) as total_commission,
    MIN(pe.create_time) as first_promotion_time,
    MAX(pe.create_time) as last_promotion_time,
    NOW() as create_time,
    NOW() as update_time
FROM weshop_promotion_earnings pe
LEFT JOIN weshop_user_goods_promotion_stats ups 
    ON pe.promoter_id = ups.promoter_id AND pe.goods_id = ups.goods_id
WHERE pe.is_tiered_promotion = 1 
AND pe.status != 'cancelled'
AND ups.promoter_id IS NULL  -- 只处理缺少统计记录的情况
GROUP BY pe.promoter_id, pe.goods_id;

-- ==========================================
-- 4. 修复不一致的统计数据
-- ==========================================
-- 更新已存在但数据不一致的统计记录
UPDATE weshop_user_goods_promotion_stats ups
JOIN (
    SELECT 
        pe.promoter_id,
        pe.goods_id,
        COUNT(*) as correct_total_count,
        SUM(CASE WHEN pe.promotion_order_count = 1 THEN 1 ELSE 0 END) as correct_tier1_count,
        SUM(CASE WHEN pe.promotion_order_count = 2 THEN 1 ELSE 0 END) as correct_tier2_count,
        SUM(CASE WHEN pe.promotion_order_count = 3 THEN 1 ELSE 0 END) as correct_tier3_count,
        SUM(CASE WHEN pe.promotion_order_count >= 4 THEN 1 ELSE 0 END) as correct_tier4_plus_count,
        SUM(pe.commission_amount) as correct_total_commission,
        MIN(pe.create_time) as correct_first_time,
        MAX(pe.create_time) as correct_last_time
    FROM weshop_promotion_earnings pe
    WHERE pe.is_tiered_promotion = 1 
    AND pe.status != 'cancelled'
    GROUP BY pe.promoter_id, pe.goods_id
) correct_data ON ups.promoter_id = correct_data.promoter_id AND ups.goods_id = correct_data.goods_id
SET 
    ups.total_promotion_count = correct_data.correct_total_count,
    ups.tier1_count = correct_data.correct_tier1_count,
    ups.tier2_count = correct_data.correct_tier2_count,
    ups.tier3_count = correct_data.correct_tier3_count,
    ups.tier4_plus_count = correct_data.correct_tier4_plus_count,
    ups.total_commission = correct_data.correct_total_commission,
    ups.first_promotion_time = correct_data.correct_first_time,
    ups.last_promotion_time = correct_data.correct_last_time,
    ups.update_time = NOW()
WHERE (
    ups.total_promotion_count != correct_data.correct_total_count
    OR ABS(ups.total_commission - correct_data.correct_total_commission) > 0.01
    OR ups.tier1_count != correct_data.correct_tier1_count
    OR ups.tier2_count != correct_data.correct_tier2_count
    OR ups.tier3_count != correct_data.correct_tier3_count
    OR ups.tier4_plus_count != correct_data.correct_tier4_plus_count
);

-- ==========================================
-- 5. 验证修复结果
-- ==========================================
-- 检查修复后的数据一致性
SELECT 
    '修复后数据验证' as check_type,
    pe.promoter_id,
    pe.goods_id,
    COUNT(*) as earnings_count,
    SUM(pe.commission_amount) as total_earnings,
    ups.total_promotion_count as stats_count,
    ups.total_commission as stats_commission,
    ups.tier1_count,
    ups.tier2_count,
    ups.tier3_count,
    ups.tier4_plus_count,
    CASE 
        WHEN COUNT(*) = ups.total_promotion_count 
        AND ABS(SUM(pe.commission_amount) - ups.total_commission) <= 0.01 
        THEN '数据一致'
        ELSE '数据仍不一致'
    END as consistency_status
FROM weshop_promotion_earnings pe
JOIN weshop_user_goods_promotion_stats ups 
    ON pe.promoter_id = ups.promoter_id AND pe.goods_id = ups.goods_id
WHERE pe.is_tiered_promotion = 1 
AND pe.status != 'cancelled'
GROUP BY pe.promoter_id, pe.goods_id, ups.total_promotion_count, ups.total_commission,
         ups.tier1_count, ups.tier2_count, ups.tier3_count, ups.tier4_plus_count
ORDER BY pe.promoter_id, pe.goods_id;

-- ==========================================
-- 6. 统计修复结果
-- ==========================================
SELECT 
    '修复统计' as result_type,
    '修复前统计记录数' as metric_name,
    (SELECT COUNT(*) FROM weshop_user_goods_promotion_stats_backup) as metric_value
WHERE EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'weshop_user_goods_promotion_stats_backup')

UNION ALL

SELECT 
    '修复统计' as result_type,
    '修复后统计记录数' as metric_name,
    COUNT(*) as metric_value
FROM weshop_user_goods_promotion_stats

UNION ALL

SELECT 
    '修复统计' as result_type,
    '阶梯推广收益记录数' as metric_name,
    COUNT(DISTINCT CONCAT(promoter_id, '-', goods_id)) as metric_value
FROM weshop_promotion_earnings 
WHERE is_tiered_promotion = 1 AND status != 'cancelled'

UNION ALL

SELECT 
    '修复统计' as result_type,
    '数据一致的记录数' as metric_name,
    COUNT(*) as metric_value
FROM (
    SELECT 
        pe.promoter_id,
        pe.goods_id
    FROM weshop_promotion_earnings pe
    JOIN weshop_user_goods_promotion_stats ups 
        ON pe.promoter_id = ups.promoter_id AND pe.goods_id = ups.goods_id
    WHERE pe.is_tiered_promotion = 1 
    AND pe.status != 'cancelled'
    GROUP BY pe.promoter_id, pe.goods_id, ups.total_promotion_count, ups.total_commission
    HAVING COUNT(*) = ups.total_promotion_count 
    AND ABS(SUM(pe.commission_amount) - ups.total_commission) <= 0.01
) consistent_data;

-- ==========================================
-- 7. 重新计算推广次数（修复promotion_order_count）
-- ==========================================
-- 如果发现promotion_order_count不正确，可以重新计算
-- 注意：这个操作会影响阶梯推广的返现比例计算，请谨慎执行

/*
-- 重新计算每个推广者对每个商品的推广次数
UPDATE weshop_promotion_earnings pe1
JOIN (
    SELECT 
        pe2.id,
        ROW_NUMBER() OVER (
            PARTITION BY pe2.promoter_id, pe2.goods_id 
            ORDER BY pe2.create_time
        ) as correct_order_count
    FROM weshop_promotion_earnings pe2
    WHERE pe2.is_tiered_promotion = 1 
    AND pe2.status != 'cancelled'
) order_calc ON pe1.id = order_calc.id
SET pe1.promotion_order_count = order_calc.correct_order_count
WHERE pe1.is_tiered_promotion = 1 
AND pe1.status != 'cancelled';

-- 重新计算统计数据（如果修改了promotion_order_count）
-- 执行上面的修复统计数据脚本
*/

-- ==========================================
-- 使用说明
-- ==========================================
/*
使用步骤：
1. 先执行诊断脚本 diagnose_promotion_stats_issue.sql 确认问题
2. 可选：创建备份表（取消注释第一部分）
3. 执行修复脚本的第2-4部分
4. 执行第5-6部分验证修复结果
5. 如果需要重新计算推广次数，谨慎执行第7部分

注意事项：
1. 在生产环境执行前，请先在测试环境验证
2. 建议在业务低峰期执行
3. 执行前做好数据备份
4. 如果涉及大量数据，考虑分批执行

验证方法：
1. 检查修复后的数据一致性
2. 验证推广统计页面显示是否正常
3. 测试新的推广订单是否正常更新统计
*/