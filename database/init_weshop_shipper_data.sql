-- 初始化weshop_shipper表的快递公司数据
-- 如果表中已有数据，此脚本会跳过重复数据

-- 检查表是否存在
SELECT 'weshop_shipper表检查' as step;
SELECT COUNT(*) as current_count FROM weshop_shipper;

-- 插入主流快递公司数据（使用INSERT IGNORE避免重复）
INSERT IGNORE INTO weshop_shipper (shipper_name, shipper_code, enabled, sort, add_time) VALUES
('顺丰速运', 'SF', 1, 1, NOW()),
('中通快递', 'ZTO', 1, 2, NOW()),
('圆通速递', 'YTO', 1, 3, NOW()),
('申通快递', 'STO', 1, 4, NOW()),
('韵达速递', 'YD', 1, 5, NOW()),
('百世快递', 'HTKY', 1, 6, NOW()),
('德邦快递', 'DBL', 1, 7, NOW()),
('京东快递', 'JD', 1, 8, NOW()),
('邮政快递包裹', 'YZPY', 1, 9, NOW()),
('EMS', 'EMS', 1, 10, NOW()),
('天天快递', 'HHTT', 1, 11, NOW()),
('宅急送', 'ZJS', 1, 12, NOW()),
('国通快递', 'GTO', 1, 13, NOW()),
('全峰快递', 'QFKD', 1, 14, NOW()),
('优速快递', 'UC', 1, 15, NOW()),
('中国快递服务', 'CCES', 1, 16, NOW()),
('安能快递', 'ANE', 1, 17, NOW()),
('快捷快递', 'FAST', 1, 18, NOW()),
('ADP国际快递', 'ADP', 1, 19, NOW()),
('DHL', 'DHL', 1, 20, NOW());

-- 验证插入结果
SELECT 'weshop_shipper数据插入完成' as step;
SELECT COUNT(*) as total_count FROM weshop_shipper;
SELECT COUNT(*) as enabled_count FROM weshop_shipper WHERE enabled = 1;

-- 显示插入的数据
SELECT 'weshop_shipper启用的快递公司列表' as step;
SELECT id, shipper_name, shipper_code, enabled, sort, add_time 
FROM weshop_shipper 
WHERE enabled = 1 
ORDER BY sort, id;

-- 检查是否有重复的快递编码
SELECT 'weshop_shipper重复编码检查' as step;
SELECT shipper_code, COUNT(*) as count 
FROM weshop_shipper 
GROUP BY shipper_code 
HAVING COUNT(*) > 1;