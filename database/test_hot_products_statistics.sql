-- 热销商品排行统计测试SQL
-- 用于验证热销商品统计逻辑的准确性

-- 1. 查看指定时间范围内的已支付订单
SELECT 
    o.id as order_id,
    o.order_sn,
    o.create_time,
    o.pay_status,
    o.actual_price
FROM weshop_order o
WHERE o.pay_status = 1  -- PayStatusEnum.PAID
  AND o.create_time BETWEEN '2024-01-01 00:00:00' AND '2024-12-31 23:59:59'
ORDER BY o.create_time DESC
LIMIT 10;

-- 2. 查看这些订单中的商品详情
SELECT 
    og.order_id,
    og.goods_id,
    og.goods_name,
    og.number as quantity,
    og.retail_price,
    (og.number * og.retail_price) as item_total,
    og.list_pic_url
FROM weshop_order_goods og
INNER JOIN weshop_order o ON og.order_id = o.id
WHERE o.pay_status = 1
  AND o.create_time BETWEEN '2024-01-01 00:00:00' AND '2024-12-31 23:59:59'
ORDER BY og.goods_id, og.order_id
LIMIT 20;

-- 3. 按商品统计实际销量（这是热销商品统计的核心逻辑）
SELECT 
    og.goods_id,
    og.goods_name,
    SUM(og.number) as total_sales,                    -- 实际销量
    SUM(og.number * og.retail_price) as total_amount, -- 实际销售额
    COUNT(DISTINCT og.order_id) as order_count,       -- 订单数量
    COUNT(og.id) as item_count,                       -- 商品项数量
    AVG(og.retail_price) as avg_price,                -- 平均价格
    og.list_pic_url
FROM weshop_order_goods og
INNER JOIN weshop_order o ON og.order_id = o.id
WHERE o.pay_status = 1  -- 只统计已支付订单
  AND o.create_time BETWEEN '2024-01-01 00:00:00' AND '2024-12-31 23:59:59'
GROUP BY og.goods_id, og.goods_name, og.list_pic_url
ORDER BY total_sales DESC  -- 按实际销量排序
LIMIT 10;

-- 4. 对比商品表中的销量字段（用于验证差异）
SELECT 
    g.id as goods_id,
    g.name as goods_name,
    g.sell_volume as goods_table_sales,  -- 商品表中的销量
    COALESCE(real_sales.total_sales, 0) as actual_sales,  -- 实际订单销量
    g.goods_number as stock,
    g.retail_price,
    g.primary_pic_url
FROM weshop_goods g
LEFT JOIN (
    SELECT 
        og.goods_id,
        SUM(og.number) as total_sales
    FROM weshop_order_goods og
    INNER JOIN weshop_order o ON og.order_id = o.id
    WHERE o.pay_status = 1
      AND o.create_time BETWEEN '2024-01-01 00:00:00' AND '2024-12-31 23:59:59'
    GROUP BY og.goods_id
) real_sales ON g.id = real_sales.goods_id
WHERE g.is_delete = 0
ORDER BY COALESCE(real_sales.total_sales, 0) DESC
LIMIT 15;

-- 5. 验证特定商品的销量计算
-- 替换 {goods_id} 为实际的商品ID
SELECT 
    og.order_id,
    o.order_sn,
    o.create_time,
    og.goods_name,
    og.number as quantity,
    og.retail_price,
    (og.number * og.retail_price) as item_total,
    o.pay_status
FROM weshop_order_goods og
INNER JOIN weshop_order o ON og.order_id = o.id
WHERE og.goods_id = 1  -- 替换为实际商品ID
  AND o.pay_status = 1
  AND o.create_time BETWEEN '2024-01-01 00:00:00' AND '2024-12-31 23:59:59'
ORDER BY o.create_time DESC;

-- 6. 统计指定商品的总销量
SELECT 
    og.goods_id,
    og.goods_name,
    SUM(og.number) as total_quantity,
    COUNT(DISTINCT og.order_id) as unique_orders,
    SUM(og.number * og.retail_price) as total_revenue
FROM weshop_order_goods og
INNER JOIN weshop_order o ON og.order_id = o.id
WHERE og.goods_id = 1  -- 替换为实际商品ID
  AND o.pay_status = 1
  AND o.create_time BETWEEN '2024-01-01 00:00:00' AND '2024-12-31 23:59:59'
GROUP BY og.goods_id, og.goods_name;

-- 7. 检查数据完整性
-- 查看是否有订单商品记录但对应订单不存在的情况
SELECT 
    og.id,
    og.order_id,
    og.goods_id,
    og.goods_name,
    CASE WHEN o.id IS NULL THEN '订单不存在' ELSE '订单存在' END as order_status
FROM weshop_order_goods og
LEFT JOIN weshop_order o ON og.order_id = o.id
WHERE o.id IS NULL
LIMIT 10;

-- 8. 查看不同支付状态的订单分布
SELECT 
    o.pay_status,
    CASE 
        WHEN o.pay_status = 0 THEN '未支付'
        WHEN o.pay_status = 1 THEN '已支付'
        WHEN o.pay_status = 2 THEN '退款中'
        WHEN o.pay_status = 3 THEN '已退款'
        ELSE '其他状态'
    END as pay_status_name,
    COUNT(*) as order_count,
    SUM(o.actual_price) as total_amount
FROM weshop_order o
WHERE o.create_time BETWEEN '2024-01-01 00:00:00' AND '2024-12-31 23:59:59'
GROUP BY o.pay_status
ORDER BY o.pay_status;

-- 9. 验证时间范围查询的准确性
SELECT 
    DATE(o.create_time) as order_date,
    COUNT(*) as order_count,
    COUNT(CASE WHEN o.pay_status = 1 THEN 1 END) as paid_order_count,
    SUM(CASE WHEN o.pay_status = 1 THEN o.actual_price ELSE 0 END) as paid_amount
FROM weshop_order o
WHERE o.create_time BETWEEN '2024-01-01 00:00:00' AND '2024-12-31 23:59:59'
GROUP BY DATE(o.create_time)
ORDER BY order_date DESC
LIMIT 10;

-- 10. 模拟热销商品API返回的数据格式
SELECT 
    og.goods_id,
    og.goods_name as name,
    CONCAT('SKU', LPAD(og.goods_id, 3, '0')) as sku,
    SUM(og.number) as sales,
    ROUND(SUM(og.number * og.retail_price), 2) as amount,
    COUNT(DISTINCT og.order_id) as order_count,
    og.list_pic_url as image,
    COALESCE(g.goods_number, 0) as stock,
    -- 模拟增长率计算（实际应该对比上一周期）
    ROUND((RAND() - 0.5) * 100, 0) as growth
FROM weshop_order_goods og
INNER JOIN weshop_order o ON og.order_id = o.id
LEFT JOIN weshop_goods g ON og.goods_id = g.id
WHERE o.pay_status = 1
  AND o.create_time BETWEEN '2024-01-01 00:00:00' AND '2024-12-31 23:59:59'
GROUP BY og.goods_id, og.goods_name, og.list_pic_url, g.goods_number
ORDER BY sales DESC
LIMIT 10;

-- 使用说明：
-- 1. 将时间范围 '2024-01-01 00:00:00' 和 '2024-12-31 23:59:59' 替换为实际需要测试的时间范围
-- 2. 将商品ID 1 替换为实际存在的商品ID
-- 3. 根据实际的枚举值调整 pay_status 的值
-- 4. 执行这些查询来验证热销商品统计的准确性