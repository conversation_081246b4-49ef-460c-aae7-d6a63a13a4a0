# 测试数据脚本使用说明

本目录包含了完整的测试数据脚本，用于测试从账户充值到优惠券生成的完整业务流程。

## 📁 脚本文件说明

### 1. 基础表结构脚本
- `init_recharge_system.sql` - 充值系统基础表
- `create_coupon_tables.sql` - 优惠券相关表
- `create_coupon_config_table.sql` - 优惠券配置表

### 2. 测试数据脚本
- `test_data_complete_flow.sql` - 完整业务流程测试数据
- `quick_test_data.sql` - 快速测试数据
- `business_flow_simulation.sql` - 业务流程模拟数据

### 3. 清理脚本
- `cleanup_test_data.sql` - 清理测试数据

## 🚀 使用步骤

### 第一步：创建基础表结构
```sql
-- 1. 创建充值系统表
source database/init_recharge_system.sql;

-- 2. 创建优惠券表
source database/create_coupon_tables.sql;

-- 3. 创建配置表
source database/create_coupon_config_table.sql;
```

### 第二步：选择测试数据脚本

#### 选项A：快速测试（推荐新手）
```sql
source database/quick_test_data.sql;
```
**创建内容：**
- 1个测试用户（ID: 999）
- 1500元余额
- 10张优惠券（5张10元券 + 5张20元券）

#### 选项B：完整流程测试
```sql
source database/test_data_complete_flow.sql;
```
**创建内容：**
- 3个测试用户（ID: 1, 2, 3）
- 不同的充值状态和余额
- 多种优惠券状态（可用/已用/过期）

#### 选项C：业务流程模拟
```sql
source database/business_flow_simulation.sql;
```
**创建内容：**
- 1个测试用户（ID: 888）
- 完整的购物流程模拟
- 包含订单和优惠券使用记录

### 第三步：验证数据
```sql
-- 查看用户余额
SELECT id, username, balance FROM weshop_user WHERE id IN (1, 2, 3, 888, 999);

-- 查看优惠券统计
SELECT user_id, COUNT(*) as total, 
       SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as available
FROM weshop_user_coupon 
WHERE user_id IN (1, 2, 3, 888, 999) 
GROUP BY user_id;
```

## 🧪 测试场景

### 1. 优惠券使用规则测试
- **10元券**：满50元可用
- **20元券**：满100元可用

### 2. 购物车结算测试
```javascript
// 测试API调用
POST /wechat/cart/advanced-checkout
{
  "addressId": "1",
  "couponId": 123,      // 优惠券ID
  "useBalance": 50.00   // 使用余额金额
}
```

### 3. 测试用例建议

#### 场景1：小额订单（30元）
- 预期：无法使用任何优惠券
- 支付方式：余额支付30元

#### 场景2：中等订单（60元）
- 预期：可使用10元券
- 支付方式：10元券 + 50元余额

#### 场景3：大额订单（150元）
- 预期：可使用20元券
- 支付方式：20元券 + 130元余额

#### 场景4：组合支付测试
- 订单金额：120元
- 使用20元券：100元
- 使用余额：50元
- 实际支付：50元

## 🔧 数据清理

当需要重置测试环境时：
```sql
source database/cleanup_test_data.sql;
```

**清理内容：**
- 删除所有测试优惠券
- 删除所有测试充值记录
- 重置用户余额为0
- 可选删除测试用户

## 📊 数据结构说明

### 用户表 (weshop_user)
```sql
id, username, nickname, balance, ...
```

### 充值记录表 (weshop_recharge_record)
```sql
id, user_id, amount, coupon_count, coupon_amount, status, ...
```

### 优惠券表 (weshop_user_coupon)
```sql
id, user_id, title, amount, min_amount, status, start_time, end_time, ...
```

### 状态说明
- **充值状态**：0-待支付，1-已支付，2-已取消
- **优惠券状态**：0-可用，1-已使用，2-已过期

## ⚠️ 注意事项

1. **执行顺序**：必须先创建表结构，再导入测试数据
2. **数据冲突**：如果用户ID已存在，脚本会更新而不是插入
3. **清理数据**：测试完成后建议清理测试数据
4. **生产环境**：请勿在生产环境执行这些脚本

## 🐛 常见问题

### Q: 表不存在错误
A: 请先执行基础表结构脚本

### Q: 用户ID冲突
A: 脚本使用了特定的测试用户ID（1,2,3,888,999），如果冲突请修改脚本

### Q: 优惠券无法使用
A: 检查订单金额是否满足最低消费要求

### Q: 余额不足
A: 检查用户余额是否充足，可以重新运行充值脚本

## 📞 技术支持

如果遇到问题，请检查：
1. 数据库连接是否正常
2. 表结构是否完整
3. 数据是否正确导入
4. API接口是否正常工作

---

**祝测试顺利！** 🎉