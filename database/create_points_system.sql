-- 积分系统数据库表结构

-- 1. 为用户表添加积分字段
ALTER TABLE `weshop_user` 
ADD COLUMN `points` int(11) NOT NULL DEFAULT 0 COMMENT '用户积分' AFTER `balance`;

-- 2. 创建积分记录表
CREATE TABLE `weshop_points_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `type` varchar(20) NOT NULL COMMENT '积分类型：earn(获得), use(使用)',
  `points` int(11) NOT NULL COMMENT '积分数量（正数为获得，负数为使用）',
  `source` varchar(50) NOT NULL COMMENT '积分来源：order(订单), manual(手动调整)',
  `source_id` int(11) DEFAULT NULL COMMENT '来源ID（如订单ID）',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_source` (`source`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分记录表';

-- 3. 创建积分配置表
CREATE TABLE `weshop_points_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `earn_rate` decimal(10,2) NOT NULL DEFAULT 1.00 COMMENT '积分获得比例（每消费多少元获得1积分）',
  `use_rate` decimal(10,2) NOT NULL DEFAULT 100.00 COMMENT '积分使用比例（多少积分抵扣1元）',
  `min_use_points` int(11) NOT NULL DEFAULT 100 COMMENT '最少使用积分数量',
  `max_use_ratio` decimal(5,2) NOT NULL DEFAULT 50.00 COMMENT '最大使用比例（订单金额的百分比）',
  `is_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用积分系统',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分配置表';

-- 4. 插入默认积分配置
-- 消费1元获得1积分，100积分抵扣1元
INSERT INTO `weshop_points_config` (`earn_rate`, `use_rate`, `min_use_points`, `max_use_ratio`, `is_enabled`) 
VALUES (1.00, 100.00, 100, 50.00, 1);