-- 测试优惠券退回功能并保留历史记录（简化版）
-- 此脚本用于验证取消订单后优惠券退回，但历史记录保留的功能

-- 设置测试用户ID
SET @test_user_id = 1;

-- 1. 查看测试前的状态
SELECT '=== 测试前状态检查 ===' as step;

-- 查看用户可用优惠券
SELECT 
    '用户可用优惠券' as type,
    id, title, amount, min_amount, status, 
    start_time, end_time, order_id, use_time,
    last_used_order_id, last_use_time, is_refunded, refund_time
FROM weshop_user_coupon 
WHERE user_id = @test_user_id AND status = 0
ORDER BY create_time DESC
LIMIT 5;

-- 查看用户订单
SELECT 
    '用户订单' as type,
    id, order_sn, order_status, coupon_id, coupon_price, 
    actual_price, create_time
FROM weshop_order 
WHERE user_id = @test_user_id
ORDER BY create_time DESC
LIMIT 5;

-- 2. 模拟创建一个使用优惠券的订单（如果没有的话）
-- 检查是否有可用的优惠券
SELECT @available_coupon_id := id 
FROM weshop_user_coupon 
WHERE user_id = @test_user_id AND status = 0 
LIMIT 1;

-- 如果有可用优惠券，创建测试订单
INSERT INTO weshop_order (
    order_sn, user_id, order_status, pay_status,
    consignee, mobile, address, 
    goods_price, coupon_id, coupon_price, actual_price,
    create_time
) 
SELECT 
    CONCAT('TEST_', UNIX_TIMESTAMP(), '_', @test_user_id),
    @test_user_id,
    'WAIT_PAY',
    'PENDING_PAYMENT',
    '测试收货人',
    '13800138000',
    '测试地址',
    100.00,
    @available_coupon_id,
    10.00,
    90.00,
    NOW()
WHERE @available_coupon_id IS NOT NULL;

-- 获取刚创建的订单ID
SELECT @test_order_id := LAST_INSERT_ID() WHERE @available_coupon_id IS NOT NULL;

-- 3. 模拟使用优惠券（更新优惠券状态和历史记录）
UPDATE weshop_user_coupon 
SET status = 1, 
    use_time = NOW(), 
    order_id = @test_order_id,
    last_used_order_id = @test_order_id,
    last_use_time = NOW(),
    is_refunded = 0,
    refund_time = NULL
WHERE id = @available_coupon_id AND @test_order_id IS NOT NULL;

-- 4. 显示使用优惠券后的状态
SELECT '=== 使用优惠券后状态 ===' as step;

SELECT 
    '订单信息' as type,
    id, order_sn, order_status, coupon_id, coupon_price
FROM weshop_order 
WHERE id = @test_order_id;

SELECT 
    '优惠券状态' as type,
    id, title, status, order_id, use_time,
    last_used_order_id, last_use_time, is_refunded
FROM weshop_user_coupon 
WHERE id = @available_coupon_id;

-- 5. 模拟取消订单
UPDATE weshop_order 
SET order_status = 'CANCELLED'
WHERE id = @test_order_id;

-- 6. 模拟优惠券退回（恢复优惠券状态但保留历史记录）
UPDATE weshop_user_coupon 
SET status = 0, 
    use_time = NULL, 
    order_id = NULL,
    is_refunded = 1,
    refund_time = NOW()
WHERE id = @available_coupon_id AND @test_order_id IS NOT NULL;

-- 7. 显示最终状态
SELECT '=== 取消订单后最终状态 ===' as step;

SELECT 
    '订单状态' as type,
    id, order_sn, order_status, coupon_id, coupon_price
FROM weshop_order 
WHERE id = @test_order_id;

SELECT 
    '优惠券状态（已退回但保留历史）' as type,
    id, title, status, order_id, use_time,
    last_used_order_id, last_use_time, is_refunded, refund_time
FROM weshop_user_coupon 
WHERE id = @available_coupon_id;

-- 8. 验证查询订单详情时能获取到优惠券历史信息
SELECT 
    '订单详情查询验证' as step,
    uc.title as coupon_title,
    uc.description as coupon_description,
    uc.amount as coupon_amount,
    o.order_status,
    uc.is_refunded as coupon_refunded
FROM weshop_order o
LEFT JOIN weshop_user_coupon uc ON o.id = uc.last_used_order_id
WHERE o.id = @test_order_id;

-- 清理测试数据（可选，取消注释以清理）
-- DELETE FROM weshop_order WHERE id = @test_order_id;
