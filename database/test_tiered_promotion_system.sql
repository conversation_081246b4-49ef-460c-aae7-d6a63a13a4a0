-- 阶梯推广系统测试数据脚本

-- 1. 创建测试用户
INSERT IGNORE INTO `weshop_user` 
(`id`, `username`, `nickname`, `password`, `wechat_open_id`, `mobile`, `avatar`, `gender`, `register_time`, `register_ip`, `last_login_time`, `last_login_ip`, `promotion_code`)
VALUES 
(1001, 'promoter_user', '推广者小王', '', 'wx_promoter_001', '13800001001', 'https://example.com/avatar1.jpg', 1, NOW(), '127.0.0.1', NOW(), '127.0.0.1', 'promo_1001'),
(1002, 'buyer_user1', '买家小李', '', 'wx_buyer_001', '13800001002', 'https://example.com/avatar2.jpg', 1, NOW(), '127.0.0.1', NOW(), '127.0.0.1', 'promo_1002'),
(1003, 'buyer_user2', '买家小张', '', 'wx_buyer_002', '13800001003', 'https://example.com/avatar3.jpg', 1, NOW(), '127.0.0.1', NOW(), '127.0.0.1', 'promo_1003'),
(1004, 'buyer_user3', '买家小赵', '', 'wx_buyer_003', '13800001004', 'https://example.com/avatar4.jpg', 1, NOW(), '127.0.0.1', NOW(), '127.0.0.1', 'promo_1004'),
(1005, 'buyer_user4', '买家小钱', '', 'wx_buyer_004', '13800001005', 'https://example.com/avatar5.jpg', 1, NOW(), '127.0.0.1', NOW(), '127.0.0.1', 'promo_1005');

-- 2. 建立推广关系（买家都是推广者的下线）
UPDATE `weshop_user` SET `promoter_id` = 1001, `promotion_time` = NOW() WHERE `id` IN (1002, 1003, 1004, 1005);

-- 3. 创建测试商品
INSERT IGNORE INTO `weshop_goods` 
(`id`, `goods_sn`, `name`, `category_id`, `brand_id`, `gallery`, `keywords`, `goods_brief`, `goods_desc`, `is_on_sale`, `is_delete`, `sort_order`, `create_time`, `update_time`, `retail_price`, `market_price`, `share_url`, `is_new`, `is_hot`, `unit_price`, `promotion_desc`, `promotion_tag`, `app_exclusive_price`, `app_exclusive_price_start`, `app_exclusive_price_end`, `counter_price`, `extra_price`, `primary_pic_url`, `list_pic_url`, `goods_number`, `detail_tag`)
VALUES 
(2001, 'TIERED_001', '阶梯推广测试商品A', 1, 1, '["https://example.com/goods1.jpg"]', '阶梯推广,测试', '这是一个阶梯推广测试商品', '<p>阶梯推广测试商品详情</p>', 1, 0, 100, NOW(), NOW(), 199.00, 299.00, '', 1, 1, 199.00, '阶梯推广商品', '热销', 199.00, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), 299.00, 0.00, 'https://example.com/goods1.jpg', 'https://example.com/goods1_list.jpg', 1000, '阶梯推广'),
(2002, 'NORMAL_001', '普通推广测试商品B', 1, 1, '["https://example.com/goods2.jpg"]', '普通推广,测试', '这是一个普通推广测试商品', '<p>普通推广测试商品详情</p>', 1, 0, 99, NOW(), NOW(), 99.00, 149.00, '', 0, 0, 99.00, '普通推广商品', '', 99.00, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), 149.00, 0.00, 'https://example.com/goods2.jpg', 'https://example.com/goods2_list.jpg', 1000, '');

-- 4. 创建商品规格（简化版）
INSERT IGNORE INTO `weshop_product` 
(`id`, `goods_id`, `goods_specification_ids`, `goods_sn`, `retail_price`, `market_price`, `goods_number`, `primary_pic_url`)
VALUES 
(3001, 2001, '', 'TIERED_001_DEFAULT', 199.00, 299.00, 1000, 'https://example.com/goods1.jpg'),
(3002, 2002, '', 'NORMAL_001_DEFAULT', 99.00, 149.00, 1000, 'https://example.com/goods2.jpg');

-- 5. 配置阶梯推广商品
INSERT IGNORE INTO `weshop_tiered_promotion_goods` 
(`goods_id`, `goods_name`, `is_active`, `tier1_rate`, `tier2_rate`, `tier3_rate`, `tier4_plus_rate`, `description`, `create_time`, `update_time`)
VALUES 
(2001, '阶梯推广测试商品A', 1, 20.00, 30.00, 50.00, 10.00, '测试阶梯推广商品：第1笔返现20%，第2笔返现30%，第3笔返现50%，第4笔及以后返现10%', NOW(), NOW());

-- 6. 创建测试订单（模拟推广者小王推广商品A的情况）

-- 第1笔订单 - 买家小李购买，返现20%
INSERT IGNORE INTO `weshop_order` 
(`id`, `order_sn`, `user_id`, `promoter_id`, `order_status`, `pay_status`, `consignee`, `mobile`, `address`, `goods_price`, `freight_price`, `coupon_price`, `integral`, `integral_money`, `balance_price`, `order_price`, `actual_price`, `promotion_commission`, `pay_time`, `create_time`, `update_time`)
VALUES 
(4001, 'ORDER_TIERED_001', 1002, 1001, 'WAIT_SEND', 'PAID', '买家小李', '13800001002', '测试地址1', 199.00, 0.00, 0.00, 0, 0.00, 0.00, 199.00, 199.00, 39.80, NOW(), NOW(), NOW());

-- 第2笔订单 - 买家小张购买，返现30%
INSERT IGNORE INTO `weshop_order` 
(`id`, `order_sn`, `user_id`, `promoter_id`, `order_status`, `pay_status`, `consignee`, `mobile`, `address`, `goods_price`, `freight_price`, `coupon_price`, `integral`, `integral_money`, `balance_price`, `order_price`, `actual_price`, `promotion_commission`, `pay_time`, `create_time`, `update_time`)
VALUES 
(4002, 'ORDER_TIERED_002', 1003, 1001, 'WAIT_SEND', 'PAID', '买家小张', '13800001003', '测试地址2', 199.00, 0.00, 0.00, 0, 0.00, 0.00, 199.00, 199.00, 59.70, NOW(), NOW(), NOW());

-- 第3笔订单 - 买家小赵购买，返现50%
INSERT IGNORE INTO `weshop_order` 
(`id`, `order_sn`, `user_id`, `promoter_id`, `order_status`, `pay_status`, `consignee`, `mobile`, `address`, `goods_price`, `freight_price`, `coupon_price`, `integral`, `integral_money`, `balance_price`, `order_price`, `actual_price`, `promotion_commission`, `pay_time`, `create_time`, `update_time`)
VALUES 
(4003, 'ORDER_TIERED_003', 1004, 1001, 'WAIT_SEND', 'PAID', '买家小赵', '13800001004', '测试地址3', 199.00, 0.00, 0.00, 0, 0.00, 0.00, 199.00, 199.00, 99.50, NOW(), NOW(), NOW());

-- 第4笔订单 - 买家小钱购买，返现10%
INSERT IGNORE INTO `weshop_order` 
(`id`, `order_sn`, `user_id`, `promoter_id`, `order_status`, `pay_status`, `consignee`, `mobile`, `address`, `goods_price`, `freight_price`, `coupon_price`, `integral`, `integral_money`, `balance_price`, `order_price`, `actual_price`, `promotion_commission`, `pay_time`, `create_time`, `update_time`)
VALUES 
(4004, 'ORDER_TIERED_004', 1005, 1001, 'WAIT_SEND', 'PAID', '买家小钱', '13800001005', '测试地址4', 199.00, 0.00, 0.00, 0, 0.00, 0.00, 199.00, 199.00, 19.90, NOW(), NOW(), NOW());

-- 7. 创建订单商品记录
INSERT IGNORE INTO `weshop_order_goods` 
(`id`, `order_id`, `goods_id`, `goods_sn`, `product_id`, `goods_name`, `list_pic_url`, `market_price`, `retail_price`, `number`, `goods_specification_name_value`, `goods_specification_ids`, `is_real`)
VALUES 
(5001, 4001, 2001, 'TIERED_001', 3001, '阶梯推广测试商品A', 'https://example.com/goods1_list.jpg', 299.00, 199.00, 1, '', '', 1),
(5002, 4002, 2001, 'TIERED_001', 3001, '阶梯推广测试商品A', 'https://example.com/goods1_list.jpg', 299.00, 199.00, 1, '', '', 1),
(5003, 4003, 2001, 'TIERED_001', 3001, '阶梯推广测试商品A', 'https://example.com/goods1_list.jpg', 299.00, 199.00, 1, '', '', 1),
(5004, 4004, 2001, 'TIERED_001', 3001, '阶梯推广测试商品A', 'https://example.com/goods1_list.jpg', 299.00, 199.00, 1, '', '', 1);

-- 8. 创建推广收益记录
INSERT IGNORE INTO `weshop_promotion_earnings` 
(`id`, `promoter_id`, `promoted_user_id`, `order_id`, `order_no`, `order_amount`, `goods_id`, `goods_name`, `promotion_order_count`, `is_tiered_promotion`, `commission_rate`, `commission_amount`, `status`, `order_create_time`, `create_time`, `update_time`, `description`)
VALUES 
(6001, 1001, 1002, 4001, 'ORDER_TIERED_001', 199.00, 2001, '阶梯推广测试商品A', 1, 1, 20.00, 39.80, 'confirmed', NOW(), NOW(), NOW(), '阶梯推广第1笔订单，返现比例20%'),
(6002, 1001, 1003, 4002, 'ORDER_TIERED_002', 199.00, 2001, '阶梯推广测试商品A', 2, 1, 30.00, 59.70, 'confirmed', NOW(), NOW(), NOW(), '阶梯推广第2笔订单，返现比例30%'),
(6003, 1001, 1004, 4003, 'ORDER_TIERED_003', 199.00, 2001, '阶梯推广测试商品A', 3, 1, 50.00, 99.50, 'confirmed', NOW(), NOW(), NOW(), '阶梯推广第3笔订单，返现比例50%'),
(6004, 1001, 1005, 4004, 'ORDER_TIERED_004', 199.00, 2001, '阶梯推广测试商品A', 4, 1, 10.00, 19.90, 'pending', NOW(), NOW(), NOW(), '阶梯推广第4笔订单，返现比例10%');

-- 9. 创建用户商品推广统计记录
INSERT IGNORE INTO `weshop_user_goods_promotion_stats` 
(`id`, `promoter_id`, `goods_id`, `total_promotion_count`, `tier1_count`, `tier2_count`, `tier3_count`, `tier4_plus_count`, `total_commission`, `first_promotion_time`, `last_promotion_time`, `create_time`, `update_time`)
VALUES 
(7001, 1001, 2001, 4, 1, 1, 1, 1, 218.90, NOW(), NOW(), NOW(), NOW());

-- 10. 查询测试结果

-- 查看阶梯推广商品配置
SELECT '=== 阶梯推广商品配置 ===' as info;
SELECT * FROM `weshop_tiered_promotion_goods` WHERE `is_active` = 1;

-- 查看测试用户信息
SELECT '=== 测试用户信息 ===' as info;
SELECT 
    u.id, u.nickname, u.promoter_id, 
    p.nickname as promoter_nickname,
    u.promotion_time
FROM `weshop_user` u
LEFT JOIN `weshop_user` p ON u.promoter_id = p.id
WHERE u.id BETWEEN 1001 AND 1005
ORDER BY u.id;

-- 查看测试订单信息
SELECT '=== 测试订单信息 ===' as info;
SELECT 
    o.id, o.order_sn, o.user_id, 
    u.nickname as buyer_nickname,
    o.promoter_id,
    p.nickname as promoter_nickname,
    o.actual_price, o.promotion_commission,
    o.order_status, o.pay_status
FROM `weshop_order` o
LEFT JOIN `weshop_user` u ON o.user_id = u.id
LEFT JOIN `weshop_user` p ON o.promoter_id = p.id
WHERE o.id BETWEEN 4001 AND 4004
ORDER BY o.id;

-- 查看推广收益记录
SELECT '=== 推广收益记录 ===' as info;
SELECT 
    pe.id, pe.promoter_id, pe.promoted_user_id,
    pe.goods_name, pe.promotion_order_count,
    pe.commission_rate, pe.commission_amount,
    pe.is_tiered_promotion, pe.status,
    pe.description
FROM `weshop_promotion_earnings` pe
WHERE pe.id BETWEEN 6001 AND 6004
ORDER BY pe.id;

-- 查看用户推广统计
SELECT '=== 用户推广统计 ===' as info;
SELECT 
    ugps.*,
    u.nickname as promoter_nickname,
    g.name as goods_name
FROM `weshop_user_goods_promotion_stats` ugps
LEFT JOIN `weshop_user` u ON ugps.promoter_id = u.id
LEFT JOIN `weshop_goods` g ON ugps.goods_id = g.id
WHERE ugps.id = 7001;

-- 查看阶梯推广统计视图
SELECT '=== 阶梯推广统计视图 ===' as info;
SELECT * FROM `v_tiered_promotion_stats`;

-- 11. 测试存储过程
SELECT '=== 测试存储过程 ===' as info;

-- 测试计算第5笔订单的佣金（应该是10%）
CALL sp_calculate_tiered_commission(1001, 2001, 199.00, @rate, @amount, @count);
SELECT 
    '第5笔订单佣金计算' as test_case,
    @rate as commission_rate,
    @amount as commission_amount,
    @count as promotion_order_count;

-- 测试普通商品的佣金计算（应该是10%）
CALL sp_calculate_tiered_commission(1001, 2002, 99.00, @rate2, @amount2, @count2);
SELECT 
    '普通商品佣金计算' as test_case,
    @rate2 as commission_rate,
    @amount2 as commission_amount,
    @count2 as promotion_order_count;

-- 12. 清理测试数据的脚本（注释掉，需要时手动执行）
/*
-- 清理测试数据
DELETE FROM `weshop_user_goods_promotion_stats` WHERE `id` = 7001;
DELETE FROM `weshop_promotion_earnings` WHERE `id` BETWEEN 6001 AND 6004;
DELETE FROM `weshop_order_goods` WHERE `id` BETWEEN 5001 AND 5004;
DELETE FROM `weshop_order` WHERE `id` BETWEEN 4001 AND 4004;
DELETE FROM `weshop_product` WHERE `id` BETWEEN 3001 AND 3002;
DELETE FROM `weshop_goods` WHERE `id` BETWEEN 2001 AND 2002;
DELETE FROM `weshop_tiered_promotion_goods` WHERE `goods_id` = 2001;
DELETE FROM `weshop_user` WHERE `id` BETWEEN 1001 AND 1005;
*/