-- 修复已存在的优惠券使用状态问题
-- 将已经在订单中使用但状态未更新的优惠券标记为已使用

-- 1. 备份当前优惠券状态（可选）
CREATE TABLE IF NOT EXISTS `weshop_user_coupon_backup` AS 
SELECT * FROM `weshop_user_coupon` WHERE 1=0;

INSERT INTO `weshop_user_coupon_backup` 
SELECT * FROM `weshop_user_coupon` 
WHERE id IN (
    SELECT DISTINCT uc.id 
    FROM `weshop_user_coupon` uc
    JOIN `weshop_order` o ON o.coupon_id = uc.id
    WHERE uc.status = 0 AND o.coupon_price > 0
);

-- 2. 查看需要修复的数据
SELECT 
    '=== 需要修复的优惠券数据 ===' as info,
    uc.id as coupon_id,
    uc.user_id,
    uc.title,
    uc.amount,
    uc.status as current_status,
    o.id as order_id,
    o.order_sn,
    o.coupon_price,
    o.create_time as order_time,
    CASE 
        WHEN uc.status = 0 THEN '需要修复：状态应为已使用'
        ELSE '状态正常'
    END as fix_needed
FROM `weshop_user_coupon` uc
JOIN `weshop_order` o ON o.coupon_id = uc.id
WHERE o.coupon_price > 0
ORDER BY o.create_time DESC;

-- 3. 统计需要修复的数据量
SELECT 
    '=== 修复统计 ===' as info,
    COUNT(*) as total_coupons_in_orders,
    COUNT(CASE WHEN uc.status = 0 THEN 1 END) as need_fix_count,
    COUNT(CASE WHEN uc.status = 1 THEN 1 END) as already_correct_count,
    COUNT(CASE WHEN uc.status = 2 THEN 1 END) as expired_count
FROM `weshop_user_coupon` uc
JOIN `weshop_order` o ON o.coupon_id = uc.id
WHERE o.coupon_price > 0;

-- 4. 执行修复：更新优惠券状态
UPDATE `weshop_user_coupon` uc
JOIN `weshop_order` o ON o.coupon_id = uc.id
SET 
    uc.status = 1,                    -- 设置为已使用
    uc.use_time = o.create_time,      -- 设置使用时间为订单创建时间
    uc.order_id = o.id                -- 关联订单ID
WHERE uc.status = 0                   -- 当前状态为可用
  AND o.coupon_price > 0              -- 订单确实使用了优惠券
  AND o.order_status != 4;            -- 排除已取消的订单

-- 5. 验证修复结果
SELECT 
    '=== 修复结果验证 ===' as info,
    COUNT(*) as total_fixed,
    MIN(o.create_time) as earliest_order,
    MAX(o.create_time) as latest_order
FROM `weshop_user_coupon` uc
JOIN `weshop_order` o ON o.coupon_id = uc.id
WHERE uc.status = 1 AND uc.order_id = o.id AND o.coupon_price > 0;

-- 6. 检查是否还有未修复的数据
SELECT 
    '=== 未修复数据检查 ===' as info,
    COUNT(*) as remaining_issues,
    CASE 
        WHEN COUNT(*) = 0 THEN '✓ 所有数据已修复'
        ELSE CONCAT('✗ 还有 ', COUNT(*), ' 条数据需要处理')
    END as status
FROM `weshop_user_coupon` uc
JOIN `weshop_order` o ON o.coupon_id = uc.id
WHERE uc.status = 0 AND o.coupon_price > 0 AND o.order_status != 4;

-- 7. 处理特殊情况：一券多用检查
SELECT 
    '=== 一券多用检查 ===' as info,
    uc.id as coupon_id,
    uc.title,
    COUNT(o.id) as order_count,
    GROUP_CONCAT(o.id ORDER BY o.create_time) as order_ids,
    GROUP_CONCAT(o.order_sn ORDER BY o.create_time) as order_sns,
    CASE 
        WHEN COUNT(o.id) > 1 THEN '✗ 发现一券多用'
        ELSE '✓ 正常'
    END as check_result
FROM `weshop_user_coupon` uc
JOIN `weshop_order` o ON o.coupon_id = uc.id
WHERE o.coupon_price > 0
GROUP BY uc.id, uc.title
HAVING COUNT(o.id) > 1;

-- 8. 处理一券多用情况（如果存在）
-- 只保留最早的订单使用记录，其他订单的优惠券使用记录需要人工处理
UPDATE `weshop_user_coupon` uc
JOIN (
    SELECT 
        uc.id as coupon_id,
        MIN(o.id) as first_order_id,
        MIN(o.create_time) as first_order_time
    FROM `weshop_user_coupon` uc
    JOIN `weshop_order` o ON o.coupon_id = uc.id
    WHERE o.coupon_price > 0
    GROUP BY uc.id
    HAVING COUNT(o.id) > 1
) first_usage ON uc.id = first_usage.coupon_id
SET 
    uc.order_id = first_usage.first_order_id,
    uc.use_time = first_usage.first_order_time,
    uc.status = 1;

-- 9. 生成一券多用问题报告（需要人工处理）
SELECT 
    '=== 一券多用问题报告（需要人工处理）===' as info,
    o.id as order_id,
    o.order_sn,
    o.user_id,
    o.coupon_id,
    o.coupon_price,
    o.actual_price,
    o.order_status,
    o.create_time,
    '需要人工审核：可能需要调整订单金额或取消订单' as action_needed
FROM `weshop_order` o
WHERE o.coupon_id IN (
    SELECT uc.id
    FROM `weshop_user_coupon` uc
    JOIN `weshop_order` o2 ON o2.coupon_id = uc.id
    WHERE o2.coupon_price > 0
    GROUP BY uc.id
    HAVING COUNT(o2.id) > 1
)
AND o.coupon_price > 0
AND o.id NOT IN (
    SELECT uc.order_id 
    FROM `weshop_user_coupon` uc 
    WHERE uc.order_id IS NOT NULL
)
ORDER BY o.create_time;

-- 10. 最终验证报告
SELECT 
    '=== 最终验证报告 ===' as report,
    (SELECT COUNT(*) FROM `weshop_user_coupon` WHERE status = 0) as available_coupons,
    (SELECT COUNT(*) FROM `weshop_user_coupon` WHERE status = 1) as used_coupons,
    (SELECT COUNT(*) FROM `weshop_user_coupon` WHERE status = 2) as expired_coupons,
    (SELECT COUNT(*) FROM `weshop_order` WHERE coupon_id > 0 AND coupon_price > 0) as orders_with_coupons,
    (SELECT COUNT(*) FROM `weshop_user_coupon` uc 
     JOIN `weshop_order` o ON o.coupon_id = uc.id 
     WHERE uc.status = 1 AND uc.order_id = o.id) as correctly_linked_coupons;

-- 11. 数据一致性检查
SELECT 
    '=== 数据一致性检查 ===' as check_type,
    CASE 
        WHEN NOT EXISTS (
            SELECT 1 FROM `weshop_user_coupon` uc 
            JOIN `weshop_order` o ON o.coupon_id = uc.id 
            WHERE uc.status = 0 AND o.coupon_price > 0 AND o.order_status != 4
        ) THEN '✓ 优惠券状态一致性正常'
        ELSE '✗ 仍存在状态不一致的优惠券'
    END as coupon_consistency,
    CASE 
        WHEN NOT EXISTS (
            SELECT 1 FROM `weshop_user_coupon` uc
            JOIN `weshop_order` o ON o.coupon_id = uc.id
            WHERE uc.status = 1 AND uc.order_id != o.id
        ) THEN '✓ 优惠券订单关联正确'
        ELSE '✗ 存在订单关联错误的优惠券'
    END as order_link_consistency;

-- 12. 清理备份表（可选，建议保留一段时间）
-- DROP TABLE IF EXISTS `weshop_user_coupon_backup`;
