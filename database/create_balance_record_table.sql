-- 创建用户余额使用记录表
-- 用于追踪用户余额的充值、使用、退款等操作

CREATE TABLE IF NOT EXISTS `weshop_balance_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `type` varchar(20) NOT NULL COMMENT '操作类型：recharge-充值, use-使用, refund-退款, adjust-调整',
  `amount` decimal(10,2) NOT NULL COMMENT '金额（正数为增加，负数为减少）',
  `balance_before` decimal(10,2) NOT NULL COMMENT '操作前余额',
  `balance_after` decimal(10,2) NOT NULL COMMENT '操作后余额',
  `source` varchar(50) NOT NULL COMMENT '来源：order-订单, recharge-充值, refund-退款, manual-手动调整',
  `source_id` bigint(20) DEFAULT NULL COMMENT '来源ID（如订单ID、充值记录ID等）',
  `description` varchar(255) DEFAULT NULL COMMENT '操作描述',
  `operator_id` bigint(20) DEFAULT NULL COMMENT '操作员ID（管理员操作时记录）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_source` (`source`, `source_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户余额使用记录表';

-- 为现有用户创建初始余额记录（如果有余额的话）
INSERT INTO `weshop_balance_record` (`user_id`, `type`, `amount`, `balance_before`, `balance_after`, `source`, `description`)
SELECT 
    u.id,
    'adjust',
    u.balance,
    0,
    u.balance,
    'manual',
    '系统初始化余额记录'
FROM `weshop_user` u 
WHERE u.balance > 0
  AND NOT EXISTS (
    SELECT 1 FROM `weshop_balance_record` br 
    WHERE br.user_id = u.id
  );

-- 验证余额记录表创建结果
SELECT 
    '=== 余额记录表创建验证 ===' as info,
    COUNT(*) as total_records,
    COUNT(DISTINCT user_id) as users_with_records,
    SUM(CASE WHEN type = 'adjust' THEN 1 ELSE 0 END) as initial_records,
    SUM(amount) as total_balance_amount
FROM `weshop_balance_record`;

-- 检查用户余额与记录的一致性
SELECT 
    '=== 余额一致性检查 ===' as check_type,
    u.id as user_id,
    u.username,
    u.balance as current_balance,
    COALESCE(SUM(br.amount), 0) as calculated_balance,
    CASE 
        WHEN u.balance = COALESCE(SUM(br.amount), 0) THEN '✓ 一致'
        ELSE CONCAT('✗ 不一致，差额：', u.balance - COALESCE(SUM(br.amount), 0))
    END as consistency_status
FROM `weshop_user` u
LEFT JOIN `weshop_balance_record` br ON br.user_id = u.id
WHERE u.balance > 0 OR EXISTS (SELECT 1 FROM `weshop_balance_record` WHERE user_id = u.id)
GROUP BY u.id, u.username, u.balance
ORDER BY u.id
LIMIT 10;

-- 显示余额记录统计
SELECT 
    '=== 余额记录统计 ===' as stats,
    type as operation_type,
    COUNT(*) as record_count,
    SUM(amount) as total_amount,
    AVG(amount) as avg_amount,
    MIN(amount) as min_amount,
    MAX(amount) as max_amount
FROM `weshop_balance_record`
GROUP BY type
ORDER BY type;
