-- 快速测试数据脚本
-- 用于快速创建测试环境，验证充值和优惠券功能

-- ==========================================
-- 快速创建测试用户和数据
-- ==========================================

-- 创建测试用户（如果不存在）
INSERT IGNORE INTO weshop_user (id, username, nickname, mobile, balance, register_time, wechat_open_id) VALUES
(999, 'quicktest', '快速测试用户', '13999999999', 0.00, NOW(), 'openid_quick_test');

-- ==========================================
-- 场景1：500元充值 → 获得5张10元券
-- ==========================================

-- 1. 创建充值记录
INSERT INTO weshop_recharge_record (
    user_id, amount, coupon_count, coupon_amount, status, payment_method,
    wx_order_id, wx_transaction_id, create_time, pay_time, remark
) VALUES (
    999, 500.00, 5, 10.00, 1, 1,
    'QUICK_TEST_500_' || UNIX_TIMESTAMP(NOW()),
    'wx_quick_test_' || UNIX_TIMESTAMP(NOW()),
    NOW(), NOW(),
    '快速测试：充值500元赠送5张10元消费券'
);

-- 2. 更新用户余额
UPDATE weshop_user SET balance = balance + 500.00 WHERE id = 999;

-- 3. 生成对应的优惠券
INSERT INTO weshop_user_coupon (
    user_id, coupon_id, coupon_number, title, description, type, type_name,
    amount, min_amount, status, create_time, start_time, end_time
) VALUES
(999, 1, 'QUICK_TEST_001', '10元消费券', '充值500元赠送，满50元可用', 1, '满减券', 10.00, 50.00, 0, NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 90 DAY)),
(999, 1, 'QUICK_TEST_002', '10元消费券', '充值500元赠送，满50元可用', 1, '满减券', 10.00, 50.00, 0, NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 90 DAY)),
(999, 1, 'QUICK_TEST_003', '10元消费券', '充值500元赠送，满50元可用', 1, '满减券', 10.00, 50.00, 0, NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 90 DAY)),
(999, 1, 'QUICK_TEST_004', '10元消费券', '充值500元赠送，满50元可用', 1, '满减券', 10.00, 50.00, 0, NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 90 DAY)),
(999, 1, 'QUICK_TEST_005', '10元消费券', '充值500元赠送，满50元可用', 1, '满减券', 10.00, 50.00, 0, NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 90 DAY));

-- ==========================================
-- 场景2：1000元充值 → 获得5张20元券
-- ==========================================

-- 等待1秒，确保时间戳不同
SELECT SLEEP(1);

-- 1. 创建充值记录
INSERT INTO weshop_recharge_record (
    user_id, amount, coupon_count, coupon_amount, status, payment_method,
    wx_order_id, wx_transaction_id, create_time, pay_time, remark
) VALUES (
    999, 1000.00, 5, 20.00, 1, 1,
    'QUICK_TEST_1000_' || UNIX_TIMESTAMP(NOW()),
    'wx_quick_test_' || UNIX_TIMESTAMP(NOW()),
    NOW(), NOW(),
    '快速测试：充值1000元赠送5张20元消费券'
);

-- 2. 更新用户余额
UPDATE weshop_user SET balance = balance + 1000.00 WHERE id = 999;

-- 3. 生成对应的优惠券
INSERT INTO weshop_user_coupon (
    user_id, coupon_id, coupon_number, title, description, type, type_name,
    amount, min_amount, status, create_time, start_time, end_time
) VALUES
(999, 2, 'QUICK_TEST_006', '20元消费券', '充值1000元赠送，满100元可用', 1, '满减券', 20.00, 100.00, 0, NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 90 DAY)),
(999, 2, 'QUICK_TEST_007', '20元消费券', '充值1000元赠送，满100元可用', 1, '满减券', 20.00, 100.00, 0, NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 90 DAY)),
(999, 2, 'QUICK_TEST_008', '20元消费券', '充值1000元赠送，满100元可用', 1, '满减券', 20.00, 100.00, 0, NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 90 DAY)),
(999, 2, 'QUICK_TEST_009', '20元消费券', '充值1000元赠送，满100元可用', 1, '满减券', 20.00, 100.00, 0, NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 90 DAY)),
(999, 2, 'QUICK_TEST_010', '20元消费券', '充值1000元赠送，满100元可用', 1, '满减券', 20.00, 100.00, 0, NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 90 DAY));

-- ==========================================
-- 验证结果
-- ==========================================

-- 查看用户信息
SELECT 
    id, username, nickname, balance,
    CONCAT('余额：', balance, '元') as balance_info
FROM weshop_user WHERE id = 999;

-- 查看充值记录
SELECT 
    amount, coupon_count, coupon_amount, status,
    CASE WHEN status = 1 THEN '已支付' ELSE '待支付' END as status_text,
    remark,
    DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as create_time
FROM weshop_recharge_record 
WHERE user_id = 999 
ORDER BY create_time;

-- 查看优惠券统计
SELECT 
    COUNT(*) as total_coupons,
    SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as available_coupons,
    SUM(CASE WHEN status = 0 THEN amount ELSE 0 END) as total_available_amount,
    CONCAT(
        '可用优惠券：', 
        SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END), 
        '张，总价值：', 
        SUM(CASE WHEN status = 0 THEN amount ELSE 0 END), 
        '元'
    ) as coupon_summary
FROM weshop_user_coupon 
WHERE user_id = 999;

-- 查看优惠券详情
SELECT 
    title, amount, min_amount,
    CONCAT('满', min_amount, '元可用') as usage_rule,
    DATE_FORMAT(end_time, '%Y-%m-%d') as expire_date
FROM weshop_user_coupon 
WHERE user_id = 999 AND status = 0
ORDER BY amount DESC, create_time;

-- ==========================================
-- 测试用例提示
-- ==========================================

SELECT 
    '快速测试数据创建完成！' as message,
    '用户ID: 999' as test_user,
    '账户余额: 1500元' as balance,
    '可用优惠券: 10张（5张10元券 + 5张20元券）' as coupons,
    '总优惠券价值: 150元' as coupon_value;

SELECT 
    '测试建议：' as tip,
    '1. 测试50元订单使用10元券' as test1,
    '2. 测试100元订单使用20元券' as test2,
    '3. 测试150元订单使用20元券+余额' as test3,
    '4. 测试30元订单无法使用券' as test4;