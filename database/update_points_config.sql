-- 更新积分配置以支持积分兑换功能
-- 100元获得100积分，100积分抵扣1元

UPDATE `weshop_points_config` 
SET 
    `earn_rate` = 1.00,      -- 每消费1元获得1积分
    `use_rate` = 100.00,     -- 100积分抵扣1元
    `min_use_points` = 100,  -- 最少使用100积分
    `max_use_ratio` = 100.00, -- 最大使用比例100%（可以全部用积分抵扣）
    `is_enabled` = 1,        -- 启用积分系统
    `update_time` = NOW()
WHERE `id` = 1;

-- 如果没有配置记录，则插入默认配置
INSERT IGNORE INTO `weshop_points_config` (`earn_rate`, `use_rate`, `min_use_points`, `max_use_ratio`, `is_enabled`) 
VALUES (1.00, 100.00, 100, 100.00, 1);