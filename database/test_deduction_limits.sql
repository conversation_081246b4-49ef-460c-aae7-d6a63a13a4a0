-- 测试积分、余额、优惠券组合使用时的抵扣金额限制
-- 验证总抵扣金额不会超过订单金额

-- 设置测试用户ID（请根据实际情况修改）
SET @test_user_id = 1;

-- 1. 检查测试用户的初始状态
SELECT 
    '=== 测试用户初始状态 ===' as step,
    u.id as user_id,
    u.username,
    u.balance as current_balance,
    u.points as current_points
FROM `weshop_user` u 
WHERE u.id = @test_user_id;

-- 2. 为测试用户准备充足的余额和积分
-- 添加余额记录
INSERT INTO `weshop_balance_record` (`user_id`, `type`, `amount`, `balance_before`, `balance_after`, `source`, `description`)
SELECT 
    @test_user_id,
    'recharge',
    200.00,
    u.balance,
    u.balance + 200.00,
    'manual',
    '测试抵扣限制-充值余额'
FROM `weshop_user` u
WHERE u.id = @test_user_id 
  AND u.balance < 200.00;

-- 更新用户余额
UPDATE `weshop_user` u
SET u.balance = u.balance + 200.00
WHERE u.id = @test_user_id 
  AND u.balance < 200.00;

-- 添加积分记录
INSERT INTO `weshop_points_record` (`user_id`, `type`, `points`, `source`, `description`)
SELECT 
    @test_user_id,
    'earn',
    10000,
    'manual',
    '测试抵扣限制-赠送积分'
FROM `weshop_user` u
WHERE u.id = @test_user_id 
  AND u.points < 10000;

-- 更新用户积分
UPDATE `weshop_user` u
SET u.points = (
    SELECT COALESCE(SUM(points), 0) 
    FROM `weshop_points_record` 
    WHERE user_id = @test_user_id
)
WHERE u.id = @test_user_id;

-- 3. 创建测试优惠券
INSERT IGNORE INTO `weshop_user_coupon` (`user_id`, `title`, `amount`, `min_amount`, `status`, `start_time`, `end_time`)
VALUES 
(@test_user_id, '测试优惠券-50元', 50.00, 100.00, 0, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY)),
(@test_user_id, '测试优惠券-30元', 30.00, 50.00, 0, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY)),
(@test_user_id, '测试优惠券-20元', 20.00, 30.00, 0, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY));

-- 4. 显示测试用户的完整状态
SELECT 
    '=== 测试用户完整状态 ===' as step,
    u.id as user_id,
    u.username,
    u.balance as current_balance,
    u.points as current_points,
    COUNT(uc.id) as available_coupons
FROM `weshop_user` u
LEFT JOIN `weshop_user_coupon` uc ON uc.user_id = u.id AND uc.status = 0
WHERE u.id = @test_user_id
GROUP BY u.id, u.username, u.balance, u.points;

-- 5. 显示可用优惠券
SELECT 
    '=== 可用优惠券 ===' as step,
    uc.id as coupon_id,
    uc.title,
    uc.amount as coupon_amount,
    uc.min_amount,
    uc.status
FROM `weshop_user_coupon` uc
WHERE uc.user_id = @test_user_id AND uc.status = 0
ORDER BY uc.amount DESC;

-- 6. 模拟不同订单金额的抵扣测试
SELECT 
    '=== 抵扣金额测试场景 ===' as test_scenario,
    test_data.order_amount as '订单金额(元)',
    test_data.coupon_amount as '优惠券抵扣(元)',
    test_data.points_deduction as '积分抵扣(元)',
    test_data.balance_deduction as '余额抵扣(元)',
    (test_data.coupon_amount + test_data.points_deduction + test_data.balance_deduction) as '总抵扣金额(元)',
    CASE 
        WHEN (test_data.coupon_amount + test_data.points_deduction + test_data.balance_deduction) > test_data.order_amount 
        THEN CONCAT('✗ 超额抵扣 ', (test_data.coupon_amount + test_data.points_deduction + test_data.balance_deduction - test_data.order_amount), '元')
        ELSE '✓ 抵扣正常'
    END as '抵扣状态',
    GREATEST(0, test_data.order_amount - test_data.coupon_amount - test_data.points_deduction - test_data.balance_deduction) as '实付金额(元)'
FROM (
    -- 测试场景1：小额订单，各种抵扣都超额
    SELECT 50.00 as order_amount, 30.00 as coupon_amount, 20.00 as points_deduction, 50.00 as balance_deduction
    UNION ALL
    -- 测试场景2：中等订单，总抵扣刚好等于订单金额
    SELECT 100.00 as order_amount, 50.00 as coupon_amount, 30.00 as points_deduction, 20.00 as balance_deduction
    UNION ALL
    -- 测试场景3：中等订单，总抵扣超过订单金额
    SELECT 100.00 as order_amount, 50.00 as coupon_amount, 50.00 as points_deduction, 50.00 as balance_deduction
    UNION ALL
    -- 测试场景4：大额订单，各种抵扣都正常
    SELECT 200.00 as order_amount, 50.00 as coupon_amount, 30.00 as points_deduction, 50.00 as balance_deduction
    UNION ALL
    -- 测试场景5：超大额订单，抵扣金额相对较小
    SELECT 500.00 as order_amount, 50.00 as coupon_amount, 50.00 as points_deduction, 100.00 as balance_deduction
) test_data;

-- 7. 按优先级调整后的抵扣金额计算
SELECT 
    '=== 按优先级调整后的抵扣计算 ===' as adjusted_calculation,
    test_data.order_amount as '订单金额',
    test_data.coupon_amount as '优惠券(优先级1)',
    CASE 
        WHEN test_data.coupon_amount >= test_data.order_amount THEN test_data.order_amount
        ELSE test_data.coupon_amount
    END as '调整后优惠券',
    CASE 
        WHEN test_data.coupon_amount >= test_data.order_amount THEN 0
        WHEN (test_data.coupon_amount + test_data.points_deduction) > test_data.order_amount 
        THEN (test_data.order_amount - test_data.coupon_amount)
        ELSE test_data.points_deduction
    END as '调整后积分',
    CASE 
        WHEN (test_data.coupon_amount + test_data.points_deduction) >= test_data.order_amount THEN 0
        WHEN (test_data.coupon_amount + test_data.points_deduction + test_data.balance_deduction) > test_data.order_amount 
        THEN (test_data.order_amount - test_data.coupon_amount - LEAST(test_data.points_deduction, test_data.order_amount - test_data.coupon_amount))
        ELSE test_data.balance_deduction
    END as '调整后余额',
    test_data.order_amount - 
    LEAST(test_data.coupon_amount, test_data.order_amount) - 
    LEAST(test_data.points_deduction, GREATEST(0, test_data.order_amount - LEAST(test_data.coupon_amount, test_data.order_amount))) -
    LEAST(test_data.balance_deduction, GREATEST(0, test_data.order_amount - LEAST(test_data.coupon_amount, test_data.order_amount) - LEAST(test_data.points_deduction, GREATEST(0, test_data.order_amount - LEAST(test_data.coupon_amount, test_data.order_amount)))))
    as '最终实付金额'
FROM (
    SELECT 50.00 as order_amount, 30.00 as coupon_amount, 20.00 as points_deduction, 50.00 as balance_deduction
    UNION ALL
    SELECT 100.00 as order_amount, 50.00 as coupon_amount, 50.00 as points_deduction, 50.00 as balance_deduction
    UNION ALL
    SELECT 200.00 as order_amount, 50.00 as coupon_amount, 30.00 as points_deduction, 50.00 as balance_deduction
) test_data;

-- 8. 积分配置检查
SELECT 
    '=== 积分配置检查 ===' as config_check,
    pc.use_rate as '积分汇率(积分/元)',
    pc.max_use_ratio as '最大使用比例(%)',
    pc.min_use_points as '最少使用积分',
    CASE 
        WHEN pc.max_use_ratio = 100 THEN '✓ 允许全额抵扣'
        ELSE CONCAT('⚠ 限制', pc.max_use_ratio, '%抵扣')
    END as '抵扣限制'
FROM `weshop_points_config` pc
WHERE pc.id = 1;

-- 9. 实际订单抵扣情况检查
SELECT 
    '=== 实际订单抵扣情况 ===' as real_orders,
    o.id as order_id,
    o.order_sn,
    o.goods_price as '商品金额',
    o.freight_price as '运费',
    (o.goods_price + o.freight_price) as '订单基础金额',
    o.coupon_price as '优惠券抵扣',
    o.integral_money as '积分抵扣',
    o.balance_price as '余额抵扣',
    (o.coupon_price + o.integral_money + o.balance_price) as '总抵扣金额',
    o.actual_price as '实付金额',
    CASE 
        WHEN (o.coupon_price + o.integral_money + o.balance_price) > (o.goods_price + o.freight_price) 
        THEN '✗ 抵扣超额'
        WHEN (o.goods_price + o.freight_price - o.coupon_price - o.integral_money - o.balance_price) != o.actual_price
        THEN '✗ 计算错误'
        ELSE '✓ 正常'
    END as '状态检查',
    o.create_time
FROM `weshop_order` o
WHERE o.user_id = @test_user_id
  AND (o.coupon_price > 0 OR o.integral_money > 0 OR o.balance_price > 0)
ORDER BY o.create_time DESC
LIMIT 10;

-- 10. 总结报告
SELECT 
    '=== 抵扣限制测试总结 ===' as summary,
    (SELECT COUNT(*) FROM `weshop_order` WHERE user_id = @test_user_id AND (coupon_price + integral_money + balance_price) > (goods_price + freight_price)) as '超额抵扣订单数',
    (SELECT COUNT(*) FROM `weshop_order` WHERE user_id = @test_user_id AND coupon_price > 0) as '使用优惠券订单数',
    (SELECT COUNT(*) FROM `weshop_order` WHERE user_id = @test_user_id AND integral_money > 0) as '使用积分订单数',
    (SELECT COUNT(*) FROM `weshop_order` WHERE user_id = @test_user_id AND balance_price > 0) as '使用余额订单数',
    (SELECT COUNT(*) FROM `weshop_order` WHERE user_id = @test_user_id AND coupon_price > 0 AND integral_money > 0 AND balance_price > 0) as '三种抵扣同时使用订单数';
