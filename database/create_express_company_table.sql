-- 创建快递公司表
CREATE TABLE IF NOT EXISTS `express_company` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '快递公司ID',
  `name` varchar(100) NOT NULL COMMENT '快递公司名称',
  `code` varchar(50) NOT NULL COMMENT '快递公司编码',
  `short_name` varchar(50) DEFAULT NULL COMMENT '快递公司简称',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 1-启用 0-禁用',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`),
  KEY `idx_status_sort` (`status`, `sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='快递公司表';

-- 插入默认快递公司数据
INSERT INTO `express_company` (`name`, `code`, `short_name`, `status`, `sort`) VALUES
('顺丰速运', 'SF', '顺丰', 1, 1),
('中通快递', 'ZTO', '中通', 1, 2),
('圆通速递', 'YTO', '圆通', 1, 3),
('申通快递', 'STO', '申通', 1, 4),
('韵达速递', 'YD', '韵达', 1, 5),
('百世快递', 'HTKY', '百世', 1, 6),
('德邦快递', 'DBL', '德邦', 1, 7),
('京东快递', 'JD', '京东', 1, 8),
('邮政快递包裹', 'YZPY', '邮政', 1, 9),
('EMS', 'EMS', 'EMS', 1, 10),
('天天快递', 'HHTT', '天天', 1, 11),
('宅急送', 'ZJS', '宅急送', 1, 12),
('国通快递', 'GTO', '国通', 1, 13),
('全峰快递', 'QFKD', '全峰', 1, 14),
('优速快递', 'UC', '优速', 1, 15),
('中国快递服务', 'CCES', '中快', 1, 16),
('安能快递', 'ANE', '安能', 1, 17),
('快捷快递', 'FAST', '快捷', 1, 18),
('ADP国际快递', 'ADP', 'ADP', 1, 19),
('DHL', 'DHL', 'DHL', 1, 20);