-- 阶梯推广统计数据问题诊断SQL脚本
-- 用于快速定位新用户通过推广链接下单后统计数据缺失的问题

-- ==========================================
-- 1. 检查最近的推广订单情况
-- ==========================================
SELECT 
    '最近推广订单情况' as check_type,
    o.id as order_id,
    o.order_sn,
    o.user_id,
    o.promoter_id,
    o.order_status,
    o.pay_status,
    o.actual_price,
    o.promotion_commission,
    o.create_time,
    o.pay_time
FROM weshop_order o
WHERE o.promoter_id IS NOT NULL 
ORDER BY o.create_time DESC 
LIMIT 10;

-- ==========================================
-- 2. 检查推广订单对应的商品信息
-- ==========================================
SELECT 
    '推广订单商品信息' as check_type,
    o.id as order_id,
    o.order_sn,
    o.promoter_id,
    o.pay_status,
    og.goods_id,
    og.goods_name,
    tpg.tier1_rate,
    tpg.is_active as is_tiered_goods,
    CASE 
        WHEN tpg.goods_id IS NOT NULL THEN '阶梯推广商品'
        ELSE '普通商品'
    END as goods_type
FROM weshop_order o
JOIN weshop_order_goods og ON o.id = og.order_id
LEFT JOIN weshop_tiered_promotion_goods tpg ON og.goods_id = tpg.goods_id AND tpg.is_active = 1
WHERE o.promoter_id IS NOT NULL 
AND o.pay_status = 'PAID'
ORDER BY o.create_time DESC 
LIMIT 10;

-- ==========================================
-- 3. 检查推广收益记录创建情况
-- ==========================================
SELECT 
    '推广收益记录情况' as check_type,
    pe.promoter_id,
    pe.promoted_user_id,
    pe.order_id,
    pe.order_no,
    pe.goods_id,
    pe.goods_name,
    pe.promotion_order_count,
    pe.is_tiered_promotion,
    pe.commission_rate,
    pe.commission_amount,
    pe.status,
    pe.create_time
FROM weshop_promotion_earnings pe
ORDER BY pe.create_time DESC 
LIMIT 10;

-- ==========================================
-- 4. 检查推广统计数据情况
-- ==========================================
SELECT 
    '推广统计数据情况' as check_type,
    ups.promoter_id,
    ups.goods_id,
    ups.total_promotion_count,
    ups.total_commission,
    ups.tier1_count,
    ups.tier2_count,
    ups.tier3_count,
    ups.tier4_plus_count,
    ups.first_promotion_time,
    ups.last_promotion_time,
    ups.create_time,
    ups.update_time
FROM weshop_user_goods_promotion_stats ups
ORDER BY ups.create_time DESC 
LIMIT 10;

-- ==========================================
-- 5. 检查数据一致性问题
-- ==========================================
SELECT 
    '数据一致性检查' as check_type,
    pe.promoter_id,
    pe.goods_id,
    COUNT(*) as earnings_count,
    SUM(pe.commission_amount) as total_earnings,
    COALESCE(ups.total_promotion_count, 0) as stats_count,
    COALESCE(ups.total_commission, 0) as stats_commission,
    CASE 
        WHEN ups.promoter_id IS NULL THEN '缺少统计记录'
        WHEN COUNT(*) != ups.total_promotion_count THEN '推广次数不一致'
        WHEN ABS(SUM(pe.commission_amount) - ups.total_commission) > 0.01 THEN '佣金金额不一致'
        ELSE '数据一致'
    END as consistency_status
FROM weshop_promotion_earnings pe
LEFT JOIN weshop_user_goods_promotion_stats ups 
    ON pe.promoter_id = ups.promoter_id AND pe.goods_id = ups.goods_id
WHERE pe.is_tiered_promotion = 1 
AND pe.status != 'cancelled'
GROUP BY pe.promoter_id, pe.goods_id, ups.total_promotion_count, ups.total_commission
ORDER BY pe.promoter_id, pe.goods_id;

-- ==========================================
-- 6. 检查推广者购买资格
-- ==========================================
SELECT 
    '推广者购买资格检查' as check_type,
    promoter_purchases.promoter_id,
    promoter_purchases.goods_id,
    promoter_purchases.goods_name,
    promoter_purchases.purchase_count,
    promoter_purchases.first_purchase_time,
    CASE 
        WHEN promoter_purchases.purchase_count > 0 THEN '有购买资格'
        ELSE '无购买资格'
    END as qualification_status
FROM (
    SELECT 
        o.user_id as promoter_id,
        og.goods_id,
        og.goods_name,
        COUNT(*) as purchase_count,
        MIN(o.pay_time) as first_purchase_time
    FROM weshop_order o
    JOIN weshop_order_goods og ON o.id = og.order_id
    WHERE o.pay_status = 'PAID' 
    AND o.order_status != 'CANCELLED'
    GROUP BY o.user_id, og.goods_id, og.goods_name
) promoter_purchases
WHERE promoter_purchases.promoter_id IN (
    SELECT DISTINCT promoter_id 
    FROM weshop_promotion_earnings 
    WHERE is_tiered_promotion = 1
)
ORDER BY promoter_purchases.promoter_id, promoter_purchases.goods_id;

-- ==========================================
-- 7. 检查阶梯推广商品配置
-- ==========================================
SELECT 
    '阶梯推广商品配置' as check_type,
    tpg.goods_id,
    tpg.goods_name,
    tpg.tier1_rate,
    tpg.tier2_rate,
    tpg.tier3_rate,
    tpg.tier4_plus_rate,
    tpg.is_active,
    tpg.create_time,
    tpg.description
FROM weshop_tiered_promotion_goods tpg
WHERE tpg.is_active = 1
ORDER BY tpg.create_time DESC;

-- ==========================================
-- 8. 查找问题订单（有推广收益但无统计数据）
-- ==========================================
SELECT 
    '问题订单分析' as check_type,
    pe.order_id,
    pe.order_no,
    pe.promoter_id,
    pe.goods_id,
    pe.goods_name,
    pe.is_tiered_promotion,
    pe.promotion_order_count,
    pe.commission_amount,
    pe.status,
    pe.create_time,
    CASE 
        WHEN ups.promoter_id IS NULL THEN '缺少统计记录'
        ELSE '有统计记录'
    END as stats_status
FROM weshop_promotion_earnings pe
LEFT JOIN weshop_user_goods_promotion_stats ups 
    ON pe.promoter_id = ups.promoter_id AND pe.goods_id = ups.goods_id
WHERE pe.is_tiered_promotion = 1 
AND pe.status != 'cancelled'
AND ups.promoter_id IS NULL
ORDER BY pe.create_time DESC;

-- ==========================================
-- 9. 检查最近的用户推广关系
-- ==========================================
SELECT 
    '用户推广关系' as check_type,
    u.id as user_id,
    u.nickname,
    u.promoter_id,
    u.create_time as user_create_time,
    promoter.nickname as promoter_nickname,
    CASE 
        WHEN u.promoter_id IS NOT NULL THEN '有推广关系'
        ELSE '无推广关系'
    END as relation_status
FROM weshop_user u
LEFT JOIN weshop_user promoter ON u.promoter_id = promoter.id
WHERE u.create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
ORDER BY u.create_time DESC 
LIMIT 20;

-- ==========================================
-- 10. 统计汇总信息
-- ==========================================
SELECT 
    '统计汇总' as check_type,
    '推广订单总数' as metric_name,
    COUNT(*) as metric_value
FROM weshop_order 
WHERE promoter_id IS NOT NULL AND pay_status = 'PAID'

UNION ALL

SELECT 
    '统计汇总' as check_type,
    '推广收益记录总数' as metric_name,
    COUNT(*) as metric_value
FROM weshop_promotion_earnings 
WHERE status != 'cancelled'

UNION ALL

SELECT 
    '统计汇总' as check_type,
    '阶梯推广收益记录数' as metric_name,
    COUNT(*) as metric_value
FROM weshop_promotion_earnings 
WHERE is_tiered_promotion = 1 AND status != 'cancelled'

UNION ALL

SELECT 
    '统计汇总' as check_type,
    '推广统计记录总数' as metric_name,
    COUNT(*) as metric_value
FROM weshop_user_goods_promotion_stats

UNION ALL

SELECT 
    '统计汇总' as check_type,
    '阶梯推广商品数量' as metric_name,
    COUNT(*) as metric_value
FROM weshop_tiered_promotion_goods 
WHERE is_active = 1;

-- ==========================================
-- 使用说明
-- ==========================================
/*
使用方法：
1. 执行上述SQL脚本，查看各项检查结果
2. 重点关注以下几个方面：
   - 推广订单是否正确设置了promoter_id
   - 商品是否配置为阶梯推广商品
   - 推广收益记录是否正常创建
   - 推广统计数据是否缺失
   - 推广者是否有购买资格

常见问题排查：
1. 如果推广订单的promoter_id为空 → 检查推广关系建立逻辑
2. 如果商品不是阶梯推广商品 → 检查商品配置
3. 如果有推广收益记录但无统计数据 → 检查统计更新逻辑
4. 如果推广者无购买资格 → 检查资格验证逻辑

修复建议：
1. 对于缺失的统计数据，可以手动执行统计更新
2. 对于配置问题，需要更新商品配置
3. 对于代码逻辑问题，需要修复相应的服务方法
*/