-- 测试余额抵扣功能
-- 验证余额使用后的状态变化和记录完整性

-- 设置测试用户ID（请根据实际情况修改）
SET @test_user_id = 1;

-- 1. 检查测试用户的初始状态
SELECT 
    '=== 测试用户初始状态 ===' as step,
    u.id as user_id,
    u.username,
    u.balance as current_balance,
    u.points as current_points
FROM `weshop_user` u 
WHERE u.id = @test_user_id;

-- 2. 检查余额记录表是否存在
SELECT 
    '=== 余额记录表检查 ===' as step,
    CASE 
        WHEN COUNT(*) > 0 THEN '✓ 余额记录表已创建'
        ELSE '✗ 余额记录表不存在'
    END as table_status,
    COUNT(*) as total_records
FROM information_schema.tables 
WHERE table_schema = DATABASE() 
  AND table_name = 'weshop_balance_record';

-- 3. 为测试用户添加余额（如果余额不足）
INSERT INTO `weshop_balance_record` (`user_id`, `type`, `amount`, `balance_before`, `balance_after`, `source`, `description`)
SELECT 
    @test_user_id,
    'recharge',
    100.00,
    u.balance,
    u.balance + 100.00,
    'manual',
    '测试充值余额'
FROM `weshop_user` u
WHERE u.id = @test_user_id 
  AND u.balance < 50.00;

-- 更新用户余额
UPDATE `weshop_user` u
SET u.balance = u.balance + 100.00
WHERE u.id = @test_user_id 
  AND u.balance < 50.00;

-- 4. 检查用户余额记录
SELECT 
    '=== 用户余额记录 ===' as step,
    br.id,
    br.type,
    br.amount,
    br.balance_before,
    br.balance_after,
    br.source,
    br.source_id as order_id,
    br.description,
    br.create_time
FROM `weshop_balance_record` br 
WHERE br.user_id = @test_user_id 
ORDER BY br.create_time DESC
LIMIT 10;

-- 5. 检查订单中的余额使用情况
SELECT 
    '=== 订单中的余额使用 ===' as step,
    o.id as order_id,
    o.order_sn,
    o.goods_price,
    o.coupon_price,
    o.integral_money as points_deduction,
    o.balance_price,
    o.actual_price,
    o.order_status,
    o.pay_status,
    o.create_time
FROM `weshop_order` o 
WHERE o.user_id = @test_user_id
  AND o.balance_price > 0
ORDER BY o.create_time DESC
LIMIT 5;

-- 6. 验证余额使用状态一致性
SELECT 
    '=== 余额使用状态一致性检查 ===' as step,
    o.id as order_id,
    o.balance_price as order_balance_used,
    br.amount as record_amount,
    br.source_id as record_order_id,
    CASE 
        WHEN o.balance_price > 0 AND br.amount = -o.balance_price AND br.source_id = o.id THEN '✓ 状态一致'
        WHEN o.balance_price > 0 AND br.amount IS NULL THEN '✗ 缺少余额使用记录'
        WHEN o.balance_price > 0 AND br.amount != -o.balance_price THEN '✗ 余额金额不匹配'
        WHEN o.balance_price = 0 THEN '- 未使用余额'
        ELSE '✗ 状态异常'
    END as consistency_check
FROM `weshop_order` o
LEFT JOIN `weshop_balance_record` br ON br.source_id = o.id AND br.type = 'use' AND br.source = 'order'
WHERE o.user_id = @test_user_id
ORDER BY o.create_time DESC
LIMIT 5;

-- 7. 余额计算验证
SELECT 
    '=== 余额计算验证 ===' as step,
    u.balance as current_balance,
    COALESCE(SUM(br.amount), 0) as calculated_balance,
    CASE 
        WHEN u.balance = COALESCE(SUM(br.amount), 0) THEN '✓ 余额计算正确'
        ELSE CONCAT('✗ 余额计算不匹配，差额：', u.balance - COALESCE(SUM(br.amount), 0))
    END as balance_check
FROM `weshop_user` u
LEFT JOIN `weshop_balance_record` br ON br.user_id = u.id
WHERE u.id = @test_user_id
GROUP BY u.id, u.balance;

-- 8. 模拟不同订单金额的余额抵扣测试
SELECT 
    '=== 不同订单金额余额抵扣测试 ===' as step,
    test_amounts.amount as '订单金额(元)',
    u.balance as '用户余额',
    LEAST(u.balance, test_amounts.amount) as '可用余额',
    test_amounts.amount - LEAST(u.balance, test_amounts.amount) as '剩余需支付',
    CASE 
        WHEN u.balance >= test_amounts.amount THEN '✓ 可全额抵扣'
        WHEN u.balance > 0 THEN '✓ 可部分抵扣'
        ELSE '✗ 无法使用余额'
    END as deduction_status
FROM (
    SELECT 10.00 as amount UNION ALL
    SELECT 25.00 UNION ALL
    SELECT 50.00 UNION ALL
    SELECT 100.00 UNION ALL
    SELECT 200.00
) test_amounts
CROSS JOIN `weshop_user` u
WHERE u.id = @test_user_id;

-- 9. 余额使用统计
SELECT 
    '=== 余额使用统计 ===' as step,
    br.type as operation_type,
    COUNT(*) as record_count,
    SUM(br.amount) as total_amount,
    AVG(br.amount) as avg_amount,
    MIN(br.amount) as min_amount,
    MAX(br.amount) as max_amount,
    MIN(br.create_time) as earliest_time,
    MAX(br.create_time) as latest_time
FROM `weshop_balance_record` br
WHERE br.user_id = @test_user_id
GROUP BY br.type
ORDER BY br.type;

-- 10. 检查重复扣减风险
SELECT 
    '=== 重复扣减风险检查 ===' as step,
    br.source_id as order_id,
    COUNT(*) as record_count,
    SUM(br.amount) as total_deducted,
    GROUP_CONCAT(br.id) as record_ids,
    CASE 
        WHEN COUNT(*) = 1 THEN '✓ 正常'
        WHEN COUNT(*) > 1 THEN '✗ 发现重复扣减'
        ELSE '- 其他情况'
    END as risk_assessment
FROM `weshop_balance_record` br
WHERE br.user_id = @test_user_id 
  AND br.type = 'use' 
  AND br.source = 'order'
  AND br.source_id IS NOT NULL
GROUP BY br.source_id
HAVING COUNT(*) > 1;

-- 11. 余额流水完整性检查
SELECT 
    '=== 余额流水完整性检查 ===' as step,
    br.id,
    br.type,
    br.amount,
    br.balance_before,
    br.balance_after,
    br.balance_before + br.amount as calculated_after,
    CASE 
        WHEN br.balance_after = br.balance_before + br.amount THEN '✓ 计算正确'
        ELSE '✗ 计算错误'
    END as calculation_check,
    br.create_time
FROM `weshop_balance_record` br
WHERE br.user_id = @test_user_id
ORDER BY br.create_time DESC
LIMIT 10;

-- 12. 总结报告
SELECT 
    '=== 余额抵扣测试总结 ===' as result,
    (SELECT balance FROM `weshop_user` WHERE id = @test_user_id) as current_balance,
    (SELECT COUNT(*) FROM `weshop_balance_record` WHERE user_id = @test_user_id AND type = 'recharge') as recharge_count,
    (SELECT COUNT(*) FROM `weshop_balance_record` WHERE user_id = @test_user_id AND type = 'use') as use_count,
    (SELECT COUNT(*) FROM `weshop_balance_record` WHERE user_id = @test_user_id AND type = 'refund') as refund_count,
    (SELECT COUNT(*) FROM `weshop_order` WHERE user_id = @test_user_id AND balance_price > 0) as orders_with_balance;

-- 13. 问题诊断
SELECT 
    '=== 问题诊断 ===' as diagnosis,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM `weshop_order` o 
            WHERE o.user_id = @test_user_id AND o.balance_price > 0 
            AND NOT EXISTS (
                SELECT 1 FROM `weshop_balance_record` br 
                WHERE br.source_id = o.id AND br.type = 'use' AND br.amount = -o.balance_price
            )
        ) THEN '✗ 发现余额使用记录缺失的订单'
        ELSE '✓ 余额使用记录完整'
    END as balance_record_check,
    CASE 
        WHEN (
            SELECT u.balance FROM `weshop_user` u WHERE u.id = @test_user_id
        ) = (
            SELECT COALESCE(SUM(br.amount), 0) FROM `weshop_balance_record` br WHERE br.user_id = @test_user_id
        ) THEN '✓ 余额计算一致'
        ELSE '✗ 余额计算不一致'
    END as balance_consistency_check;
