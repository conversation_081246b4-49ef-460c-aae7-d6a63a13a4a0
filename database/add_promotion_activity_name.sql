-- 为阶梯推广商品表添加活动名称字段
-- 用于在商品卡片上显示活动标记

-- 添加活动名称字段
ALTER TABLE `weshop_tiered_promotion_goods` 
ADD COLUMN `name` varchar(100) DEFAULT NULL COMMENT '活动名称' AFTER `goods_name`;

-- 为现有数据设置默认活动名称
UPDATE `weshop_tiered_promotion_goods` 
SET `name` = '阶梯推广' 
WHERE `name` IS NULL AND `is_active` = 1;

-- 添加索引优化查询
ALTER TABLE `weshop_tiered_promotion_goods` 
ADD INDEX `idx_name_active` (`name`, `is_active`);

-- 查看更新结果
SELECT 
    goods_id,
    goods_name,
    name as activity_name,
    is_active,
    tier1_rate,
    tier2_rate,
    tier3_rate,
    tier4_plus_rate
FROM `weshop_tiered_promotion_goods` 
WHERE `is_active` = 1;