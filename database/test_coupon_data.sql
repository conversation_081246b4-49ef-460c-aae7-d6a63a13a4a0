-- 测试优惠券数据
-- 为用户添加一些测试优惠券

-- 插入测试优惠券数据（假设用户ID为1）
INSERT INTO weshop_user_coupon (
    user_id, coupon_id, coupon_number, title, description, 
    type, type_name, amount, min_amount, status, 
    create_time, start_time, end_time
) VALUES 
-- 10元满50减券
(1, 1, 'TEST_COUPON_001', '10元消费券', '满50元可用', 
 1, '满减券', 10.00, 50.00, 0, 
 NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY)),

-- 20元满100减券
(1, 2, 'TEST_COUPON_002', '20元消费券', '满100元可用', 
 1, '满减券', 20.00, 100.00, 0, 
 NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY)),

-- 5元满30减券
(1, 3, 'TEST_COUPON_003', '5元消费券', '满30元可用', 
 1, '满减券', 5.00, 30.00, 0, 
 NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY)),

-- 50元满200减券（高门槛）
(1, 4, 'TEST_COUPON_004', '50元消费券', '满200元可用', 
 1, '满减券', 50.00, 200.00, 0, 
 NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY)),

-- 已使用的优惠券
(1, 5, 'TEST_COUPON_005', '15元消费券', '满80元可用', 
 1, '满减券', 15.00, 80.00, 1, 
 DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_ADD(NOW(), INTERVAL 25 DAY)),

-- 已过期的优惠券
(1, 6, 'TEST_COUPON_006', '8元消费券', '满40元可用', 
 1, '满减券', 8.00, 40.00, 2, 
 DATE_SUB(NOW(), INTERVAL 35 DAY), DATE_SUB(NOW(), INTERVAL 35 DAY), DATE_SUB(NOW(), INTERVAL 5 DAY));

-- 为其他用户也添加一些测试数据（如果存在用户ID为2的用户）
INSERT INTO weshop_user_coupon (
    user_id, coupon_id, coupon_number, title, description, 
    type, type_name, amount, min_amount, status, 
    create_time, start_time, end_time
) VALUES 
(2, 1, 'TEST_COUPON_007', '10元消费券', '满50元可用', 
 1, '满减券', 10.00, 50.00, 0, 
 NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY)),

(2, 2, 'TEST_COUPON_008', '20元消费券', '满100元可用', 
 1, '满减券', 20.00, 100.00, 0, 
 NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY));

-- 查询验证数据
SELECT 
    id, user_id, title, amount, min_amount, status,
    DATE_FORMAT(start_time, '%Y-%m-%d') as start_date,
    DATE_FORMAT(end_time, '%Y-%m-%d') as end_date
FROM weshop_user_coupon 
ORDER BY user_id, create_time DESC;