# 积分兑换系统说明

## 功能概述

积分兑换系统实现了以下功能：
- 购物消费1元获得1积分
- 100积分可以抵扣1元现金
- 订单完成后自动发放积分
- 下单时可以使用积分抵扣

## 数据库表结构

### 1. 用户表 (weshop_user)
- 新增字段：`points` int(11) NOT NULL DEFAULT 0 COMMENT '用户积分'

### 2. 积分记录表 (weshop_points_record)
```sql
CREATE TABLE `weshop_points_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `type` varchar(20) NOT NULL COMMENT '积分类型：earn(获得), use(使用)',
  `points` int(11) NOT NULL COMMENT '积分数量（正数为获得，负数为使用）',
  `source` varchar(50) NOT NULL COMMENT '积分来源：order(订单), manual(手动调整)',
  `source_id` int(11) DEFAULT NULL COMMENT '来源ID（如订单ID）',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_source` (`source`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分记录表';
```

### 3. 积分配置表 (weshop_points_config)
```sql
CREATE TABLE `weshop_points_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `earn_rate` decimal(10,2) NOT NULL DEFAULT 1.00 COMMENT '积分获得比例（每消费多少元获得1积分）',
  `use_rate` decimal(10,2) NOT NULL DEFAULT 100.00 COMMENT '积分使用比例（多少积分抵扣1元）',
  `min_use_points` int(11) NOT NULL DEFAULT 100 COMMENT '最少使用积分数量',
  `max_use_ratio` decimal(5,2) NOT NULL DEFAULT 50.00 COMMENT '最大使用比例（订单金额的百分比）',
  `is_enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用积分系统',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分配置表';
```

## 后端接口

### 1. 积分信息接口
- **路径**: `/wechat/points/info`
- **方法**: GET
- **说明**: 获取用户积分信息和配置

### 2. 积分记录接口
- **路径**: `/wechat/points/records`
- **方法**: GET
- **参数**: page, size
- **说明**: 获取用户积分记录

### 3. 购物车结算接口（支持积分）
- **路径**: `/wechat/cart/checkout-with-points`
- **方法**: GET
- **参数**: addressId, couponId, usePoints
- **说明**: 购物车结算时支持积分抵扣

### 4. 高级结算接口
- **路径**: `/wechat/cart/advanced-checkout`
- **方法**: POST
- **参数**: addressId, couponId, useBalance, usePoints
- **说明**: 支持余额、优惠券、积分组合使用

## 前端页面

### 1. 我的积分页面
- **路径**: `pages/ucenter/points/points`
- **功能**: 显示用户积分、积分规则、积分记录

### 2. 用户中心更新
- **文件**: `pages/ucenter/me/me`
- **更新**: 显示用户积分信息

## 业务流程

### 1. 积分获得流程
1. 用户下单购买商品
2. 订单完成后，系统自动计算积分（消费金额 × 1）
3. 调用 `PointsService.earnPointsFromOrder()` 方法
4. 创建积分记录，更新用户积分总数

### 2. 积分使用流程
1. 用户在购物车结算时选择使用积分
2. 系统验证用户积分是否足够
3. 计算积分可抵扣金额（积分数 ÷ 100）
4. 创建订单时扣减积分
5. 调用 `PointsService.usePointsForOrder()` 方法

## 测试步骤

### 1. 数据库初始化
```sql
-- 执行积分系统创建脚本
source database/create_points_system.sql;

-- 执行积分配置更新脚本
source database/update_points_config.sql;
```

### 2. 测试积分获得
```sql
-- 执行积分兑换流程测试
source database/test_points_exchange_flow.sql;
```

### 3. 前端测试
1. 登录小程序
2. 进入"我的" -> "我的积分"页面
3. 查看积分余额和记录
4. 添加商品到购物车
5. 结算时使用积分抵扣
6. 完成订单后查看积分变化

## 配置说明

### 积分规则配置
- `earn_rate`: 1.00 (每消费1元获得1积分)
- `use_rate`: 100.00 (100积分抵扣1元)
- `min_use_points`: 100 (最少使用100积分)
- `max_use_ratio`: 100.00 (最大可使用100%的订单金额)

### 示例计算
- 购物100元 → 获得100积分
- 使用100积分 → 抵扣1元
- 实际支付 = 订单金额 - 积分抵扣金额

## 注意事项

1. 积分只有在订单完成后才会发放
2. 积分使用有最小限制（默认100积分）
3. 积分抵扣金额不能超过订单总金额
4. 系统支持积分、余额、优惠券组合使用
5. 所有积分操作都有详细记录，便于追踪和审计

## 错误处理

- `INSUFFICIENT_POINTS`: 积分不足
- `POINTS_USE_FAILED`: 积分使用失败
- `INSUFFICIENT_BALANCE`: 余额不足
- `BALANCE_USE_FAILED`: 余额使用失败