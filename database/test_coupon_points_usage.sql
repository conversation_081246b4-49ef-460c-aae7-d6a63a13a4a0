-- 测试积分和优惠券使用后的状态变化
-- 验证使用后是否正确标记为已使用，不能再次使用

-- 设置测试用户ID（请根据实际情况修改）
SET @test_user_id = 1;

-- 1. 检查测试用户的初始状态
SELECT 
    '=== 测试用户初始状态 ===' as step,
    u.id as user_id,
    u.username,
    u.points as current_points,
    u.balance as current_balance
FROM `weshop_user` u 
WHERE u.id = @test_user_id;

-- 2. 检查用户可用优惠券
SELECT 
    '=== 用户可用优惠券 ===' as step,
    uc.id as coupon_id,
    uc.title,
    uc.amount as coupon_amount,
    uc.min_amount,
    uc.status,
    CASE 
        WHEN uc.status = 0 THEN '可用'
        WHEN uc.status = 1 THEN '已使用'
        WHEN uc.status = 2 THEN '已过期'
        ELSE '未知状态'
    END as status_desc,
    uc.start_time,
    uc.end_time,
    uc.use_time,
    uc.order_id
FROM `weshop_user_coupon` uc 
WHERE uc.user_id = @test_user_id
ORDER BY uc.status, uc.create_time DESC;

-- 3. 检查用户积分记录
SELECT 
    '=== 用户积分记录 ===' as step,
    pr.id,
    pr.type,
    pr.points,
    pr.source,
    pr.source_id as order_id,
    pr.description,
    pr.create_time
FROM `weshop_points_record` pr 
WHERE pr.user_id = @test_user_id 
ORDER BY pr.create_time DESC
LIMIT 10;

-- 4. 检查用户订单中的积分和优惠券使用情况
SELECT 
    '=== 订单中的积分和优惠券使用 ===' as step,
    o.id as order_id,
    o.order_sn,
    o.goods_price,
    o.coupon_id,
    o.coupon_price,
    o.integral as used_points,
    o.integral_money as points_deduction,
    o.balance_price,
    o.actual_price,
    o.order_status,
    o.pay_status,
    o.create_time
FROM `weshop_order` o 
WHERE o.user_id = @test_user_id
ORDER BY o.create_time DESC
LIMIT 5;

-- 5. 验证优惠券使用状态一致性
SELECT 
    '=== 优惠券使用状态一致性检查 ===' as step,
    o.id as order_id,
    o.coupon_id,
    o.coupon_price,
    uc.status as coupon_status,
    uc.use_time,
    uc.order_id as coupon_order_id,
    CASE 
        WHEN o.coupon_id > 0 AND uc.status = 1 AND uc.order_id = o.id THEN '✓ 状态一致'
        WHEN o.coupon_id > 0 AND uc.status != 1 THEN '✗ 优惠券未标记为已使用'
        WHEN o.coupon_id > 0 AND uc.order_id != o.id THEN '✗ 优惠券关联订单不匹配'
        WHEN o.coupon_id = 0 THEN '- 未使用优惠券'
        ELSE '✗ 状态异常'
    END as consistency_check
FROM `weshop_order` o
LEFT JOIN `weshop_user_coupon` uc ON o.coupon_id = uc.id
WHERE o.user_id = @test_user_id AND o.coupon_id > 0
ORDER BY o.create_time DESC
LIMIT 5;

-- 6. 验证积分使用状态一致性
SELECT 
    '=== 积分使用状态一致性检查 ===' as step,
    o.id as order_id,
    o.integral as order_used_points,
    o.integral_money as order_points_deduction,
    pr.points as record_points,
    pr.source_id as record_order_id,
    CASE 
        WHEN o.integral > 0 AND pr.points = -o.integral AND pr.source_id = o.id THEN '✓ 状态一致'
        WHEN o.integral > 0 AND pr.points IS NULL THEN '✗ 缺少积分使用记录'
        WHEN o.integral > 0 AND pr.points != -o.integral THEN '✗ 积分数量不匹配'
        WHEN o.integral = 0 THEN '- 未使用积分'
        ELSE '✗ 状态异常'
    END as consistency_check
FROM `weshop_order` o
LEFT JOIN `weshop_points_record` pr ON pr.source_id = o.id AND pr.type = 'use' AND pr.source = 'order'
WHERE o.user_id = @test_user_id AND o.integral > 0
ORDER BY o.create_time DESC
LIMIT 5;

-- 7. 模拟重复使用检查
-- 检查是否有已使用的优惠券仍然显示为可用状态
SELECT 
    '=== 重复使用风险检查 ===' as step,
    uc.id as coupon_id,
    uc.title,
    uc.status,
    uc.use_time,
    uc.order_id,
    COUNT(o.id) as order_count,
    GROUP_CONCAT(o.id) as related_orders,
    CASE 
        WHEN uc.status = 1 AND COUNT(o.id) = 1 THEN '✓ 正常'
        WHEN uc.status = 1 AND COUNT(o.id) > 1 THEN '✗ 一券多用'
        WHEN uc.status = 1 AND COUNT(o.id) = 0 THEN '✗ 标记已使用但无关联订单'
        WHEN uc.status = 0 AND COUNT(o.id) > 0 THEN '✗ 已使用但状态未更新'
        ELSE '- 其他情况'
    END as risk_assessment
FROM `weshop_user_coupon` uc
LEFT JOIN `weshop_order` o ON o.coupon_id = uc.id
WHERE uc.user_id = @test_user_id
GROUP BY uc.id, uc.title, uc.status, uc.use_time, uc.order_id
HAVING COUNT(o.id) > 0 OR uc.status = 1
ORDER BY uc.status, uc.create_time DESC;

-- 8. 积分余额验证
SELECT 
    '=== 积分余额验证 ===' as step,
    u.points as current_points,
    COALESCE(SUM(pr.points), 0) as calculated_points,
    CASE 
        WHEN u.points = COALESCE(SUM(pr.points), 0) THEN '✓ 积分余额正确'
        ELSE CONCAT('✗ 积分余额不匹配，差额：', u.points - COALESCE(SUM(pr.points), 0))
    END as balance_check
FROM `weshop_user` u
LEFT JOIN `weshop_points_record` pr ON pr.user_id = u.id
WHERE u.id = @test_user_id
GROUP BY u.id, u.points;

-- 9. 总结报告
SELECT 
    '=== 测试总结报告 ===' as result,
    (SELECT COUNT(*) FROM `weshop_user_coupon` WHERE user_id = @test_user_id AND status = 0) as available_coupons,
    (SELECT COUNT(*) FROM `weshop_user_coupon` WHERE user_id = @test_user_id AND status = 1) as used_coupons,
    (SELECT COUNT(*) FROM `weshop_user_coupon` WHERE user_id = @test_user_id AND status = 2) as expired_coupons,
    (SELECT points FROM `weshop_user` WHERE id = @test_user_id) as current_points,
    (SELECT COUNT(*) FROM `weshop_points_record` WHERE user_id = @test_user_id AND type = 'use') as points_usage_records,
    (SELECT COUNT(*) FROM `weshop_order` WHERE user_id = @test_user_id AND coupon_id > 0) as orders_with_coupons,
    (SELECT COUNT(*) FROM `weshop_order` WHERE user_id = @test_user_id AND integral > 0) as orders_with_points;

-- 10. 问题诊断
SELECT 
    '=== 问题诊断 ===' as diagnosis,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM `weshop_user_coupon` uc 
            JOIN `weshop_order` o ON o.coupon_id = uc.id 
            WHERE uc.user_id = @test_user_id AND uc.status = 0
        ) THEN '✗ 发现已使用但状态未更新的优惠券'
        ELSE '✓ 优惠券状态更新正常'
    END as coupon_status_check,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM `weshop_order` o 
            WHERE o.user_id = @test_user_id AND o.integral > 0 
            AND NOT EXISTS (
                SELECT 1 FROM `weshop_points_record` pr 
                WHERE pr.source_id = o.id AND pr.type = 'use' AND pr.points = -o.integral
            )
        ) THEN '✗ 发现积分使用记录缺失的订单'
        ELSE '✓ 积分使用记录完整'
    END as points_record_check;
