-- 测试商品页面活动名称显示功能的数据脚本

-- 1. 确保数据库已添加name字段（如果还没有执行迁移）
-- ALTER TABLE `weshop_tiered_promotion_goods` 
-- ADD COLUMN `name` varchar(100) DEFAULT NULL COMMENT '活动名称' AFTER `goods_name`;

-- 2. 清理测试数据
DELETE FROM `weshop_tiered_promotion_goods` WHERE `goods_id` IN (1, 2, 3, 4);

-- 3. 插入测试数据
INSERT INTO `weshop_tiered_promotion_goods` 
(`goods_id`, `goods_name`, `name`, `is_active`, `tier1_rate`, `tier2_rate`, `tier3_rate`, `tier4_plus_rate`, `description`, `create_time`, `update_time`)
VALUES 
-- 有活动名称的商品
(1, '测试商品1', '限时推广活动', 1, 20.00, 30.00, 50.00, 10.00, '限时推广活动，前3笔享受高额返现', NOW(), NOW()),
(2, '测试商品2', '新品特惠', 1, 15.00, 25.00, 40.00, 8.00, '新品特惠活动，分享即可获得返现', NOW(), NOW()),
(3, '测试商品3', '会员专享', 1, 25.00, 35.00, 60.00, 12.00, '会员专享活动，更高返现比例', NOW(), NOW()),
-- 没有活动名称的商品（测试默认显示）
(4, '测试商品4', NULL, 1, 18.00, 28.00, 45.00, 9.00, '阶梯推广商品，无特定活动名称', NOW(), NOW());

-- 4. 验证插入的数据
SELECT 
    goods_id,
    goods_name,
    name as activity_name,
    is_active,
    CONCAT(tier1_rate, '%/', tier2_rate, '%/', tier3_rate, '%/', tier4_plus_rate, '%') as rates,
    description
FROM `weshop_tiered_promotion_goods` 
WHERE `goods_id` IN (1, 2, 3, 4)
ORDER BY `goods_id`;

-- 5. 测试API查询（模拟后端查询）
-- 查询有活动名称的商品
SELECT 
    goods_id,
    goods_name,
    name,
    is_active,
    tier1_rate,
    tier2_rate,
    tier3_rate,
    tier4_plus_rate,
    description
FROM `weshop_tiered_promotion_goods` 
WHERE `goods_id` = 1 AND `is_active` = 1;

-- 查询没有活动名称的商品
SELECT 
    goods_id,
    goods_name,
    name,
    is_active,
    tier1_rate,
    tier2_rate,
    tier3_rate,
    tier4_plus_rate,
    description
FROM `weshop_tiered_promotion_goods` 
WHERE `goods_id` = 4 AND `is_active` = 1;

-- 6. 检查商品基本信息（确保商品存在）
SELECT 
    id,
    name,
    list_pic_url,
    retail_price,
    is_on_sale,
    is_delete
FROM `weshop_goods` 
WHERE `id` IN (1, 2, 3, 4)
AND `is_delete` = 0
ORDER BY `id`;

-- 7. 测试不同场景的查询结果
-- 场景1：有活动名称的商品 - 应该显示活动名称
SELECT 
    CASE 
        WHEN name IS NOT NULL AND name != '' THEN name 
        ELSE '阶梯推广商品' 
    END as display_title,
    goods_id,
    name as original_name
FROM `weshop_tiered_promotion_goods` 
WHERE `goods_id` = 1 AND `is_active` = 1;

-- 场景2：没有活动名称的商品 - 应该显示默认文字
SELECT 
    CASE 
        WHEN name IS NOT NULL AND name != '' THEN name 
        ELSE '阶梯推广商品' 
    END as display_title,
    goods_id,
    name as original_name
FROM `weshop_tiered_promotion_goods` 
WHERE `goods_id` = 4 AND `is_active` = 1;

-- 8. 批量查询所有测试商品的显示标题
SELECT 
    goods_id,
    goods_name,
    name as activity_name,
    CASE 
        WHEN name IS NOT NULL AND name != '' THEN name 
        ELSE '阶梯推广商品' 
    END as display_title,
    is_active
FROM `weshop_tiered_promotion_goods` 
WHERE `goods_id` IN (1, 2, 3, 4) AND `is_active` = 1
ORDER BY `goods_id`;

-- 清理脚本（测试完成后可选执行）
/*
DELETE FROM `weshop_tiered_promotion_goods` WHERE `goods_id` IN (1, 2, 3, 4);
*/