-- 测试积分系统功能

-- 1. 确保积分配置正确
UPDATE `weshop_points_config` 
SET 
    `earn_rate` = 1.00,      -- 每消费1元获得1积分
    `use_rate` = 100.00,     -- 100积分抵扣1元
    `min_use_points` = 100,  -- 最少使用100积分
    `max_use_ratio` = 100.00, -- 最大使用比例100%
    `is_enabled` = 1,        -- 启用积分系统
    `update_time` = NOW()
WHERE `id` = 1;

-- 2. 为测试用户添加一些积分
-- 假设用户ID为1的用户
INSERT INTO `weshop_points_record` (`user_id`, `type`, `points`, `source`, `description`, `create_time`, `update_time`)
VALUES 
(1, 'earn', 500, 'manual', '系统测试赠送积分', NOW(), NOW()),
(1, 'earn', 300, 'order', '订单消费获得积分', NOW(), NOW()),
(1, 'use', -200, 'order', '订单使用积分抵扣', NOW(), NOW());

-- 3. 更新用户积分总数
UPDATE `weshop_user` 
SET `points` = (
    SELECT COALESCE(SUM(points), 0) 
    FROM `weshop_points_record` 
    WHERE `user_id` = 1
)
WHERE `id` = 1;

-- 4. 查看测试结果
SELECT 
    u.id as user_id,
    u.username,
    u.points as current_points,
    u.balance as current_balance
FROM `weshop_user` u 
WHERE u.id = 1;

-- 5. 查看积分记录
SELECT 
    pr.id,
    pr.user_id,
    pr.type,
    pr.points,
    pr.source,
    pr.description,
    pr.create_time
FROM `weshop_points_record` pr 
WHERE pr.user_id = 1 
ORDER BY pr.create_time DESC;

-- 6. 查看积分配置
SELECT * FROM `weshop_points_config` WHERE id = 1;