-- 创建用户优惠券表
CREATE TABLE IF NOT EXISTS `weshop_user_coupon` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `coupon_id` int(11) NOT NULL COMMENT '优惠券模板ID',
  `coupon_number` varchar(64) NOT NULL COMMENT '优惠券编号',
  `title` varchar(100) NOT NULL COMMENT '优惠券标题',
  `description` varchar(255) DEFAULT NULL COMMENT '优惠券描述',
  `type` int(1) NOT NULL DEFAULT 1 COMMENT '优惠券类型：1-满减券，2-折扣券，3-免邮券',
  `type_name` varchar(20) DEFAULT '满减券' COMMENT '优惠券类型名称',
  `amount` decimal(10,2) NOT NULL COMMENT '优惠金额/折扣',
  `min_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '最低消费金额',
  `status` int(1) NOT NULL DEFAULT 0 COMMENT '优惠券状态：0-可用，1-已使用，2-已过期',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '获得时间',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `use_time` datetime DEFAULT NULL COMMENT '使用时间',
  `order_id` int(11) DEFAULT NULL COMMENT '使用订单ID',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_coupon_number` (`coupon_number`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_end_time` (`end_time`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户优惠券表';

-- 插入一些测试数据（可选）
INSERT INTO `weshop_user_coupon` (`user_id`, `coupon_id`, `coupon_number`, `title`, `description`, `type`, `type_name`, `amount`, `min_amount`, `status`, `start_time`, `end_time`) VALUES
(1, 1, 'COUPON_TEST_001', '10元优惠券', '充值赠送优惠券', 1, '满减券', 10.00, 50.00, 0, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY)),
(1, 2, 'COUPON_TEST_002', '20元优惠券', '充值赠送优惠券', 1, '满减券', 20.00, 100.00, 0, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY)),
(1, 1, 'COUPON_TEST_003', '10元优惠券', '已使用的优惠券', 1, '满减券', 10.00, 50.00, 1, DATE_SUB(NOW(), INTERVAL 10 DAY), DATE_ADD(NOW(), INTERVAL 20 DAY)),
(1, 1, 'COUPON_TEST_004', '10元优惠券', '已过期的优惠券', 1, '满减券', 10.00, 50.00, 2, DATE_SUB(NOW(), INTERVAL 40 DAY), DATE_SUB(NOW(), INTERVAL 10 DAY));

-- 显示创建结果
SELECT 'User coupon tables created successfully' as result;