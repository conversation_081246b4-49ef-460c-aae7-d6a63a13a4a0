-- 阶梯式推广返现系统数据库扩展
-- 功能：指定产品的阶梯式返现推广

-- 1. 扩展推广收益表，添加商品相关字段
ALTER TABLE `weshop_promotion_earnings` 
ADD COLUMN `goods_id` int(11) DEFAULT NULL COMMENT '商品ID' AFTER `promoted_user_id`,
ADD COLUMN `goods_name` varchar(255) DEFAULT NULL COMMENT '商品名称' AFTER `goods_id`,
ADD COLUMN `promotion_order_count` int(11) DEFAULT 1 COMMENT '推广该商品的第几笔订单' AFTER `goods_name`,
ADD COLUMN `is_tiered_promotion` tinyint(1) DEFAULT 0 COMMENT '是否为阶梯推广商品：0-否，1-是' AFTER `promotion_order_count`;

-- 2. 创建阶梯推广商品配置表
CREATE TABLE IF NOT EXISTS `weshop_tiered_promotion_goods` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `goods_id` int(11) NOT NULL COMMENT '商品ID',
  `goods_name` varchar(255) NOT NULL COMMENT '商品名称',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否启用：0-禁用，1-启用',
  `tier1_rate` decimal(5,2) DEFAULT 20.00 COMMENT '第1笔返现比例（%）',
  `tier2_rate` decimal(5,2) DEFAULT 30.00 COMMENT '第2笔返现比例（%）',
  `tier3_rate` decimal(5,2) DEFAULT 50.00 COMMENT '第3笔返现比例（%）',
  `tier4_plus_rate` decimal(5,2) DEFAULT 10.00 COMMENT '第4笔及以后返现比例（%）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator_id` int(11) DEFAULT NULL COMMENT '创建者ID',
  `description` text COMMENT '商品推广描述',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_goods_id` (`goods_id`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='阶梯推广商品配置表';

-- 3. 创建用户商品推广统计表
CREATE TABLE IF NOT EXISTS `weshop_user_goods_promotion_stats` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `promoter_id` int(11) NOT NULL COMMENT '推广者用户ID',
  `goods_id` int(11) NOT NULL COMMENT '商品ID',
  `total_promotion_count` int(11) DEFAULT 0 COMMENT '该商品总推广次数',
  `tier1_count` int(11) DEFAULT 0 COMMENT '第1阶梯推广次数',
  `tier2_count` int(11) DEFAULT 0 COMMENT '第2阶梯推广次数',
  `tier3_count` int(11) DEFAULT 0 COMMENT '第3阶梯推广次数',
  `tier4_plus_count` int(11) DEFAULT 0 COMMENT '第4阶梯及以后推广次数',
  `total_commission` decimal(10,2) DEFAULT 0.00 COMMENT '该商品总佣金',
  `first_promotion_time` datetime DEFAULT NULL COMMENT '首次推广时间',
  `last_promotion_time` datetime DEFAULT NULL COMMENT '最后推广时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_promoter_goods` (`promoter_id`, `goods_id`),
  KEY `idx_promoter_id` (`promoter_id`),
  KEY `idx_goods_id` (`goods_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户商品推广统计表';

-- 4. 插入测试数据 - 配置一个阶梯推广商品
-- 假设商品ID为1的商品启用阶梯推广
INSERT IGNORE INTO `weshop_tiered_promotion_goods` 
(`goods_id`, `goods_name`, `is_active`, `tier1_rate`, `tier2_rate`, `tier3_rate`, `tier4_plus_rate`, `description`)
VALUES 
(1, '测试阶梯推广商品', 1, 20.00, 30.00, 50.00, 10.00, '这是一个测试的阶梯推广商品，前3笔分别返现20%、30%、50%，第4笔及以后返现10%');

-- 5. 创建相关索引优化查询性能
ALTER TABLE `weshop_promotion_earnings` 
ADD INDEX `idx_promoter_goods` (`promoter_id`, `goods_id`),
ADD INDEX `idx_goods_tiered` (`goods_id`, `is_tiered_promotion`);

-- 6. 创建视图，方便查询阶梯推广统计
CREATE OR REPLACE VIEW `v_tiered_promotion_stats` AS
SELECT 
    tpg.goods_id,
    tpg.goods_name,
    tpg.is_active,
    tpg.tier1_rate,
    tpg.tier2_rate,
    tpg.tier3_rate,
    tpg.tier4_plus_rate,
    COALESCE(SUM(ugps.total_promotion_count), 0) as total_promotions,
    COALESCE(SUM(ugps.tier1_count), 0) as tier1_promotions,
    COALESCE(SUM(ugps.tier2_count), 0) as tier2_promotions,
    COALESCE(SUM(ugps.tier3_count), 0) as tier3_promotions,
    COALESCE(SUM(ugps.tier4_plus_count), 0) as tier4_plus_promotions,
    COALESCE(SUM(ugps.total_commission), 0) as total_commission,
    COUNT(DISTINCT ugps.promoter_id) as active_promoters
FROM `weshop_tiered_promotion_goods` tpg
LEFT JOIN `weshop_user_goods_promotion_stats` ugps ON tpg.goods_id = ugps.goods_id
GROUP BY tpg.goods_id, tpg.goods_name, tpg.is_active, tpg.tier1_rate, tpg.tier2_rate, tpg.tier3_rate, tpg.tier4_plus_rate;

-- 7. 创建存储过程，用于计算阶梯返现比例
DELIMITER $$

CREATE PROCEDURE `sp_calculate_tiered_commission`(
    IN p_promoter_id INT,
    IN p_goods_id INT,
    IN p_order_amount DECIMAL(10,2),
    OUT p_commission_rate DECIMAL(5,2),
    OUT p_commission_amount DECIMAL(10,2),
    OUT p_promotion_order_count INT
)
BEGIN
    DECLARE v_tier1_rate DECIMAL(5,2) DEFAULT 20.00;
    DECLARE v_tier2_rate DECIMAL(5,2) DEFAULT 30.00;
    DECLARE v_tier3_rate DECIMAL(5,2) DEFAULT 50.00;
    DECLARE v_tier4_plus_rate DECIMAL(5,2) DEFAULT 10.00;
    DECLARE v_current_count INT DEFAULT 0;
    DECLARE v_is_tiered_goods INT DEFAULT 0;
    
    -- 检查是否为阶梯推广商品
    SELECT COUNT(*) INTO v_is_tiered_goods
    FROM `weshop_tiered_promotion_goods`
    WHERE `goods_id` = p_goods_id AND `is_active` = 1;
    
    IF v_is_tiered_goods = 0 THEN
        -- 不是阶梯推广商品，使用默认10%返现
        SET p_commission_rate = 10.00;
        SET p_commission_amount = p_order_amount * p_commission_rate / 100;
        SET p_promotion_order_count = 1;
    ELSE
        -- 获取阶梯推广配置
        SELECT `tier1_rate`, `tier2_rate`, `tier3_rate`, `tier4_plus_rate`
        INTO v_tier1_rate, v_tier2_rate, v_tier3_rate, v_tier4_plus_rate
        FROM `weshop_tiered_promotion_goods`
        WHERE `goods_id` = p_goods_id AND `is_active` = 1;
        
        -- 获取当前推广次数
        SELECT COALESCE(`total_promotion_count`, 0) INTO v_current_count
        FROM `weshop_user_goods_promotion_stats`
        WHERE `promoter_id` = p_promoter_id AND `goods_id` = p_goods_id;
        
        -- 计算这是第几笔推广
        SET p_promotion_order_count = v_current_count + 1;
        
        -- 根据推广次数确定返现比例
        IF p_promotion_order_count = 1 THEN
            SET p_commission_rate = v_tier1_rate;
        ELSEIF p_promotion_order_count = 2 THEN
            SET p_commission_rate = v_tier2_rate;
        ELSEIF p_promotion_order_count = 3 THEN
            SET p_commission_rate = v_tier3_rate;
        ELSE
            SET p_commission_rate = v_tier4_plus_rate;
        END IF;
        
        -- 计算返现金额
        SET p_commission_amount = p_order_amount * p_commission_rate / 100;
    END IF;
    
END$$

DELIMITER ;

-- 8. 创建触发器，自动更新推广统计
DELIMITER $$

CREATE TRIGGER `tr_update_promotion_stats_after_insert`
AFTER INSERT ON `weshop_promotion_earnings`
FOR EACH ROW
BEGIN
    IF NEW.is_tiered_promotion = 1 AND NEW.goods_id IS NOT NULL THEN
        -- 更新或插入推广统计
        INSERT INTO `weshop_user_goods_promotion_stats` 
        (`promoter_id`, `goods_id`, `total_promotion_count`, `tier1_count`, `tier2_count`, `tier3_count`, `tier4_plus_count`, `total_commission`, `first_promotion_time`, `last_promotion_time`)
        VALUES 
        (NEW.promoter_id, NEW.goods_id, 1, 
         CASE WHEN NEW.promotion_order_count = 1 THEN 1 ELSE 0 END,
         CASE WHEN NEW.promotion_order_count = 2 THEN 1 ELSE 0 END,
         CASE WHEN NEW.promotion_order_count = 3 THEN 1 ELSE 0 END,
         CASE WHEN NEW.promotion_order_count >= 4 THEN 1 ELSE 0 END,
         NEW.commission_amount, NEW.create_time, NEW.create_time)
        ON DUPLICATE KEY UPDATE
        `total_promotion_count` = `total_promotion_count` + 1,
        `tier1_count` = `tier1_count` + CASE WHEN NEW.promotion_order_count = 1 THEN 1 ELSE 0 END,
        `tier2_count` = `tier2_count` + CASE WHEN NEW.promotion_order_count = 2 THEN 1 ELSE 0 END,
        `tier3_count` = `tier3_count` + CASE WHEN NEW.promotion_order_count = 3 THEN 1 ELSE 0 END,
        `tier4_plus_count` = `tier4_plus_count` + CASE WHEN NEW.promotion_order_count >= 4 THEN 1 ELSE 0 END,
        `total_commission` = `total_commission` + NEW.commission_amount,
        `last_promotion_time` = NEW.create_time,
        `update_time` = NOW();
    END IF;
END$$

DELIMITER ;

-- 9. 查询语句示例
-- 查看阶梯推广商品配置
SELECT * FROM `weshop_tiered_promotion_goods` WHERE `is_active` = 1;

-- 查看用户的商品推广统计
SELECT 
    ugps.*,
    u.nickname as promoter_nickname,
    g.name as goods_name
FROM `weshop_user_goods_promotion_stats` ugps
LEFT JOIN `weshop_user` u ON ugps.promoter_id = u.id
LEFT JOIN `weshop_goods` g ON ugps.goods_id = g.id
ORDER BY ugps.total_commission DESC;

-- 查看阶梯推广统计视图
SELECT * FROM `v_tiered_promotion_stats`;