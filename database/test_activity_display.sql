-- 测试商品活动标记功能的数据脚本

-- 1. 首先执行数据库迁移（如果还没有执行）
-- ALTER TABLE `weshop_tiered_promotion_goods` 
-- ADD COLUMN `name` varchar(100) DEFAULT NULL COMMENT '活动名称' AFTER `goods_name`;

-- 2. 清理测试数据
DELETE FROM `weshop_tiered_promotion_goods` WHERE `goods_id` IN (1, 2, 3);

-- 3. 插入测试活动配置
INSERT INTO `weshop_tiered_promotion_goods` 
(`goods_id`, `goods_name`, `name`, `is_active`, `tier1_rate`, `tier2_rate`, `tier3_rate`, `tier4_plus_rate`, `description`, `create_time`, `update_time`)
VALUES 
(1, '测试商品1', '限时推广', 1, 20.00, 30.00, 50.00, 10.00, '限时推广活动', NOW(), NOW()),
(2, '测试商品2', '新品特惠', 1, 15.00, 25.00, 40.00, 8.00, '新品特惠活动', NOW(), NOW()),
(3, '测试商品3', '会员专享', 1, 25.00, 35.00, 60.00, 12.00, '会员专享活动', NOW(), NOW());

-- 4. 验证数据插入
SELECT 
    goods_id,
    goods_name,
    name as activity_name,
    is_active,
    CONCAT(tier1_rate, '%/', tier2_rate, '%/', tier3_rate, '%/', tier4_plus_rate, '%') as rates
FROM `weshop_tiered_promotion_goods` 
WHERE `goods_id` IN (1, 2, 3)
ORDER BY `goods_id`;

-- 5. 测试批量查询活动信息的SQL（模拟后端查询）
SELECT goods_id, name, 'tiered_promotion' as activity_type 
FROM weshop_tiered_promotion_goods 
WHERE is_active = 1 AND goods_id IN (1, 2, 3, 4, 5);

-- 6. 查看商品基本信息（确保商品存在）
SELECT 
    id,
    name,
    list_pic_url,
    retail_price,
    is_on_sale,
    is_delete
FROM `weshop_goods` 
WHERE `id` IN (1, 2, 3, 4, 5)
AND `is_delete` = 0
ORDER BY `id`;

-- 清理脚本（测试完成后可选执行）
/*
DELETE FROM `weshop_tiered_promotion_goods` WHERE `goods_id` IN (1, 2, 3);
*/