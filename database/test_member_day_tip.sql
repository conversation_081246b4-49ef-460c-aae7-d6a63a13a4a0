-- 会员日提示功能测试SQL

-- 1. 查看当前所有提示信息
SELECT * FROM member_day_tip ORDER BY priority DESC, create_time DESC;

-- 2. 查看商品页面的有效提示信息
SELECT * FROM member_day_tip 
WHERE is_enabled = 1 
AND (display_position = 'goods' OR display_position = 'all') 
AND (start_time IS NULL OR start_time <= NOW()) 
AND (end_time IS NULL OR end_time >= NOW()) 
ORDER BY priority DESC, create_time DESC 
LIMIT 1;

-- 3. 插入测试数据
INSERT INTO member_day_tip (tip_text, is_enabled, display_position, priority, start_time, end_time, create_by, update_by) VALUES
('🎉 周末狂欢！会员专享双倍积分奖励', 1, 'goods', 15, NOW(), DATE_ADD(NOW(), INTERVAL 2 DAY), 'test', 'test'),
('💰 限时特惠！新用户首单立减50元', 1, 'all', 12, NOW(), DATE_ADD(NOW(), INTERVAL 7 DAY), 'test', 'test'),
('🔥 热销爆款！每日限量抢购中', 0, 'goods', 8, NOW(), DATE_ADD(NOW(), INTERVAL 1 DAY), 'test', 'test');

-- 4. 测试时间范围过滤
-- 插入一个未来生效的提示
INSERT INTO member_day_tip (tip_text, is_enabled, display_position, priority, start_time, end_time, create_by, update_by) VALUES
('🚀 即将上线！敬请期待新功能', 1, 'goods', 20, DATE_ADD(NOW(), INTERVAL 1 DAY), DATE_ADD(NOW(), INTERVAL 3 DAY), 'test', 'test');

-- 插入一个已过期的提示
INSERT INTO member_day_tip (tip_text, is_enabled, display_position, priority, start_time, end_time, create_by, update_by) VALUES
('📅 已过期的活动提示', 1, 'goods', 18, DATE_SUB(NOW(), INTERVAL 3 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY), 'test', 'test');

-- 5. 验证查询结果（应该只返回当前有效的提示）
SELECT 
    id,
    tip_text,
    is_enabled,
    display_position,
    priority,
    start_time,
    end_time,
    CASE 
        WHEN start_time IS NULL OR start_time <= NOW() THEN '时间范围有效'
        ELSE '未到开始时间'
    END as start_status,
    CASE 
        WHEN end_time IS NULL OR end_time >= NOW() THEN '时间范围有效'
        ELSE '已过结束时间'
    END as end_status
FROM member_day_tip 
WHERE is_enabled = 1 
AND (display_position = 'goods' OR display_position = 'all')
ORDER BY priority DESC, create_time DESC;

-- 6. 测试不同位置的提示
-- 首页提示
INSERT INTO member_day_tip (tip_text, is_enabled, display_position, priority, create_by, update_by) VALUES
('🏠 首页专属提示：欢迎来到我们的商城', 1, 'index', 10, 'test', 'test');

-- 全局提示
INSERT INTO member_day_tip (tip_text, is_enabled, display_position, priority, create_by, update_by) VALUES
('🌟 全站通知：系统维护时间调整', 1, 'all', 5, 'test', 'test');

-- 7. 查看各个位置的有效提示
-- 商品页面
SELECT '商品页面' as page_type, tip_text, priority FROM member_day_tip 
WHERE is_enabled = 1 
AND (display_position = 'goods' OR display_position = 'all') 
AND (start_time IS NULL OR start_time <= NOW()) 
AND (end_time IS NULL OR end_time >= NOW()) 
ORDER BY priority DESC, create_time DESC 
LIMIT 1;

-- 首页
SELECT '首页' as page_type, tip_text, priority FROM member_day_tip 
WHERE is_enabled = 1 
AND (display_position = 'index' OR display_position = 'all') 
AND (start_time IS NULL OR start_time <= NOW()) 
AND (end_time IS NULL OR end_time >= NOW()) 
ORDER BY priority DESC, create_time DESC 
LIMIT 1;

-- 8. 清理测试数据（可选）
-- DELETE FROM member_day_tip WHERE create_by = 'test';

-- 9. 验证默认数据是否存在
SELECT 
    COUNT(*) as total_count,
    SUM(CASE WHEN is_enabled = 1 THEN 1 ELSE 0 END) as enabled_count,
    SUM(CASE WHEN display_position = 'goods' THEN 1 ELSE 0 END) as goods_page_count
FROM member_day_tip;

-- 10. 性能测试查询
EXPLAIN SELECT * FROM member_day_tip 
WHERE is_enabled = 1 
AND (display_position = 'goods' OR display_position = 'all') 
AND (start_time IS NULL OR start_time <= NOW()) 
AND (end_time IS NULL OR end_time >= NOW()) 
ORDER BY priority DESC, create_time DESC 
LIMIT 1;