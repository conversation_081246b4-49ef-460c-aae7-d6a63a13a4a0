# Checkout页面优惠券抵扣和积分抵扣功能测试指南

## 功能概述

本次更新为checkout页面添加了以下功能：
1. 优惠券选择和抵扣
2. 积分抵扣
3. 余额抵扣
4. 多种抵扣方式组合使用

## 测试准备

### 1. 数据库准备
执行以下SQL文件来准备测试数据：
```sql
-- 创建优惠券相关表
source database/create_coupon_tables.sql;

-- 创建积分系统表
source database/create_points_system.sql;

-- 添加用户余额字段
source database/add_user_balance_column.sql;

-- 插入测试优惠券数据
source database/test_coupon_data.sql;

-- 插入测试积分数据
source database/quick_test_points.sql;
```

### 2. 用户数据准备
确保测试用户具有：
- 足够的积分（建议1000+积分）
- 一定的余额（建议100+元）
- 多张不同面额的优惠券

## 测试用例

### 测试用例1：优惠券选择功能
1. 进入checkout页面
2. 点击"优惠券"卡片
3. 验证跳转到优惠券选择页面
4. 验证可用优惠券和不可用优惠券正确分类
5. 选择一张优惠券并确认
6. 验证返回checkout页面后优惠券信息正确显示
7. 验证价格计算正确

### 测试用例2：积分抵扣功能
1. 在checkout页面找到"积分抵扣"卡片
2. 开启积分抵扣开关
3. 输入要使用的积分数量
4. 验证积分抵扣金额计算正确（100积分=1元）
5. 验证不能超过最大可用积分
6. 验证最终价格计算正确

### 测试用例3：余额抵扣功能
1. 在checkout页面找到"余额抵扣"卡片
2. 开启余额抵扣开关
3. 输入要使用的余额金额
4. 验证不能超过用户余额和订单金额
5. 验证最终价格计算正确

### 测试用例4：组合抵扣功能
1. 同时使用优惠券、积分和余额
2. 验证抵扣顺序：优惠券 -> 积分 -> 余额
3. 验证最终实付金额正确
4. 提交订单验证扣减正确

### 测试用例5：边界条件测试
1. 测试积分不足的情况
2. 测试余额不足的情况
3. 测试优惠券不满足使用条件
4. 测试输入非法数值
5. 测试网络异常情况

## 预期结果

### 优惠券功能
- 能够正确显示可用和不可用优惠券
- 选择优惠券后价格计算正确
- 优惠券使用状态正确更新

### 积分功能
- 积分抵扣金额计算正确（100积分=1元）
- 不能超过最大可用积分限制
- 积分使用后用户积分正确扣减

### 余额功能
- 余额抵扣金额不能超过用户余额
- 余额使用后用户余额正确扣减

### 组合使用
- 多种抵扣方式可以同时使用
- 抵扣顺序正确
- 最终实付金额计算准确

## 注意事项

1. 确保后端API接口正常运行
2. 检查数据库表结构是否正确创建
3. 验证用户权限和数据安全
4. 测试不同设备和网络环境下的表现

## 故障排除

### 常见问题
1. **优惠券列表为空**：检查数据库中是否有测试优惠券数据
2. **积分抵扣不生效**：检查积分配置表是否正确设置
3. **余额显示异常**：检查用户表是否添加了balance字段
4. **价格计算错误**：检查后端计算逻辑和前端显示逻辑

### 调试方法
1. 查看浏览器控制台错误信息
2. 检查网络请求和响应数据
3. 查看后端日志文件
4. 使用数据库查询验证数据状态

## 更新内容总结

### 前端更新
- 新增优惠券选择页面
- 添加积分抵扣组件
- 添加余额抵扣组件
- 优化价格计算和显示逻辑

### 后端更新
- 完善购物车高级结算接口
- 优化优惠券服务
- 完善积分服务
- 增强订单提交逻辑

### 数据库更新
- 用户优惠券表结构优化
- 积分系统表完善
- 用户余额字段添加