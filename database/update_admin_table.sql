-- 管理员表结构更新脚本
-- 添加status、real_name、roles字段

-- 检查并添加status字段（管理员状态：1启用，0禁用）
ALTER TABLE `weshop_admin` 
ADD COLUMN IF NOT EXISTS `status` tinyint(1) DEFAULT 1 COMMENT '管理员状态：1启用，0禁用' AFTER `admin_role_id`;

-- 检查并添加real_name字段（真实姓名）
ALTER TABLE `weshop_admin` 
ADD COLUMN IF NOT EXISTS `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名' AFTER `username`;

-- 检查并添加roles字段（角色名称）
ALTER TABLE `weshop_admin` 
ADD COLUMN IF NOT EXISTS `roles` varchar(200) DEFAULT NULL COMMENT '角色名称' AFTER `admin_role_id`;

-- 更新现有数据，为没有状态的管理员设置默认状态为启用
UPDATE `weshop_admin` SET `status` = 1 WHERE `status` IS NULL;

-- 更新现有数据，为没有真实姓名的管理员设置默认值
UPDATE `weshop_admin` SET `real_name` = `username` WHERE `real_name` IS NULL OR `real_name` = '';

-- 更新现有数据，为没有角色的管理员设置默认角色
UPDATE `weshop_admin` SET `roles` = '超级管理员' WHERE `id` = 1 AND (`roles` IS NULL OR `roles` = '');
UPDATE `weshop_admin` SET `roles` = '普通管理员' WHERE `id` != 1 AND (`roles` IS NULL OR `roles` = '');

-- 输出执行结果
SELECT 'weshop_admin表字段更新完成' as result;