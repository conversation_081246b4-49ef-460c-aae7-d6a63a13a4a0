// 测试custom-navbar集成
console.log('=== 测试custom-navbar集成 ===');

// 模拟导航栏初始化
function initNavbar() {
  const systemInfo = {
    statusBarHeight: 20,
    getSystemInfoSync: function() {
      return this;
    }
  };
  
  const statusBarHeight = systemInfo.statusBarHeight || 0;
  const titleBarHeight = 44;
  const navbarHeight = statusBarHeight + titleBarHeight;
  
  console.log('导航栏高度计算:', {
    statusBarHeight,
    titleBarHeight,
    navbarHeight
  });
  
  return {
    navbarHeight,
    navOpacity: 0.95
  };
}

// 模拟页面滚动透明度计算
function onPageScroll(scrollTop) {
  let opacity = Math.min(1, Math.max(0.95, 0.95 + (scrollTop / 200)));
  console.log(`滚动位置: ${scrollTop}, 导航栏透明度: ${opacity.toFixed(2)}`);
  return opacity;
}

// 模拟导航栏返回事件
function onNavBack(drillDownLevel) {
  if (drillDownLevel > 0) {
    console.log('在下钻状态，执行drillBack()');
    return 'drillBack';
  } else {
    console.log('不在下钻状态，执行正常返回');
    return 'navigateBack';
  }
}

// 运行测试
console.log('1. 测试导航栏初始化');
const navbarData = initNavbar();
console.log('导航栏数据:', navbarData);

console.log('\n2. 测试页面滚动透明度');
onPageScroll(0);
onPageScroll(50);
onPageScroll(100);
onPageScroll(200);

console.log('\n3. 测试导航栏返回逻辑');
console.log('下钻层级为0时:', onNavBack(0));
console.log('下钻层级为1时:', onNavBack(1));
console.log('下钻层级为2时:', onNavBack(2));

console.log('\n✅ custom-navbar集成测试通过');

console.log('\n=== 测试完成 ===');