<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>积分抵扣订单API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        .form-group input, .form-group select {
            width: 200px;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result-area {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <h1>积分抵扣订单API测试工具</h1>
    
    <!-- 获取积分抵扣订单列表 -->
    <div class="test-section">
        <h3>1. 获取积分抵扣订单列表</h3>
        <div class="form-group">
            <label>页码:</label>
            <input type="number" id="page" value="1" min="1">
        </div>
        <div class="form-group">
            <label>每页大小:</label>
            <input type="number" id="limit" value="15" min="1" max="100">
        </div>
        <div class="form-group">
            <label>订单号:</label>
            <input type="text" id="orderSn" placeholder="可选">
        </div>
        <div class="form-group">
            <label>用户关键词:</label>
            <input type="text" id="userKeyword" placeholder="昵称或手机号">
        </div>
        <div class="form-group">
            <label>开始时间:</label>
            <input type="date" id="startTime">
        </div>
        <div class="form-group">
            <label>结束时间:</label>
            <input type="date" id="endTime">
        </div>
        <button onclick="testOrderList()">测试订单列表接口</button>
        <button onclick="clearOrderListResult()">清空结果</button>
        <div id="orderListResult" class="result-area"></div>
    </div>
    
    <!-- 获取积分抵扣统计信息 -->
    <div class="test-section">
        <h3>2. 获取积分抵扣统计信息</h3>
        <div class="form-group">
            <label>开始时间:</label>
            <input type="date" id="statStartTime">
        </div>
        <div class="form-group">
            <label>结束时间:</label>
            <input type="date" id="statEndTime">
        </div>
        <button onclick="testStatistics()">测试统计接口</button>
        <button onclick="clearStatisticsResult()">清空结果</button>
        <div id="statisticsResult" class="result-area"></div>
    </div>
    
    <!-- 测试说明 -->
    <div class="test-section">
        <h3>测试说明</h3>
        <ul>
            <li>确保后端服务正在运行</li>
            <li>修改下面脚本中的baseURL为实际的后端地址</li>
            <li>如果需要认证，请先登录获取token</li>
            <li>所有时间参数都是可选的</li>
            <li>订单列表只返回使用了积分抵扣的订单（integral > 0 且 integralMoney > 0）</li>
        </ul>
    </div>

    <script>
        // 配置基础URL，请根据实际情况修改
        const baseURL = 'http://localhost:8080';
        
        // 通用请求函数
        async function apiRequest(url, params = {}) {
            try {
                const queryString = new URLSearchParams();
                Object.keys(params).forEach(key => {
                    if (params[key] !== '' && params[key] !== null && params[key] !== undefined) {
                        queryString.append(key, params[key]);
                    }
                });
                
                const fullURL = `${baseURL}${url}?${queryString.toString()}`;
                console.log('请求URL:', fullURL);
                
                const response = await fetch(fullURL, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        // 如果需要认证，在这里添加Authorization头
                        // 'Authorization': 'Bearer ' + token
                    }
                });
                
                const data = await response.json();
                return {
                    success: response.ok,
                    data: data,
                    status: response.status
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }
        
        // 测试订单列表接口
        async function testOrderList() {
            const params = {
                page: document.getElementById('page').value,
                limit: document.getElementById('limit').value,
                orderSn: document.getElementById('orderSn').value,
                userKeyword: document.getElementById('userKeyword').value,
                startTime: document.getElementById('startTime').value,
                endTime: document.getElementById('endTime').value
            };
            
            const resultDiv = document.getElementById('orderListResult');
            resultDiv.innerHTML = '正在请求...';
            
            const result = await apiRequest('/admin/points-orders/list', params);
            
            if (result.success) {
                resultDiv.innerHTML = `<div class="success">请求成功！</div>\n` + 
                                    JSON.stringify(result.data, null, 2);
            } else {
                resultDiv.innerHTML = `<div class="error">请求失败！</div>\n` + 
                                    `状态码: ${result.status}\n` +
                                    `错误信息: ${result.error || '未知错误'}\n` +
                                    JSON.stringify(result.data, null, 2);
            }
        }
        
        // 测试统计接口
        async function testStatistics() {
            const params = {
                startTime: document.getElementById('statStartTime').value,
                endTime: document.getElementById('statEndTime').value
            };
            
            const resultDiv = document.getElementById('statisticsResult');
            resultDiv.innerHTML = '正在请求...';
            
            const result = await apiRequest('/admin/points-orders/statistics', params);
            
            if (result.success) {
                resultDiv.innerHTML = `<div class="success">请求成功！</div>\n` + 
                                    JSON.stringify(result.data, null, 2);
            } else {
                resultDiv.innerHTML = `<div class="error">请求失败！</div>\n` + 
                                    `状态码: ${result.status}\n` +
                                    `错误信息: ${result.error || '未知错误'}\n` +
                                    JSON.stringify(result.data, null, 2);
            }
        }
        
        // 清空结果
        function clearOrderListResult() {
            document.getElementById('orderListResult').innerHTML = '';
        }
        
        function clearStatisticsResult() {
            document.getElementById('statisticsResult').innerHTML = '';
        }
        
        // 页面加载完成后设置默认时间
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date();
            const sevenDaysAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            
            // 设置默认时间范围为最近7天
            document.getElementById('startTime').value = sevenDaysAgo.toISOString().split('T')[0];
            document.getElementById('endTime').value = today.toISOString().split('T')[0];
            document.getElementById('statStartTime').value = sevenDaysAgo.toISOString().split('T')[0];
            document.getElementById('statEndTime').value = today.toISOString().split('T')[0];
        });
    </script>
</body>
</html>