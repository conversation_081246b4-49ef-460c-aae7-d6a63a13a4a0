<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品统计API测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .test-section {
            margin-bottom: 30px;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-section h2 {
            margin-top: 0;
            color: #1890ff;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 10px;
        }
        .test-button {
            background: linear-gradient(135deg, #1890ff, #096dd9);
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 8px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        .test-button:hover {
            background: linear-gradient(135deg, #096dd9, #0050b3);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        .test-button:active {
            transform: translateY(0);
        }
        .config-section {
            background: #fafafa;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .config-section label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
            color: #333;
        }
        .config-section input {
            width: 300px;
            padding: 8px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            margin: 5px 0;
        }
        .result {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin-top: 15px;
            border-radius: 6px;
            font-family: 'Consolas', 'Monaco', monospace;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-size: 12px;
            line-height: 1.4;
        }
        .result.error {
            background-color: #fff2f0;
            border-color: #ffccc7;
            color: #cf1322;
        }
        .result.success {
            background-color: #f6ffed;
            border-color: #b7eb8f;
            color: #389e0d;
        }
        .result.loading {
            background-color: #e6f7ff;
            border-color: #91d5ff;
            color: #0050b3;
        }
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #52c41a; }
        .status-error { background-color: #ff4d4f; }
        .status-loading { background-color: #1890ff; }
        .stats {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            padding: 15px;
            background: #f0f2f5;
            border-radius: 6px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #1890ff;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛍️ 商品统计API测试工具</h1>
        <p>用于测试wjsy_shop项目的商品统计接口是否正常调用真实业务数据</p>
    </div>

    <div class="test-section">
        <h2>⚙️ 配置信息</h2>
        <div class="config-section">
            <div>
                <label>API基础地址:</label>
                <input type="text" id="baseUrl" value="http://localhost:9999/weshop-wjhx/adminapi/statistic">
            </div>
            <div>
                <label>认证Token:</label>
                <input type="text" id="authToken" value="your-token-here" placeholder="请输入管理员Token">
            </div>
            <div>
                <label>测试时间范围:</label>
                <input type="text" id="dateRange" value="2025/01/01-2025/01/07" placeholder="yyyy/MM/dd-yyyy/MM/dd">
            </div>
        </div>
        <button class="test-button" onclick="updateConfig()">更新配置</button>
        <button class="test-button" onclick="testConnection()">测试连接</button>
    </div>

    <div class="test-section">
        <h2>📊 商品统计基础数据测试</h2>
        <button class="test-button" onclick="testProductBasic()">获取基础统计数据</button>
        <div id="basic-result" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>📈 商品统计趋势数据测试</h2>
        <button class="test-button" onclick="testProductTrend()">获取趋势统计数据</button>
        <div id="trend-result" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>🏆 商品排行数据测试</h2>
        <button class="test-button" onclick="testProductRanking()">获取商品排行数据</button>
        <div id="ranking-result" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>📤 导出功能测试</h2>
        <button class="test-button" onclick="testProductExport()">测试导出功能</button>
        <div id="export-result" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>🧪 综合测试</h2>
        <button class="test-button" onclick="testAllProductAPIs()">测试所有商品统计接口</button>
        <div id="all-result" class="result" style="display: none;"></div>
        <div class="stats">
            <div class="stat-item">
                <div class="stat-value" id="successCount">0</div>
                <div class="stat-label">成功</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="errorCount">0</div>
                <div class="stat-label">失败</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="totalCount">0</div>
                <div class="stat-label">总计</div>
            </div>
        </div>
    </div>

    <script>
        // 配置信息
        let config = {
            baseUrl: 'http://localhost:9999/weshop-wjhx/adminapi/statistic',
            token: 'your-token-here',
            dateRange: '2025/01/01-2025/01/07'
        };

        // 更新配置
        function updateConfig() {
            config.baseUrl = document.getElementById('baseUrl').value;
            config.token = document.getElementById('authToken').value;
            config.dateRange = document.getElementById('dateRange').value;
            console.log('配置已更新:', config);
            alert('配置已更新！');
        }

        // 通用请求函数
        async function makeRequest(url, resultElementId, description = '') {
            const resultElement = document.getElementById(resultElementId);
            resultElement.style.display = 'block';
            resultElement.className = 'result loading';
            resultElement.innerHTML = `<span class="status-indicator status-loading"></span>正在请求${description}...`;

            console.log(`发送请求: ${url}`);

            try {
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${config.token}`,
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });

                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    resultElement.className = 'result success';
                    resultElement.innerHTML = `<span class="status-indicator status-success"></span>✅ ${description}请求成功\n\n响应数据:\n${JSON.stringify(data, null, 2)}`;
                    return { success: true, data };
                } else {
                    resultElement.className = 'result error';
                    resultElement.innerHTML = `<span class="status-indicator status-error"></span>❌ ${description}请求失败\n状态码: ${response.status}\n错误信息: ${data.message || '未知错误'}\n\n完整响应:\n${JSON.stringify(data, null, 2)}`;
                    return { success: false, error: data };
                }
            } catch (error) {
                resultElement.className = 'result error';
                resultElement.innerHTML = `<span class="status-indicator status-error"></span>❌ ${description}网络错误\n错误信息: ${error.message}\n\n请检查:\n1. 后端服务是否启动\n2. API地址是否正确\n3. 网络连接是否正常`;
                return { success: false, error };
            }
        }

        // 测试连接
        async function testConnection() {
            await makeRequest(`${config.baseUrl}/product/get_basic?data=${config.dateRange}`, 'basic-result', '连接');
        }

        // 测试商品基础统计
        async function testProductBasic() {
            await makeRequest(`${config.baseUrl}/product/get_basic?data=${config.dateRange}`, 'basic-result', '商品基础统计');
        }

        // 测试商品趋势统计
        async function testProductTrend() {
            await makeRequest(`${config.baseUrl}/product/get_trend?data=${config.dateRange}`, 'trend-result', '商品趋势统计');
        }

        // 测试商品排行
        async function testProductRanking() {
            await makeRequest(`${config.baseUrl}/product/get_product_ranking?data=${config.dateRange}&page=1&limit=10`, 'ranking-result', '商品排行');
        }

        // 测试导出功能
        async function testProductExport() {
            await makeRequest(`${config.baseUrl}/product/get_excel?data=${config.dateRange}`, 'export-result', '导出功能');
        }

        // 测试所有接口
        async function testAllProductAPIs() {
            const resultElement = document.getElementById('all-result');
            resultElement.style.display = 'block';
            resultElement.className = 'result loading';
            resultElement.innerHTML = '开始批量测试商品统计接口...\n\n';

            const apis = [
                { name: '商品基础统计', url: `${config.baseUrl}/product/get_basic?data=${config.dateRange}` },
                { name: '商品趋势统计', url: `${config.baseUrl}/product/get_trend?data=${config.dateRange}` },
                { name: '商品排行统计', url: `${config.baseUrl}/product/get_product_ranking?data=${config.dateRange}&page=1&limit=10` },
                { name: '商品导出功能', url: `${config.baseUrl}/product/get_excel?data=${config.dateRange}` }
            ];

            let results = [];
            let successCount = 0;
            let errorCount = 0;

            for (const api of apis) {
                try {
                    console.log(`测试: ${api.name}`);
                    const response = await fetch(api.url, {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${config.token}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    const data = await response.json();
                    
                    if (response.ok && data.code === 200) {
                        results.push(`✅ ${api.name}: 成功 (数据条数: ${getDataCount(data)})`);
                        successCount++;
                    } else {
                        results.push(`❌ ${api.name}: 失败 (${response.status} - ${data.message || '未知错误'})`);
                        errorCount++;
                    }
                } catch (error) {
                    results.push(`❌ ${api.name}: 网络错误 (${error.message})`);
                    errorCount++;
                }

                // 更新进度显示
                resultElement.innerHTML = `测试进度: ${results.length}/${apis.length}\n\n${results.join('\n')}`;
                
                // 更新统计数字
                document.getElementById('successCount').textContent = successCount;
                document.getElementById('errorCount').textContent = errorCount;
                document.getElementById('totalCount').textContent = apis.length;
            }

            resultElement.className = successCount === apis.length ? 'result success' : 'result error';
            resultElement.innerHTML += `\n\n🎯 测试完成！\n成功: ${successCount}/${apis.length}\n失败: ${errorCount}/${apis.length}`;
            
            // 生成测试报告
            generateTestReport(results, successCount, errorCount);
        }

        // 获取数据条数
        function getDataCount(data) {
            if (data.data) {
                if (Array.isArray(data.data)) {
                    return data.data.length;
                } else if (typeof data.data === 'object') {
                    if (data.data.list && Array.isArray(data.data.list)) {
                        return data.data.list.length;
                    } else if (data.data.series && Array.isArray(data.data.series)) {
                        return data.data.series.length;
                    }
                    return Object.keys(data.data).length;
                }
            }
            return '未知';
        }

        // 生成测试报告
        function generateTestReport(results, successCount, errorCount) {
            const report = {
                timestamp: new Date().toISOString(),
                config: config,
                results: results,
                summary: {
                    total: results.length,
                    success: successCount,
                    error: errorCount,
                    successRate: ((successCount / results.length) * 100).toFixed(2) + '%'
                }
            };
            
            console.log('测试报告:', report);
            
            // 保存到本地存储
            localStorage.setItem('productStatisticTestReport', JSON.stringify(report));
        }

        // 页面加载时的初始化
        window.onload = function() {
            console.log('商品统计API测试工具已加载');
            console.log('请确保：');
            console.log('1. 后端服务已启动');
            console.log('2. 数据库连接正常');
            console.log('3. Token认证有效');
            
            // 检查本地存储的配置
            const savedConfig = localStorage.getItem('productStatisticConfig');
            if (savedConfig) {
                const parsed = JSON.parse(savedConfig);
                config = { ...config, ...parsed };
                document.getElementById('baseUrl').value = config.baseUrl;
                document.getElementById('authToken').value = config.token;
                document.getElementById('dateRange').value = config.dateRange;
            }
        };

        // 保存配置到本地存储
        window.addEventListener('beforeunload', function() {
            localStorage.setItem('productStatisticConfig', JSON.stringify(config));
        });
    </script>
</body>
</html>