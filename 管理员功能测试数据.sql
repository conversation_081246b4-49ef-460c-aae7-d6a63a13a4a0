-- 管理员功能测试数据初始化脚本

-- 1. 设置管理员用户（将第一个用户设为管理员）
UPDATE weshop_user SET user_level_id = 1 WHERE id = 1;

-- 2. 创建测试用户数据
INSERT INTO weshop_user (nickname, username, mobile, register_time, last_login_time, user_level_id, promoter_id, promotion_count) VALUES
('张三', 'zhangsan', '138****8888', '2025-01-20 10:30:00', '2025-01-26 09:15:00', 1, NULL, 3),
('李四', 'lisi', '139****9999', '2025-01-25 14:20:00', '2025-01-26 08:45:00', 0, 1, 0),
('王五', 'wangwu', '137****7777', '2025-01-15 16:45:00', '2025-01-25 20:30:00', 1, NULL, 8),
('赵六', 'zhaoliu', '136****6666', '2025-01-26 08:00:00', '2025-01-26 10:00:00', 0, 1, 0),
('钱七', 'qianqi', '135****5555', '2025-01-24 12:00:00', '2025-01-25 15:30:00', 0, 3, 1),
('孙八', 'sunba', '134****4444', '2025-01-22 09:30:00', '2025-01-24 18:20:00', 0, 3, 0),
('周九', 'zhoujiu', '133****3333', '2025-01-18 14:15:00', '2025-01-23 11:45:00', 0, 1, 2),
('吴十', 'wushi', '132****2222', '2025-01-16 11:20:00', '2025-01-22 16:10:00', 0, 3, 0)
ON DUPLICATE KEY UPDATE 
nickname = VALUES(nickname),
username = VALUES(username),
mobile = VALUES(mobile);

-- 3. 创建测试订单数据
INSERT INTO weshop_order (
    order_sn, user_id, order_status, pay_status, shipping_status,
    consignee, mobile, address, province, city, district,
    order_price, actual_price, goods_price, freight_price, coupon_price,
    pay_name, pay_id, create_time, pay_time, confirm_time
) VALUES
('WJ202501260001', 2, 1, 1, 0, '张三', '138****8888', '科技园南区', 440000, 440300, 440305, 299.00, 299.00, 299.00, 0.00, 0.00, '微信支付', 1, '2025-01-26 10:30:00', '2025-01-26 10:35:00', NULL),
('WJ202501260002', 3, 0, 0, 0, '李四', '139****9999', '天河区珠江新城', 440000, 440100, 440106, 158.00, 158.00, 158.00, 0.00, 0.00, NULL, NULL, '2025-01-26 09:15:00', NULL, NULL),
('WJ202501260003', 4, 2, 1, 1, '王五', '137****7777', '东城区中心广场', 441900, 441900, 441900, 568.00, 568.00, 568.00, 0.00, 0.00, '微信支付', 1, '2025-01-25 16:45:00', '2025-01-25 16:50:00', NULL),
('WJ202501260004', 5, 3, 1, 2, '赵六', '136****6666', '福田区华强北', 440000, 440300, 440304, 89.00, 89.00, 89.00, 0.00, 0.00, '微信支付', 1, '2025-01-25 14:20:00', '2025-01-25 14:25:00', '2025-01-26 10:00:00'),
('WJ202501260005', 6, 1, 1, 0, '钱七', '135****5555', '南山区后海', 440000, 440300, 440305, 199.00, 179.00, 199.00, 0.00, 20.00, '微信支付', 1, '2025-01-25 12:30:00', '2025-01-25 12:35:00', NULL),
('WJ202501260006', 7, 0, 0, 0, '孙八', '134****4444', '宝安区西乡', 440000, 440300, 440306, 328.00, 328.00, 328.00, 0.00, 0.00, NULL, NULL, '2025-01-24 18:20:00', NULL, NULL),
('WJ202501260007', 8, 2, 1, 1, '周九', '133****3333', '龙岗区坂田', 440000, 440300, 440307, 456.00, 456.00, 456.00, 0.00, 0.00, '微信支付', 1, '2025-01-24 11:45:00', '2025-01-24 11:50:00', NULL),
('WJ202501260008', 9, 1, 1, 0, '吴十', '132****2222', '罗湖区东门', 440000, 440300, 440303, 128.00, 128.00, 128.00, 0.00, 0.00, '微信支付', 1, '2025-01-23 16:10:00', '2025-01-23 16:15:00', NULL),
('WJ202501250001', 2, 3, 1, 2, '张三', '138****8888', '科技园南区', 440000, 440300, 440305, 399.00, 399.00, 399.00, 0.00, 0.00, '微信支付', 1, '2025-01-25 08:30:00', '2025-01-25 08:35:00', '2025-01-25 18:00:00'),
('WJ202501250002', 3, 2, 1, 1, '李四', '139****9999', '天河区珠江新城', 440000, 440100, 440106, 268.00, 268.00, 268.00, 0.00, 0.00, '微信支付', 1, '2025-01-25 07:15:00', '2025-01-25 07:20:00', NULL),
('WJ202501240001', 4, 3, 1, 2, '王五', '137****7777', '东城区中心广场', 441900, 441900, 441900, 688.00, 688.00, 688.00, 0.00, 0.00, '微信支付', 1, '2025-01-24 15:45:00', '2025-01-24 15:50:00', '2025-01-25 12:00:00'),
('WJ202501240002', 5, 1, 1, 0, '赵六', '136****6666', '福田区华强北', 440000, 440300, 440304, 189.00, 189.00, 189.00, 0.00, 0.00, '微信支付', 1, '2025-01-24 13:20:00', '2025-01-24 13:25:00', NULL),
('WJ202501230001', 6, 3, 1, 2, '钱七', '135****5555', '南山区后海', 440000, 440300, 440305, 299.00, 279.00, 299.00, 0.00, 20.00, '微信支付', 1, '2025-01-23 11:30:00', '2025-01-23 11:35:00', '2025-01-24 09:00:00'),
('WJ202501230002', 7, 2, 1, 1, '孙八', '134****4444', '宝安区西乡', 440000, 440300, 440306, 428.00, 428.00, 428.00, 0.00, 0.00, '微信支付', 1, '2025-01-23 09:20:00', '2025-01-23 09:25:00', NULL),
('WJ202501220001', 8, 3, 1, 2, '周九', '133****3333', '龙岗区坂田', 440000, 440300, 440307, 556.00, 556.00, 556.00, 0.00, 0.00, '微信支付', 1, '2025-01-22 14:45:00', '2025-01-22 14:50:00', '2025-01-23 16:30:00')
ON DUPLICATE KEY UPDATE 
order_sn = VALUES(order_sn),
user_id = VALUES(user_id),
order_status = VALUES(order_status);

-- 4. 创建测试订单商品数据
INSERT INTO weshop_order_goods (
    order_id, goods_id, goods_name, goods_sn, pic_url, price, number, specifications
) VALUES
(1, 1, '精选商品A', 'SP001', '/static/images/goods1.jpg', 299.00, 1, '规格：标准版'),
(2, 2, '精选商品B', 'SP002', '/static/images/goods2.jpg', 158.00, 1, '规格：经典款'),
(3, 3, '精选商品C', 'SP003', '/static/images/goods1.jpg', 568.00, 1, '规格：豪华版'),
(4, 4, '精选商品D', 'SP004', '/static/images/goods2.jpg', 89.00, 1, '规格：简约版'),
(5, 5, '精选商品E', 'SP005', '/static/images/goods1.jpg', 199.00, 1, '规格：时尚版'),
(6, 6, '精选商品F', 'SP006', '/static/images/goods2.jpg', 328.00, 1, '规格：商务版'),
(7, 7, '精选商品G', 'SP007', '/static/images/goods1.jpg', 456.00, 1, '规格：运动版'),
(8, 8, '精选商品H', 'SP008', '/static/images/goods2.jpg', 128.00, 1, '规格：休闲版'),
(9, 1, '精选商品A', 'SP001', '/static/images/goods1.jpg', 399.00, 1, '规格：升级版'),
(10, 2, '精选商品B', 'SP002', '/static/images/goods2.jpg', 268.00, 1, '规格：加强版'),
(11, 3, '精选商品C', 'SP003', '/static/images/goods1.jpg', 688.00, 1, '规格：旗舰版'),
(12, 4, '精选商品D', 'SP004', '/static/images/goods2.jpg', 189.00, 1, '规格：进阶版'),
(13, 5, '精选商品E', 'SP005', '/static/images/goods1.jpg', 299.00, 1, '规格：精装版'),
(14, 6, '精选商品F', 'SP006', '/static/images/goods2.jpg', 428.00, 1, '规格：专业版'),
(15, 7, '精选商品G', 'SP007', '/static/images/goods1.jpg', 556.00, 1, '规格：竞技版')
ON DUPLICATE KEY UPDATE 
goods_name = VALUES(goods_name),
price = VALUES(price);

-- 5. 更新用户推广关系
UPDATE weshop_user SET promoter_id = 1 WHERE id IN (2, 4, 7);
UPDATE weshop_user SET promoter_id = 3 WHERE id IN (5, 6, 8);

-- 6. 创建一些商品数据（如果不存在）
INSERT IGNORE INTO weshop_goods (
    id, name, goods_sn, primary_pic_url, retail_price, sell_volume, is_delete
) VALUES
(1, '精选商品A', 'SP001', '/static/images/goods1.jpg', 299.00, 156, 0),
(2, '精选商品B', 'SP002', '/static/images/goods2.jpg', 158.00, 89, 0),
(3, '精选商品C', 'SP003', '/static/images/goods1.jpg', 568.00, 234, 0),
(4, '精选商品D', 'SP004', '/static/images/goods2.jpg', 89.00, 67, 0),
(5, '精选商品E', 'SP005', '/static/images/goods1.jpg', 199.00, 123, 0),
(6, '精选商品F', 'SP006', '/static/images/goods2.jpg', 328.00, 178, 0),
(7, '精选商品G', 'SP007', '/static/images/goods1.jpg', 456.00, 201, 0),
(8, '精选商品H', 'SP008', '/static/images/goods2.jpg', 128.00, 95, 0);

-- 查询验证数据
SELECT '=== 用户统计 ===' as info;
SELECT 
    COUNT(*) as total_users,
    COUNT(CASE WHEN user_level_id = 1 THEN 1 END) as vip_users,
    COUNT(CASE WHEN register_time >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as new_users_7days,
    COUNT(CASE WHEN last_login_time >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as active_users_30days
FROM weshop_user;

SELECT '=== 订单统计 ===' as info;
SELECT 
    COUNT(*) as total_orders,
    COUNT(CASE WHEN order_status = 0 THEN 1 END) as pending_payment,
    COUNT(CASE WHEN order_status = 1 THEN 1 END) as pending_delivery,
    COUNT(CASE WHEN order_status = 2 THEN 1 END) as shipped,
    COUNT(CASE WHEN order_status = 3 THEN 1 END) as completed,
    COUNT(CASE WHEN create_time >= CURDATE() THEN 1 END) as today_orders,
    SUM(CASE WHEN pay_status = 1 THEN actual_price ELSE 0 END) as total_amount,
    SUM(CASE WHEN pay_status = 1 AND create_time >= CURDATE() THEN actual_price ELSE 0 END) as today_amount
FROM weshop_order;

SELECT '=== 管理员用户 ===' as info;
SELECT id, nickname, username, user_level_id FROM weshop_user WHERE user_level_id = 1;
