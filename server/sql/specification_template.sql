-- 创建规格模板表
CREATE TABLE IF NOT EXISTS `weshop_specification_template` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `rule_name` varchar(100) NOT NULL COMMENT '规格模板名称',
  `attr_name` varchar(100) DEFAULT NULL COMMENT '规格名称',
  `attr_value` text COMMENT '规格值，JSON格式存储',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：1启用，0禁用',
  PRIMARY KEY (`id`),
  KEY `idx_rule_name` (`rule_name`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='规格模板表';

-- 插入示例数据
INSERT INTO `weshop_specification_template` (`rule_name`, `attr_name`, `attr_value`, `status`) VALUES
('颜色尺寸模板', '颜色', '[{"value":"颜色","detail":[{"value":"红色","pic":""},{"value":"蓝色","pic":""},{"value":"绿色","pic":""}],"add_pic":0},{"value":"尺寸","detail":[{"value":"S","pic":""},{"value":"M","pic":""},{"value":"L","pic":""},{"value":"XL","pic":""}],"add_pic":0}]', 1),
('服装规格模板', '颜色', '[{"value":"颜色","detail":[{"value":"黑色","pic":""},{"value":"白色","pic":""},{"value":"灰色","pic":""}],"add_pic":0},{"value":"尺寸","detail":[{"value":"S","pic":""},{"value":"M","pic":""},{"value":"L","pic":""},{"value":"XL","pic":""},{"value":"XXL","pic":""}],"add_pic":0}]', 1),
('电子产品规格', '容量', '[{"value":"容量","detail":[{"value":"64GB","pic":""},{"value":"128GB","pic":""},{"value":"256GB","pic":""}],"add_pic":0},{"value":"颜色","detail":[{"value":"深空灰","pic":""},{"value":"银色","pic":""},{"value":"金色","pic":""}],"add_pic":0}]', 1);
