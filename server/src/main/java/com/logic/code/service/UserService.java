package com.logic.code.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.logic.code.common.utils.JwtHelper;
import com.logic.code.entity.User;
import com.logic.code.entity.WithdrawRecord;
import com.logic.code.entity.BankCardInfo;
import com.logic.code.entity.order.Order;
import com.logic.code.entity.PromotionEarnings;
import com.logic.code.mapper.CommonMapper;
import com.logic.code.mapper.UserMapper;
import com.logic.code.mapper.WithdrawRecordMapper;
import com.logic.code.mapper.BankCardInfoMapper;
import com.logic.code.mapper.OrderMapper;
import com.logic.code.model.query.AdminUserQuery;
import com.logic.code.model.vo.AdminUserListVO;
import com.logic.code.model.vo.UserInfoVO;
import com.logic.code.service.PromotionEarningsService;
import com.logic.code.service.PromotionLevelService;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;
import java.text.SimpleDateFormat;
import java.util.Calendar;

/**
 * <AUTHOR>
 * @date 2025/5/7 16:06
 * @desc
 */
@Service
@Slf4j
public class UserService extends BaseService<User> {

    @Resource
    UserMapper userMapper;
    @Resource
    OrderMapper orderMapper;
    @Resource
    CardService cardService;
    @Resource
    private CollectService collectService;
    @Resource
    private FootprintService footprintService;
    @Resource
    private WxQrCodeService wxQrCodeService;
    @Autowired
    private PromotionEarningsService promotionEarningsService;
    @Autowired
    private WithdrawRecordMapper withdrawRecordMapper;
    @Autowired
    private UserCouponService userCouponService;
    @Autowired
    private PromotionLevelService promotionLevelService;
    @Autowired
    private BankCardInfoMapper bankCardInfoMapper;
    @Autowired
    private BankCardInfoService bankCardInfoService;

    @Value("${app.base.url:https://www.sxwjsm.com}")
    private String baseUrl;

    @Override
    protected CommonMapper<User> getMapper() {
        return userMapper;
    }


    public UserInfoVO getUserInfo() {
        UserInfoVO userInfoVO = new UserInfoVO();

        // 设置统计数据
        userInfoVO.setCardCount(cardService.count());
        userInfoVO.setCollectionCount(collectService.getCollectNum());
        userInfoVO.setFootprintCount(footprintService.count());

        // 获取当前用户信息
        User currentUser = JwtHelper.getUserInfo();
        if (currentUser != null) {
            userInfoVO.setUserId(currentUser.getId());
            userInfoVO.setNickname(currentUser.getNickname());
            userInfoVO.setAvatar(currentUser.getAvatar());
            userInfoVO.setMobile(currentUser.getMobile());
            userInfoVO.setGender(currentUser.getGender());
            userInfoVO.setUsername(currentUser.getUsername());
            
            // 获取用户优惠券数量
            try {
                Map<String, Object> couponStats = userCouponService.getCouponStats(currentUser.getId());
                userInfoVO.setCouponCount(((Long) couponStats.get("availableCount")).intValue());
            } catch (Exception e) {
                userInfoVO.setCouponCount(0);
            }
            
            // 获取用户积分
            User fullUser = userMapper.selectById(currentUser.getId());
            userInfoVO.setUserPoints(fullUser.getPoints() != null ? fullUser.getPoints() : 0);
        }

        return userInfoVO;
    }


    public Boolean updateUser(User user) {
        return userMapper.updateById(user) == 1;
    }

    /**
     * 生成用户推广二维码
     * @param userId 用户ID
     * @return 二维码信息
     */
    public Map<String, String> generatePromotionQrCode(Integer userId) {
        try {
            // 生成唯一的推广场景值，使用用户ID作为推广标识
            String scene = "promo_" + userId;

            // 设置推广页面路径，指向登录页面，这样用户扫码后会直接进入登录流程
            String page = "pages/auth/login/login";

            // 生成小程序码
            byte[] qrCodeBytes = wxQrCodeService.createUnlimitedQrCode(scene, page, 430, false, null, false, "release");

            // 构建二维码访问URL（这里返回base64编码的图片数据）
            String qrCodeBase64 = java.util.Base64.getEncoder().encodeToString(qrCodeBytes);
            String qrCodeDataUrl = "data:image/png;base64," + qrCodeBase64;

            // 构建推广链接
            String promotionUrl = baseUrl + "/weshop-wjhx/pages/auth/login/login?scene=" + scene;

            Map<String, String> result = new HashMap<>();
            result.put("qrCodeUrl", qrCodeDataUrl);
            result.put("promotionUrl", promotionUrl);
            result.put("scene", scene);
            result.put("userId", userId.toString());

            return result;
        } catch (Exception e) {
            throw new RuntimeException("生成推广二维码失败: " + e.getMessage(), e);
        }
    }


    /**
     * 生成用户推广二维码
     * @param userId 用户ID
     * @return 二维码信息
     */
    public Map<String, String> generatePromotionQrCodeTM(Integer userId) {
        try {
            // 生成唯一的推广场景值，使用用户ID作为推广标识
            String scene = "promo_" + userId;

            // 设置推广页面路径，指向登录页面，这样用户扫码后会直接进入登录流程
            String page = "pages/auth/login/login";

            // 生成小程序码
            byte[] qrCodeBytes = wxQrCodeService.createUnlimitedQrCode(scene, page, 430, true, null, true, "release");

            // 构建二维码访问URL（这里返回base64编码的图片数据）
            String qrCodeBase64 = java.util.Base64.getEncoder().encodeToString(qrCodeBytes);
            String qrCodeDataUrl = "data:image/png;base64," + qrCodeBase64;

            // 构建推广链接
            String promotionUrl = baseUrl + "/weshop-wjhx/pages/auth/login/login?scene=" + scene;

            Map<String, String> result = new HashMap<>();
            result.put("qrCodeUrl", qrCodeDataUrl);
            result.put("promotionUrl", promotionUrl);
            result.put("scene", scene);
            result.put("userId", userId.toString());

            return result;
        } catch (Exception e) {
            throw new RuntimeException("生成推广二维码失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取用户推广统计信息（优化版本）
     * @param userId 用户ID
     * @return 推广统计信息
     */
    public Map<String, Object> getPromotionStats(Integer userId) {
        try {
            // 获取用户信息
            User user = userMapper.selectById(userId);
            if (user == null) {
                throw new RuntimeException("用户不存在");
            }

            // 查询推广用户列表
            List<User> promotedUsers = userMapper.selectList(
                new QueryWrapper<User>().eq("promoter_id", userId)
            );

            // 计算今日直邀用户数
            QueryWrapper<User> todayInviteWrapper = new QueryWrapper<>();
            todayInviteWrapper.eq("promoter_id", userId);
            todayInviteWrapper.ge("promotion_time", getTodayStart());
            Long todayInviteCount = userMapper.selectCount(todayInviteWrapper);

            // 计算本月直邀用户数
            QueryWrapper<User> monthInviteWrapper = new QueryWrapper<>();
            monthInviteWrapper.eq("promoter_id", userId);
            monthInviteWrapper.ge("promotion_time", getMonthStart());
            Long monthInviteCount = userMapper.selectCount(monthInviteWrapper);

            // 计算粉丝数（递归计算所有子孙节点）
            int fansCount = calculateFansCount(userId);

            // 统计信息
            Map<String, Object> stats = new HashMap<>();
            stats.put("promotionCode", user.getPromotionCode());
            stats.put("promotionLevel", user.getPromotionLevel());
            stats.put("promotionCount", promotedUsers.size());
            stats.put("todayInviteCount", todayInviteCount.intValue()); // 今日直邀用户数
            stats.put("monthInviteCount", monthInviteCount.intValue()); // 本月直邀用户数
            stats.put("fansCount", fansCount); // 粉丝数
            stats.put("firstPromotionTime", user.getFirstPromotionTime());

            // 如果没有推广用户，直接返回基础信息
            if (promotedUsers.isEmpty()) {
                stats.put("promotedUsers", new ArrayList<>());

                // 推广者信息（如果当前用户是被推广的）
                if (user.getPromoterId() != null) {
                    User promoter = userMapper.selectById(user.getPromoterId());
                    if (promoter != null) {
                        Map<String, Object> promoterInfo = new HashMap<>();
                        promoterInfo.put("id", promoter.getId());
                        promoterInfo.put("nickname", promoter.getNickname());
                        promoterInfo.put("avatar", promoter.getAvatar());
                        stats.put("promoter", promoterInfo);
                    }
                }

                return stats;
            }

            // 批量获取推广用户统计信息（优化核心）
            Map<Integer, Map<String, Object>> userStatsMap = getBatchUserStatistics(
                promotedUsers.stream().map(User::getId).collect(java.util.stream.Collectors.toList())
            );

            // 构建推广用户详情
            List<Map<String, Object>> promotedUserDetails = new ArrayList<>();
            for (User promotedUser : promotedUsers) {
                Map<String, Object> userDetail = new HashMap<>();
                userDetail.put("id", promotedUser.getId());
                userDetail.put("nickname", promotedUser.getNickname());
                userDetail.put("avatar", promotedUser.getAvatar());
                userDetail.put("promotionTime", promotedUser.getPromotionTime());
                userDetail.put("registerTime", promotedUser.getRegisterTime());
                userDetail.put("promoterId", promotedUser.getPromoterId());
                userDetail.put("promoterNickname", user.getNickname());

                // 添加统计信息
                Map<String, Object> userStats = userStatsMap.get(promotedUser.getId());
                if (userStats != null) {
                    userDetail.putAll(userStats);
                } else {
                    // 设置默认值
                    userDetail.put("totalOrderCount", 0);
                    userDetail.put("todayEstimatedIncome", "0.00");
                    userDetail.put("monthEstimatedIncome", "0.00");
                    userDetail.put("todayInviteCount", 0);
                    userDetail.put("monthInviteCount", 0);
                    userDetail.put("totalOrderAmount", "0.00");
                }

                promotedUserDetails.add(userDetail);
            }
            stats.put("promotedUsers", promotedUserDetails);

            // 推广者信息（如果当前用户是被推广的）
            if (user.getPromoterId() != null) {
                User promoter = userMapper.selectById(user.getPromoterId());
                if (promoter != null) {
                    Map<String, Object> promoterInfo = new HashMap<>();
                    promoterInfo.put("id", promoter.getId());
                    promoterInfo.put("nickname", promoter.getNickname());
                    promoterInfo.put("avatar", promoter.getAvatar());
                    stats.put("promoter", promoterInfo);
                }
            }

            return stats;
        } catch (Exception e) {
            throw new RuntimeException("获取推广统计失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取推广用户详情
     * @param currentUserId 当前用户ID
     * @param targetUserId 目标用户ID
     * @return 推广用户详情信息
     */
    /**
     * 获取区域总监统计信息
     * @return 区域总监统计信息
     */
    public Map<String, Object> getDirectorStatistics() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取区域总监数量（user_level_id = 2的用户）
            QueryWrapper<User> directorWrapper = new QueryWrapper<>();
            directorWrapper.eq("user_level_id", 2);
            Long totalDirectors = userMapper.selectCount(directorWrapper);
            result.put("totalDirectors", totalDirectors);
            
            // 获取今日新增区域总监数（user_level_id = 2 且今天注册的用户）
            LocalDateTime todayStart = LocalDate.now().atStartOfDay();
            QueryWrapper<User> newDirectorWrapper = new QueryWrapper<>();
            newDirectorWrapper.eq("user_level_id", 2);
            newDirectorWrapper.ge("register_time", todayStart);
            Long newDirectors = userMapper.selectCount(newDirectorWrapper);
            result.put("newDirectors", newDirectors);
            
            // 获取本月新增区域总监数（user_level_id = 2 且本月注册的用户）
            LocalDateTime monthStart = LocalDate.now().withDayOfMonth(1).atStartOfDay();
            QueryWrapper<User> monthDirectorWrapper = new QueryWrapper<>();
            monthDirectorWrapper.eq("user_level_id", 2);
            monthDirectorWrapper.ge("register_time", monthStart);
            Long monthDirectors = userMapper.selectCount(monthDirectorWrapper);
            result.put("monthDirectors", monthDirectors);
            
        } catch (Exception e) {
            throw new RuntimeException("获取区域总监统计信息失败: " + e.getMessage(), e);
        }
        
        return result;
    }

    public Map<String, Object> getPromotionUserDetail(Integer currentUserId, Integer targetUserId) {
        try {
            // 验证权限：只能查看自己推广的用户详情，或管理员可以查看所有用户
            User currentUser = userMapper.selectById(currentUserId);
            User targetUser = userMapper.selectById(targetUserId);
            
            if (targetUser == null) {
                throw new RuntimeException("用户不存在");
            }
            
            // 权限检查：管理员或者是目标用户的推广者
            boolean hasPermission = (currentUser.getUserLevelId() != null && ( currentUser.getUserLevelId() == 1 || currentUser.getUserLevelId() == 2 )) ||
                                  (targetUser.getPromoterId() != null && targetUser.getPromoterId().equals(currentUserId));
            
            if (!hasPermission) {
                throw new RuntimeException("无权限查看该用户详情");
            }

            Map<String, Object> result = new HashMap<>();
            
            // 用户基本信息
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", targetUser.getId());
            userInfo.put("nickname", targetUser.getNickname());
            userInfo.put("avatar", targetUser.getAvatar());
            userInfo.put("promotionTime", targetUser.getPromotionTime());
            userInfo.put("registerTime", targetUser.getRegisterTime());
            
            // 推广者信息
            if (targetUser.getPromoterId() != null) {
                User promoter = userMapper.selectById(targetUser.getPromoterId());
                if (promoter != null) {
                    userInfo.put("promoterId", promoter.getId());
                    userInfo.put("promoterNickname", promoter.getNickname());
                }
            }
            
            result.put("userInfo", userInfo);
            
            // 统计数据
            try {
                Map<String, Object> statistics = getPromotionUserStatistics(targetUserId);
                result.putAll(statistics);
            } catch (Exception e) {
                // 如果统计数据获取失败，设置默认值
                result.put("totalOrderCount", 0);
                result.put("totalOrderAmount", "0.00");
                result.put("monthInviteCount", 0);
                result.put("todayInviteCount", 0);
                result.put("monthEstimatedIncome", "0.00");
                result.put("todayEstimatedIncome", "0.00");
            }
            
            // 订单列表（最近20条）
            try {
                List<Map<String, Object>> ordersList = getPromotionUserOrders(targetUserId, 20);
                result.put("ordersList", ordersList);
            } catch (Exception e) {
                result.put("ordersList", new ArrayList<>());
            }
            
            // 邀请用户列表（最近10个）
            try {
                List<Map<String, Object>> invitesList = getPromotionUserInvites(targetUserId, 1000);
                result.put("invitesList", invitesList);
            } catch (Exception e) {
                result.put("invitesList", new ArrayList<>());
            }
            
            return result;
        } catch (Exception e) {
            throw new RuntimeException("获取推广用户详情失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取推广用户的统计数据
     */
    private Map<String, Object> getPromotionUserStatistics(Integer userId) {
        Map<String, Object> stats = new HashMap<>();
        
        // 订单统计
        QueryWrapper<Order> orderWrapper = new QueryWrapper<>();
        orderWrapper.eq("user_id", userId);
        Long totalOrderCount = orderMapper.selectCount(orderWrapper);
        stats.put("totalOrderCount", totalOrderCount.intValue());
        
        // 订单总金额
        orderWrapper.select("IFNULL(SUM(order_amount), 0) as total_amount");
        List<Map<String, Object>> amountResult = orderMapper.selectMaps(orderWrapper);
        String totalAmount = amountResult.isEmpty() ? "0.00" : amountResult.get(0).get("total_amount").toString();
        stats.put("totalOrderAmount", totalAmount);
        
        // 邀请用户统计
        QueryWrapper<User> inviteWrapper = new QueryWrapper<>();
        inviteWrapper.eq("promoter_id", userId);
        Long totalInviteCount = userMapper.selectCount(inviteWrapper);
        stats.put("totalInviteCount", totalInviteCount.intValue());
        
        // 本月邀请统计
        inviteWrapper.ge("promotion_time", getMonthStart());
        Long monthInviteCount = userMapper.selectCount(inviteWrapper);
        stats.put("monthInviteCount", monthInviteCount.intValue());
        
        // 今日邀请统计
        inviteWrapper.ge("promotion_time", getTodayStart());
        Long todayInviteCount = userMapper.selectCount(inviteWrapper);
        stats.put("todayInviteCount", todayInviteCount.intValue());
        
        // 收益统计
        try {
            BigDecimal monthEstimatedIncome = promotionEarningsService.getMonthEstimatedEarnings(userId);
            BigDecimal todayEstimatedIncome = promotionEarningsService.getTodayEstimatedEarnings(userId);
            stats.put("monthEstimatedIncome", monthEstimatedIncome.toString());
            stats.put("todayEstimatedIncome", todayEstimatedIncome.toString());
        } catch (Exception e) {
            stats.put("monthEstimatedIncome", "0.00");
            stats.put("todayEstimatedIncome", "0.00");
        }
        
        return stats;
    }
    
    /**
     * 获取推广用户的订单列表
     */
    private List<Map<String, Object>> getPromotionUserOrders(Integer userId, int limit) {
        QueryWrapper<Order> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId);
        wrapper.orderByDesc("create_time");
        wrapper.last("LIMIT " + limit);
        
        List<Order> orders = orderMapper.selectList(wrapper);
        List<Map<String, Object>> ordersList = new ArrayList<>();
        
        for (Order order : orders) {
            Map<String, Object> orderInfo = new HashMap<>();
            orderInfo.put("id", order.getId());
            orderInfo.put("orderNo", order.getOrderSn());
            orderInfo.put("orderAmount", order.getOrderPrice());
            orderInfo.put("status", order.getOrderStatus());
            orderInfo.put("createTime", order.getCreateTime());
            
            // 获取订单商品信息（简化版）
            // 这里可以根据需要添加订单商品详情
            orderInfo.put("orderGoods", new ArrayList<>());
            
            // 计算佣金（简化版本，使用10%佣金率）
            try {
                BigDecimal orderAmount = order.getOrderPrice();
                BigDecimal commissionRate = new BigDecimal("0.10"); // 10%佣金率
                BigDecimal commission = orderAmount.multiply(commissionRate);
                orderInfo.put("commissionAmount", commission.toString());
                orderInfo.put("commissionRate", "10");
            } catch (Exception e) {
                orderInfo.put("commissionAmount", "0.00");
                orderInfo.put("commissionRate", "10");
            }
            
            ordersList.add(orderInfo);
        }
        
        return ordersList;
    }
    
    /**
     * 获取推广用户的邀请用户列表
     */
    private List<Map<String, Object>> getPromotionUserInvites(Integer userId, int limit) {
        QueryWrapper<User> wrapper = new QueryWrapper<>();
        wrapper.eq("promoter_id", userId);
        wrapper.orderByDesc("promotion_time");
        wrapper.last("LIMIT " + limit);

        List<User> invites = userMapper.selectList(wrapper);
        List<Map<String, Object>> invitesList = new ArrayList<>();

        for (User invite : invites) {
            Map<String, Object> inviteInfo = new HashMap<>();
            inviteInfo.put("id", invite.getId());
            inviteInfo.put("nickname", invite.getNickname());
            inviteInfo.put("username", invite.getUsername());
            inviteInfo.put("avatar", invite.getAvatar());
            inviteInfo.put("mobile", invite.getMobile());
            inviteInfo.put("inviteTime", invite.getPromotionTime());
            inviteInfo.put("registerTime", invite.getRegisterTime());
            inviteInfo.put("lastLoginTime", invite.getLastLoginTime());
            inviteInfo.put("userLevelId", invite.getUserLevelId());
            inviteInfo.put("points", invite.getPoints() != null ? invite.getPoints() : 0);
            inviteInfo.put("balance", invite.getBalance() != null ? invite.getBalance().toString() : "0.00");

            // 获取该用户的订单数量和消费总额
            QueryWrapper<Order> orderWrapper = new QueryWrapper<>();
            orderWrapper.eq("user_id", invite.getId());
            List<Order> orders = orderMapper.selectList(orderWrapper);

            int orderCount = orders.size();
            BigDecimal totalAmount = orders.stream()
                .map(Order::getOrderAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            inviteInfo.put("orderCount", orderCount);
            inviteInfo.put("totalAmount", totalAmount.toString());

            // 获取推广统计信息
            QueryWrapper<User> promotionWrapper = new QueryWrapper<>();
            promotionWrapper.eq("promoter_id", invite.getId());
            Long promotionCount = userMapper.selectCount(promotionWrapper);
            inviteInfo.put("promotionCount", promotionCount.intValue());

            // 获取今日直邀数量
            QueryWrapper<User> todayInviteWrapper = new QueryWrapper<>();
            todayInviteWrapper.eq("promoter_id", invite.getId());
            todayInviteWrapper.ge("promotion_time", getTodayStart());
            Long todayInviteCount = userMapper.selectCount(todayInviteWrapper);
            inviteInfo.put("todayInviteCount", todayInviteCount.intValue());

            // 获取本月直邀数量
            QueryWrapper<User> monthInviteWrapper = new QueryWrapper<>();
            monthInviteWrapper.eq("promoter_id", invite.getId());
            monthInviteWrapper.ge("promotion_time", getMonthStart());
            Long monthInviteCount = userMapper.selectCount(monthInviteWrapper);
            inviteInfo.put("monthInviteCount", monthInviteCount.intValue());

            // 获取推广收益信息
            try {
                // 获取待确认收益
                QueryWrapper<PromotionEarnings> pendingWrapper = new QueryWrapper<>();
                pendingWrapper.eq("promoter_id", invite.getId());
                pendingWrapper.eq("status", "pending");
                List<PromotionEarnings> pendingEarningsList = promotionEarningsService.list(pendingWrapper);
                BigDecimal pendingEarnings = pendingEarningsList.stream()
                        .map(PromotionEarnings::getCommissionAmount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                inviteInfo.put("pendingEarnings", pendingEarnings.toString());

                // 获取可提现金额
                BigDecimal lastMonthEarnings = promotionEarningsService.getLastMonthEarnings(invite.getId());
                BigDecimal withdrawableAmount = lastMonthEarnings.add(pendingEarnings);
                inviteInfo.put("withdrawableAmount", withdrawableAmount.toString());
            } catch (Exception e) {
                inviteInfo.put("pendingEarnings", "0.00");
                inviteInfo.put("withdrawableAmount", "0.00");
            }

            invitesList.add(inviteInfo);
        }

        return invitesList;
    }
    
    /**
     * 获取今日开始时间
     */
    private Date getTodayStart() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }
    
    /**
     * 获取本月开始时间
     */
    private Date getMonthStart() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }
    
    /**
     * 递归计算用户的粉丝数（所有子孙节点的数量）
     * @param userId 用户ID
     * @return 粉丝数
     */
    private int calculateFansCount(Integer userId) {
        // 使用缓存避免重复计算
        Map<Integer, Integer> cache = new HashMap<>();
        return calculateFansCountWithCache(userId, cache);
    }
    
    /**
     * 递归计算用户的粉丝数（带缓存优化）
     * @param userId 用户ID
     * @param cache 缓存map
     * @return 粉丝数
     */
    private int calculateFansCountWithCache(Integer userId, Map<Integer, Integer> cache) {
        // 检查缓存中是否已有结果
        /*if (cache.containsKey(userId)) {
            return cache.get(userId);
        }*/
        
        // 查询直接邀请的用户
        QueryWrapper<User> wrapper = new QueryWrapper<>();
        wrapper.eq("promoter_id", userId);
        List<User> directInvites = userMapper.selectList(wrapper);
        
        // 如果没有直接邀请的用户，返回0
        if (directInvites.isEmpty()) {
            cache.put(userId, 0);
            return 0;
        }
        
        // 粉丝数 = 直接邀请的用户数 + 所有直接邀请用户的粉丝数
        int fansCount = directInvites.size();
        
        // 递归计算每个直接邀请用户的粉丝数
        for (User invite : directInvites) {
            fansCount += calculateFansCountWithCache(invite.getId(), cache);
        }
        
        // 缓存结果
        cache.put(userId, fansCount);
        return fansCount;
    }

    /**
     * 获取用户收益统计
     * @param userId 用户ID
     * @return 收益统计信息
     */
    public Map<String, Object> getEarningsStats(Integer userId) {
        try {
            return promotionEarningsService.getEarningsStats(userId);
        } catch (Exception e) {
            throw new RuntimeException("获取收益统计失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 根据筛选条件获取用户收益统计
     * @param params 筛选参数
     * @return 收益统计信息
     */
    public Map<String, Object> getEarningsStatsWithFilters(Map<String, Object> params) {
        try {
            Integer userId = (Integer) params.get("userId");
            String startDate = (String) params.get("startDate");
            String endDate = (String) params.get("endDate");
            String status = (String) params.get("status");
            
            return promotionEarningsService.getEarningsStatsWithFilters(userId, startDate, endDate, status);
        } catch (Exception e) {
            throw new RuntimeException("获取收益统计失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 性能测试接口 - 测试getPromotionStats优化效果
     * 仅用于开发测试，生产环境应移除
     */
    public Map<String, Object> performanceTest(Integer userId) {
        try {
            long startTime = System.currentTimeMillis();
            
            // 测试粉丝数计算
            int fansCount = calculateFansCount(userId);
            
            long endTime = System.currentTimeMillis();
            
            Map<String, Object> result = new HashMap<>();
            result.put("userId", userId);
            result.put("fansCount", fansCount);
            result.put("calculationTime", (endTime - startTime) + "ms");
            
            return result;
        } catch (Exception e) {
            throw new RuntimeException("性能测试失败: " + e.getMessage(), e);
        }
    }

    /**
     * 提交提现申请
     * @param userId 用户ID
     * @param amount 提现金额
     * @param paymentMethod 收款方式
     * @return 是否成功
     */
    public boolean submitWithdraw(Integer userId, String amount, String paymentMethod) {
        try {
            BigDecimal withdrawAmount = new BigDecimal(amount);

            // 创建提现记录
            WithdrawRecord record = new WithdrawRecord();
            record.setUserId(userId);
            record.setAmount(withdrawAmount);
            record.setFee(BigDecimal.ZERO); // 无手续费
            record.setActualAmount(withdrawAmount);
            record.setPaymentMethod(paymentMethod);
            record.setStatus("pending");
            record.setApplyTime(new Date());
            record.setCreateTime(new Date());

            int result = withdrawRecordMapper.insert(record);
            return result > 0;
        } catch (Exception e) {
            throw new RuntimeException("提现申请失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 提交提现申请（支持银行卡信息）
     * @param userId 用户ID
     * @param amount 提现金额
     * @param paymentMethod 收款方式
     * @param bankInfo 银行卡信息（可选）
     * @return 是否成功
     */
    @Transactional
    public boolean submitWithdrawWithBankInfo(Integer userId, String amount, String paymentMethod, Map<String, Object> bankInfo) {
        try {
            BigDecimal withdrawAmount = new BigDecimal(amount);
            Integer bankCardId = null;
            String bankCardSnapshot = null;

            // 如果是银行卡提现且提供了银行卡信息
            if ("bank".equals(paymentMethod) && bankInfo != null) {
                // 验证银行卡信息
                bankCardInfoService.validateBankCardInfo(bankInfo);
                
                // 处理银行卡信息
                BankCardInfo cardInfo = bankCardInfoService.saveBankCardInfo(userId, bankInfo);
                if (cardInfo != null) {
                    bankCardId = cardInfo.getId();
                    bankCardSnapshot = cardInfo.createSnapshot();
                }
            }

            // 创建提现记录
            WithdrawRecord record = new WithdrawRecord();
            record.setUserId(userId);
            record.setAmount(withdrawAmount);
            record.setFee(BigDecimal.ZERO); // 无手续费
            record.setActualAmount(withdrawAmount);
            record.setPaymentMethod(paymentMethod);
            record.setBankCardId(bankCardId);
            record.setBankCardSnapshot(bankCardSnapshot);
            record.setStatus("pending");
            record.setApplyTime(new Date());
            record.setCreateTime(new Date());

            int result = withdrawRecordMapper.insert(record);
            return result > 0;
        } catch (Exception e) {
            throw new RuntimeException("提现申请失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取每日收益统计
     * @param userId 用户ID
     * @return 每日收益统计
     */
    public Map<String, Object> getDailyEarningsStats(Integer userId) {
        try {
            return promotionEarningsService.getDailyEarningsStats(userId);
        } catch (Exception e) {
            throw new RuntimeException("获取每日收益统计失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取月度收益统计
     * @param userId 用户ID
     * @return 月度收益统计
     */
    public Map<String, Object> getMonthlyEarningsStats(Integer userId) {
        try {
            return promotionEarningsService.getMonthlyEarningsStats(userId);
        } catch (Exception e) {
            throw new RuntimeException("获取月度收益统计失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取用户提现记录
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页数量
     * @return 提现记录列表
     */
    public Map<String, Object> getUserWithdrawRecords(Integer userId, Integer page, Integer size) {
        try {
            // 计算分页参数
            int offset = (page - 1) * size;
            
            // 查询用户的提现记录
            QueryWrapper<WithdrawRecord> wrapper = new QueryWrapper<>();
            wrapper.eq("user_id", userId)
                   .orderByDesc("apply_time");
            
            // 获取总数
            long total = withdrawRecordMapper.selectCount(wrapper);
            
            // 分页查询
            wrapper.last("LIMIT " + offset + ", " + size);
            List<WithdrawRecord> records = withdrawRecordMapper.selectList(wrapper);
            
            // 转换数据格式
            List<Map<String, Object>> formattedRecords = records.stream().map(record -> {
                Map<String, Object> item = new HashMap<>();
                item.put("id", record.getId());
                item.put("amount", record.getAmount());
                item.put("actualAmount", record.getActualAmount());
                item.put("fee", record.getFee());
                item.put("paymentMethod", record.getPaymentMethod());
                item.put("status", record.getStatus());
                item.put("applyTime", record.getApplyTime());
                item.put("processTime", record.getProcessTime());
                item.put("completeTime", record.getCompleteTime());
                item.put("bankCardSnapshot", record.getBankCardSnapshot());
                return item;
            }).collect(Collectors.toList());
            
            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("records", formattedRecords);
            result.put("total", total);
            result.put("page", page);
            result.put("size", size);
            result.put("hasMore", offset + size < total);
            
            return result;
        } catch (Exception e) {
            throw new RuntimeException("获取提现记录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取每日订单明细
     * @param userId 用户ID
     * @param date 日期
     * @return 每日订单明细
     */
    public Map<String, Object> getDayOrderDetail(Integer userId, String date) {
        try {
            return promotionEarningsService.getDayOrderDetail(userId, date);
        } catch (Exception e) {
            throw new RuntimeException("获取每日订单明细失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取月度订单明细
     * @param userId 用户ID
     * @param month 月份
     * @return 月度订单明细
     */
    public Map<String, Object> getMonthOrderDetail(Integer userId, String month) {
        try {
            return promotionEarningsService.getMonthOrderDetail(userId, month);
        } catch (Exception e) {
            throw new RuntimeException("获取月度订单明细失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取用户统计信息
     * @param userId 用户ID
     * @return 用户统计信息
     */
    public Map<String, Object> getUserStatistics(Integer userId) {
        Map<String, Object> statistics = new HashMap<>();

        try {
            // 获取今日开始和结束时间
            LocalDate today = LocalDate.now();
            Date todayStart = Date.from(today.atStartOfDay(ZoneId.systemDefault()).toInstant());
            Date todayEnd = Date.from(today.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());

            // 获取本月开始和结束时间
            LocalDate monthStart = today.withDayOfMonth(1);
            Date monthStartDate = Date.from(monthStart.atStartOfDay(ZoneId.systemDefault()).toInstant());
            Date monthEndDate = Date.from(today.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());

            // 1. 获取用户总订单数
            QueryWrapper<Order> totalOrderWrapper = new QueryWrapper<>();
            totalOrderWrapper.eq("user_id", userId);
            totalOrderWrapper.ne("order_status", "CANCELLED"); // 排除已取消订单
            int totalOrderCount = Math.toIntExact(orderMapper.selectCount(totalOrderWrapper));
            statistics.put("totalOrderCount", totalOrderCount);

            // 2. 获取今日预估收入
            BigDecimal todayEstimatedIncome = promotionEarningsService.getTodayEstimatedEarnings(userId);
            statistics.put("todayEstimatedIncome", todayEstimatedIncome.toString());

            // 3. 获取本月预估收入
            BigDecimal monthEstimatedIncome = promotionEarningsService.getMonthEstimatedEarnings(userId);
            statistics.put("monthEstimatedIncome", monthEstimatedIncome.toString());

            // 4. 获取今日邀请人数
            QueryWrapper<User> todayInviteWrapper = new QueryWrapper<>();
            todayInviteWrapper.eq("promoter_id", userId);
            todayInviteWrapper.ge("create_time", todayStart);
            todayInviteWrapper.lt("create_time", todayEnd);
            int todayInviteCount = Math.toIntExact(userMapper.selectCount(todayInviteWrapper));
            statistics.put("todayInviteCount", todayInviteCount);

            // 5. 获取本月邀请人数
            QueryWrapper<User> monthInviteWrapper = new QueryWrapper<>();
            monthInviteWrapper.eq("promoter_id", userId);
            monthInviteWrapper.ge("create_time", monthStartDate);
            monthInviteWrapper.lt("create_time", monthEndDate);
            int monthInviteCount = Math.toIntExact(userMapper.selectCount(monthInviteWrapper));
            statistics.put("monthInviteCount", monthInviteCount);

            // 6. 获取涉及订单金额（用户所有订单的总金额）
            QueryWrapper<Order> orderAmountWrapper = new QueryWrapper<>();
            orderAmountWrapper.eq("user_id", userId);
            orderAmountWrapper.ne("order_status", "CANCELLED"); // 排除已取消订单
            orderAmountWrapper.select("IFNULL(SUM(actual_price), 0) as total_amount");
            List<Map<String, Object>> orderAmountResult = orderMapper.selectMaps(orderAmountWrapper);
            BigDecimal totalOrderAmount = BigDecimal.ZERO;
            if (!orderAmountResult.isEmpty() && orderAmountResult.get(0).get("total_amount") != null) {
                totalOrderAmount = new BigDecimal(orderAmountResult.get(0).get("total_amount").toString());
            }
            statistics.put("totalOrderAmount", totalOrderAmount.toString());

        } catch (Exception e) {
            // 如果出现异常，返回默认值
            statistics.put("totalOrderCount", 0);
            statistics.put("todayEstimatedIncome", "0.00");
            statistics.put("monthEstimatedIncome", "0.00");
            statistics.put("todayInviteCount", 0);
            statistics.put("monthInviteCount", 0);
            statistics.put("totalOrderAmount", "0.00");
        }

        return statistics;
    }

    /**
     * 批量获取用户统计信息（优化版本）
     * @param userIds 用户ID列表
     * @return 用户统计信息Map，key为用户ID，value为统计信息
     */
    private Map<Integer, Map<String, Object>> getBatchUserStatistics(List<Integer> userIds) {
        Map<Integer, Map<String, Object>> resultMap = new HashMap<>();

        if (userIds == null || userIds.isEmpty()) {
            return resultMap;
        }

        try {
            // 获取时间范围
            LocalDate today = LocalDate.now();
            Date todayStart = Date.from(today.atStartOfDay(ZoneId.systemDefault()).toInstant());
            Date todayEnd = Date.from(today.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());

            LocalDate monthStart = today.withDayOfMonth(1);
            Date monthStartDate = Date.from(monthStart.atStartOfDay(ZoneId.systemDefault()).toInstant());
            Date monthEndDate = Date.from(today.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());

            // 1. 批量查询用户总订单数和订单总金额
            Map<Integer, Map<String, Object>> orderStatsMap = getBatchOrderStatistics(userIds);

            // 2. 批量查询今日和本月预估收入
            Map<Integer, Map<String, Object>> earningsStatsMap = getBatchEarningsStatistics(userIds, todayStart, todayEnd, monthStartDate, monthEndDate);

            // 3. 批量查询今日和本月邀请人数
            Map<Integer, Map<String, Object>> inviteStatsMap = getBatchInviteStatistics(userIds, todayStart, todayEnd, monthStartDate, monthEndDate);

            // 合并所有统计数据
            for (Integer userId : userIds) {
                Map<String, Object> userStats = new HashMap<>();

                // 订单统计
                Map<String, Object> orderStats = orderStatsMap.get(userId);
                if (orderStats != null) {
                    userStats.putAll(orderStats);
                } else {
                    userStats.put("totalOrderCount", 0);
                    userStats.put("totalOrderAmount", "0.00");
                }

                // 收益统计
                Map<String, Object> earningsStats = earningsStatsMap.get(userId);
                if (earningsStats != null) {
                    userStats.putAll(earningsStats);
                } else {
                    userStats.put("todayEstimatedIncome", "0.00");
                    userStats.put("monthEstimatedIncome", "0.00");
                }

                // 邀请统计
                Map<String, Object> inviteStats = inviteStatsMap.get(userId);
                if (inviteStats != null) {
                    userStats.putAll(inviteStats);
                } else {
                    userStats.put("todayInviteCount", 0);
                    userStats.put("monthInviteCount", 0);
                }

                resultMap.put(userId, userStats);
            }

        } catch (Exception e) {
            // 如果批量查询失败，为所有用户设置默认值
            for (Integer userId : userIds) {
                Map<String, Object> defaultStats = new HashMap<>();
                defaultStats.put("totalOrderCount", 0);
                defaultStats.put("todayEstimatedIncome", "0.00");
                defaultStats.put("monthEstimatedIncome", "0.00");
                defaultStats.put("todayInviteCount", 0);
                defaultStats.put("monthInviteCount", 0);
                defaultStats.put("totalOrderAmount", "0.00");
                resultMap.put(userId, defaultStats);
            }
        }

        return resultMap;
    }

    /**
     * 批量查询订单统计信息
     * @param userIds 用户ID列表
     * @return 订单统计信息Map
     */
    private Map<Integer, Map<String, Object>> getBatchOrderStatistics(List<Integer> userIds) {
        Map<Integer, Map<String, Object>> resultMap = new HashMap<>();

        try {
            // 使用SQL聚合查询批量获取订单统计
            QueryWrapper<Order> wrapper = new QueryWrapper<>();
            wrapper.in("user_id", userIds)
                   .ne("order_status", "CANCELLED") // 排除已取消订单
                   .select("user_id",
                          "COUNT(*) as order_count",
                          "IFNULL(SUM(actual_price), 0) as total_amount")
                   .groupBy("user_id");

            List<Map<String, Object>> orderStatsList = orderMapper.selectMaps(wrapper);

            for (Map<String, Object> orderStats : orderStatsList) {
                Integer userId = (Integer) orderStats.get("user_id");
                Map<String, Object> stats = new HashMap<>();
                stats.put("totalOrderCount", ((Long) orderStats.get("order_count")).intValue());
                stats.put("totalOrderAmount", orderStats.get("total_amount").toString());
                resultMap.put(userId, stats);
            }

        } catch (Exception e) {
            // 查询失败时返回空Map，由上层方法处理默认值
        }

        return resultMap;
    }

    /**
     * 批量查询收益统计信息（优化版本）
     * @param userIds 用户ID列表
     * @param todayStart 今日开始时间
     * @param todayEnd 今日结束时间
     * @param monthStartDate 本月开始时间
     * @param monthEndDate 本月结束时间
     * @return 收益统计信息Map
     */
    private Map<Integer, Map<String, Object>> getBatchEarningsStatistics(List<Integer> userIds,
                                                                        Date todayStart, Date todayEnd,
                                                                        Date monthStartDate, Date monthEndDate) {
        Map<Integer, Map<String, Object>> resultMap = new HashMap<>();

        try {
            // 使用批量查询方法，大幅提升性能
            Map<Integer, BigDecimal> todayEarningsMap = promotionEarningsService.getBatchTodayEstimatedEarnings(userIds);
            Map<Integer, BigDecimal> monthEarningsMap = promotionEarningsService.getBatchMonthEstimatedEarnings(userIds);

            // 组装结果
            for (Integer userId : userIds) {
                Map<String, Object> stats = new HashMap<>();
                stats.put("todayEstimatedIncome", todayEarningsMap.getOrDefault(userId, BigDecimal.ZERO).toString());
                stats.put("monthEstimatedIncome", monthEarningsMap.getOrDefault(userId, BigDecimal.ZERO).toString());
                resultMap.put(userId, stats);
            }

        } catch (Exception e) {
            // 查询失败时返回空Map，由上层方法处理默认值
        }

        return resultMap;
    }

    /**
     * 批量查询邀请统计信息
     * @param userIds 用户ID列表
     * @param todayStart 今日开始时间
     * @param todayEnd 今日结束时间
     * @param monthStartDate 本月开始时间
     * @param monthEndDate 本月结束时间
     * @return 邀请统计信息Map
     */
    private Map<Integer, Map<String, Object>> getBatchInviteStatistics(List<Integer> userIds,
                                                                      Date todayStart, Date todayEnd,
                                                                      Date monthStartDate, Date monthEndDate) {
        Map<Integer, Map<String, Object>> resultMap = new HashMap<>();

        try {
            // 批量查询今日邀请人数
            QueryWrapper<User> todayWrapper = new QueryWrapper<>();
            todayWrapper.in("promoter_id", userIds)
                       .ge("register_time", todayStart)
                       .lt("register_time", todayEnd)
                       .select("promoter_id", "COUNT(*) as invite_count")
                       .groupBy("promoter_id");

            List<Map<String, Object>> todayInviteList = userMapper.selectMaps(todayWrapper);
            Map<Integer, Integer> todayInviteMap = new HashMap<>();
            for (Map<String, Object> item : todayInviteList) {
                Integer promoterId = (Integer) item.get("promoter_id");
                Integer count = ((Long) item.get("invite_count")).intValue();
                todayInviteMap.put(promoterId, count);
            }

            // 批量查询本月邀请人数
            QueryWrapper<User> monthWrapper = new QueryWrapper<>();
            monthWrapper.in("promoter_id", userIds)
                       .ge("register_time", monthStartDate)
                       .lt("register_time", monthEndDate)
                       .select("promoter_id", "COUNT(*) as invite_count")
                       .groupBy("promoter_id");

            List<Map<String, Object>> monthInviteList = userMapper.selectMaps(monthWrapper);
            Map<Integer, Integer> monthInviteMap = new HashMap<>();
            for (Map<String, Object> item : monthInviteList) {
                Integer promoterId = (Integer) item.get("promoter_id");
                Integer count = ((Long) item.get("invite_count")).intValue();
                monthInviteMap.put(promoterId, count);
            }

            // 组装结果
            for (Integer userId : userIds) {
                Map<String, Object> stats = new HashMap<>();
                stats.put("todayInviteCount", todayInviteMap.getOrDefault(userId, 0));
                stats.put("monthInviteCount", monthInviteMap.getOrDefault(userId, 0));
                resultMap.put(userId, stats);
            }

        } catch (Exception e) {
            // 查询失败时返回空Map，由上层方法处理默认值
        }

        return resultMap;
    }

    /**
     * 获取用户余额
     * @param userId 用户ID
     * @return 用户余额信息
     */
    public Map<String, Object> getUserBalance(Integer userId) {
        try {
            User user = userMapper.selectById(userId);
            if (user == null) {
                throw new RuntimeException("用户不存在");
            }

            Map<String, Object> result = new HashMap<>();
            BigDecimal balance = user.getBalance() != null ? user.getBalance() : BigDecimal.ZERO;
            result.put("balance", balance);
            result.put("balanceStr", balance.toString());
            
            return result;
        } catch (Exception e) {
            throw new RuntimeException("获取用户余额失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用用户余额
     * @param userId 用户ID
     * @param amount 使用金额
     * @param description 使用描述
     * @return 是否成功
     */
    public boolean useBalance(Integer userId, BigDecimal amount, String description) {
        try {
            if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
                return false;
            }
            
            User user = userMapper.selectById(userId);
            if (user == null) {
                return false;
            }
            
            BigDecimal currentBalance = user.getBalance() != null ? user.getBalance() : BigDecimal.ZERO;
            if (currentBalance.compareTo(amount) < 0) {
                return false; // 余额不足
            }
            
            // 扣减余额
            BigDecimal newBalance = currentBalance.subtract(amount);
            user.setBalance(newBalance);
            
            return userMapper.updateById(user) > 0;
        } catch (Exception e) {
            return false;
        }
    }


    /**
     * 获取管理员统计数据
     * @return 统计数据
     */
    public Map<String, Object> getAdminStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            // 获取总用户数
            Long totalUsers = userMapper.selectCount(new QueryWrapper<>());
            stats.put("totalUsers", totalUsers);
            
            // 获取今日新增用户数（最近24小时）
            LocalDateTime yesterday = LocalDateTime.now().minusDays(1);
            QueryWrapper<User> newUserWrapper = new QueryWrapper<>();
            newUserWrapper.ge("register_time", yesterday);
            Long newUsers = userMapper.selectCount(newUserWrapper);
            stats.put("newUsers", newUsers);
            
            // 获取活跃用户数（最近3天登录）
            LocalDateTime threeDaysAgo = LocalDateTime.now().minusDays(3);
            QueryWrapper<User> activeUserWrapper = new QueryWrapper<>();
            activeUserWrapper.ge("last_login_time", threeDaysAgo);
            Long activeUsers = userMapper.selectCount(activeUserWrapper);
            stats.put("activeUsers", activeUsers);
            
            // 获取VIP用户数（等级大于0）
            QueryWrapper<User> vipUserWrapper = new QueryWrapper<>();
            vipUserWrapper.gt("user_level_id", 0);
            Long vipUsers = userMapper.selectCount(vipUserWrapper);
            stats.put("vipUsers", vipUsers);
            
            // 获取推广用户数（有下线的用户）
            QueryWrapper<User> promoterWrapper = new QueryWrapper<>();
            promoterWrapper.apply("id IN (SELECT DISTINCT promoter_id FROM user WHERE promoter_id IS NOT NULL)");
            Long promoterUsers = userMapper.selectCount(promoterWrapper);
            stats.put("promoterUsers", promoterUsers);
            
            return stats;
        } catch (Exception e) {
            throw new RuntimeException("获取管理员统计数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取管理员用户列表
     * @param query 查询参数
     * @return 用户列表
     */
    public AdminUserListVO getAdminUserList(AdminUserQuery query) {
        try {
            // 构建查询条件
            QueryWrapper<User> wrapper = new QueryWrapper<>();
            
            // 根据类型筛选
            if (query.getType() != null && !query.getType().isEmpty()) {
                switch (query.getType()) {
                    case "new":
                        // 最近7天注册的用户
                        LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(7);
                        wrapper.ge("register_time", sevenDaysAgo);
                        break;
                    case "active":
                        // 最近3天登录的用户
                        LocalDateTime threeDaysAgo = LocalDateTime.now().minusDays(3);
                        wrapper.ge("last_login_time", threeDaysAgo);
                        break;
                    case "vip":
                        // VIP用户（等级大于0）
                        wrapper.gt("user_level_id", 0);
                        break;
                    case "promoter":
                        // 推广用户（有下线的用户）
                        wrapper.exists("SELECT 1 FROM weshop_user u2 WHERE u2.promoter_id = weshop_user.id");
                        break;
                }
            }
            
            // 根据关键词搜索
            if (query.getKeyword() != null && !query.getKeyword().isEmpty()) {
                wrapper.and(w -> w.like("nickname", query.getKeyword())
                                .or().like("mobile", query.getKeyword()));
            }
            
            // 根据用户等级筛选
            if (query.getUserLevelId() != null) {
                wrapper.eq("user_level_id", query.getUserLevelId());
            }
            
            // 排序
            wrapper.orderByDesc("register_time");
            
            // 分页查询
            int offset = (query.getPageNum() - 1) * query.getPageSize();
            wrapper.last("LIMIT " + offset + ", " + query.getPageSize());
            
            List<User> users = userMapper.selectList(wrapper);
            
            // 获取总数
            QueryWrapper<User> countWrapper = new QueryWrapper<>();
            if (query.getType() != null && !query.getType().isEmpty()) {
                switch (query.getType()) {
                    case "new":
                        LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(7);
                        countWrapper.ge("register_time", sevenDaysAgo);
                        break;
                    case "active":
                        LocalDateTime threeDaysAgo = LocalDateTime.now().minusDays(3);
                        countWrapper.ge("last_login_time", threeDaysAgo);
                        break;
                    case "vip":
                        countWrapper.gt("user_level_id", 0);
                        break;
                    case "promoter":
                        countWrapper.exists("SELECT 1 FROM weshop_user u2 WHERE u2.promoter_id = weshop_user.id");
                        break;
                }
            }
            if (query.getKeyword() != null && !query.getKeyword().isEmpty()) {
                countWrapper.and(w -> w.like("nickname", query.getKeyword())
                                     .or().like("mobile", query.getKeyword()));
            }
            
            // 根据用户等级筛选（计数查询）
            if (query.getUserLevelId() != null) {
                countWrapper.eq("user_level_id", query.getUserLevelId());
            }
            
            Long total = userMapper.selectCount(countWrapper);
            
            // 转换为VO
            List<com.logic.code.model.vo.AdminUserListVO.AdminUserVO> userVOs = new ArrayList<>();
            for (User user : users) {
                com.logic.code.model.vo.AdminUserListVO.AdminUserVO userVO = new com.logic.code.model.vo.AdminUserListVO.AdminUserVO();
                userVO.setId(user.getId().longValue());
                userVO.setNickname(user.getNickname());
                userVO.setUsername(user.getUsername());
                userVO.setMobile(user.getMobile());
                userVO.setAvatar(user.getAvatar());
                userVO.setUserLevelId(user.getUserLevelId());
                
                // 设置用户等级文本
                String levelText = "L0";
                if (user.getUserLevelId() != null) {
                    switch (user.getUserLevelId()) {
                        case 1: levelText = "L1"; break;
                        case 2: levelText = "VIP"; break;
                        case 3: levelText = "SVIP"; break;
                        default: levelText = "L" + user.getUserLevelId();
                    }
                }
                userVO.setUserLevelText(levelText);
                
                // 格式化时间
                if (user.getRegisterTime() != null) {
                    userVO.setRegisterTime(new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(user.getRegisterTime()));
                }
                if (user.getLastLoginTime() != null) {
                    userVO.setLastLoginTime(new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(user.getLastLoginTime()));
                }
                
                // 设置在线状态（简单判断：最近1小时登录算在线）
                boolean isOnline = false;
                if (user.getLastLoginTime() != null) {
                    long diffMinutes = (System.currentTimeMillis() - user.getLastLoginTime().getTime()) / (1000 * 60);
                    isOnline = diffMinutes <= 60;
                }
                userVO.setIsOnline(isOnline);
                
                // 获取用户订单统计
                QueryWrapper<Order> orderWrapper = new QueryWrapper<>();
                orderWrapper.eq("user_id", user.getId());
                orderWrapper.ne("order_status", "CANCELLED"); // 排除已取消订单
                Long orderCount = orderMapper.selectCount(orderWrapper);
                userVO.setOrderCount(orderCount.intValue());
                
                // 获取用户消费总额
                QueryWrapper<Order> amountWrapper = new QueryWrapper<>();
                amountWrapper.eq("user_id", user.getId());
                amountWrapper.ne("order_status", "CANCELLED"); // 排除已取消订单
                amountWrapper.select("IFNULL(SUM(actual_price), 0) as total_amount");
                List<Map<String, Object>> amountResult = orderMapper.selectMaps(amountWrapper);
                String totalAmount = "0.00";
                if (!amountResult.isEmpty() && amountResult.get(0).get("total_amount") != null) {
                    totalAmount = amountResult.get(0).get("total_amount").toString();
                }
                userVO.setTotalAmount(totalAmount);
                
                // 设置积分和余额
                userVO.setPoints(user.getPoints() != null ? user.getPoints() : 0);
                userVO.setBalance(user.getBalance() != null ? user.getBalance().toString() : "0.00");
                
                // 获取推广信息
                QueryWrapper<User> promotionWrapper = new QueryWrapper<>();
                promotionWrapper.eq("promoter_id", user.getId());
                Long promotionCount = userMapper.selectCount(promotionWrapper);
                userVO.setPromotionCount(promotionCount.intValue());

                // 获取今日直邀数量
                QueryWrapper<User> todayInviteWrapper = new QueryWrapper<>();
                todayInviteWrapper.eq("promoter_id", user.getId());
                todayInviteWrapper.ge("promotion_time", getTodayStart());
                Long todayInviteCount = userMapper.selectCount(todayInviteWrapper);
                userVO.setTodayInviteCount(todayInviteCount.intValue());

                // 获取本月直邀数量
                QueryWrapper<User> monthInviteWrapper = new QueryWrapper<>();
                monthInviteWrapper.eq("promoter_id", user.getId());
                monthInviteWrapper.ge("promotion_time", getMonthStart());
                Long monthInviteCount = userMapper.selectCount(monthInviteWrapper);
                userVO.setMonthInviteCount(monthInviteCount.intValue());

                // 获取推广者信息
                if (user.getPromoterId() != null) {
                    User promoter = userMapper.selectById(user.getPromoterId());
                    if (promoter != null) {
                        userVO.setPromoterId(promoter.getId().longValue());
                        userVO.setPromoterName(promoter.getNickname());
                    }
                }
                
                // 获取推广收益数据
                try {
                    // 获取总推广收益（已确认）
                    BigDecimal totalEarnings = promotionEarningsService.getTotalEarnings(user.getId());
                    userVO.setPromotionEarnings(totalEarnings != null ? totalEarnings.toString() : "0.00");
                    
                    // 获取待确认收益
                    QueryWrapper<PromotionEarnings> pendingWrapper = new QueryWrapper<>();
                    pendingWrapper.eq("promoter_id", user.getId());
                    pendingWrapper.eq("status", "pending");
                    List<PromotionEarnings> pendingEarningsList = promotionEarningsService.list(pendingWrapper);
                    BigDecimal pendingEarnings = pendingEarningsList.stream()
                            .map(PromotionEarnings::getCommissionAmount)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    userVO.setPendingEarnings(pendingEarnings.toString());
                    
                    // 获取可提现金额（上月已确认收益 + 待确认收益）
                    BigDecimal lastMonthEarnings = promotionEarningsService.getLastMonthEarnings(user.getId());
                    BigDecimal withdrawableAmount = lastMonthEarnings.add(pendingEarnings);
                    userVO.setWithdrawableAmount(withdrawableAmount.toString());
                } catch (Exception e) {
                    log.error("获取用户推广收益数据失败，用户ID: " + user.getId(), e);
                    userVO.setPromotionEarnings("0.00");
                    userVO.setPendingEarnings("0.00");
                    userVO.setWithdrawableAmount("0.00");
                }
                
                userVOs.add(userVO);
            }
            
            // 构建返回结果
            com.logic.code.model.vo.AdminUserListVO result = new com.logic.code.model.vo.AdminUserListVO();
            result.setList(userVOs);
            result.setPageNum(query.getPageNum());
            result.setPageSize(query.getPageSize());
            result.setTotal(total);
            result.setPages((int) Math.ceil((double) total / query.getPageSize()));
            
            return result;
        } catch (Exception e) {
            throw new RuntimeException("获取管理员用户列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 为已登录用户建立推广关系
     * @param userId 用户ID
     * @param promotionScene 推广场景值
     * @return 是否建立成功
     */
    public boolean establishPromotionRelation(Integer userId, String promotionScene) {
        try {
            // 获取用户信息
            User user = userMapper.selectById(userId);
            if (user == null) {
                System.err.println("用户不存在，ID: " + userId);
                return false;
            }

            // 检查用户是否已经有推广者
            if (user.getPromoterId() != null) {
                System.out.println("用户 " + user.getNickname() + " 已经有推广者，无法重复建立推广关系");
                return false;
            }

            // 解析推广场景值
            Integer promoterId = parsePromotionScene(promotionScene);
            if (promoterId == null) {
                System.err.println("推广场景值格式错误: " + promotionScene);
                return false;
            }

            // 检查是否是自己推广自己
            if (promoterId.equals(userId)) {
                System.out.println("用户不能推广自己，用户ID: " + userId);
                return false;
            }

            // 验证推广者是否存在
            User promoter = userMapper.selectById(promoterId);
            if (promoter == null) {
                System.err.println("推广者不存在，ID: " + promoterId);
                return false;
            }

            // 建立推广关系
            user.setPromoterId(promoterId);
            user.setPromotionTime(new Date());
            user.setPromotionLevel(0); // 普通用户

            // 更新用户信息
            int updateResult = userMapper.updateById(user);
            if (updateResult > 0) {
                // 更新推广者的推广统计
                updatePromoterStats(promoter);
                System.out.println("用户 " + user.getNickname() + " 通过用户 " + promoter.getNickname() + " 的推广建立了推广关系");
                return true;
            } else {
                System.err.println("更新用户推广关系失败，用户ID: " + userId);
                return false;
            }
        } catch (Exception e) {
            System.err.println("建立推广关系失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 解析推广场景值，提取推广者ID
     * @param promotionScene 推广场景值，格式：promo_用户ID 或 直接的用户ID
     * @return 推广者用户ID
     */
    private Integer parsePromotionScene(String promotionScene) {
        try {
            if (promotionScene.startsWith("promo_")) {
                return Integer.parseInt(promotionScene.substring(6));
            } else {
                // 兼容直接传用户ID的情况
                return Integer.parseInt(promotionScene);
            }
        } catch (NumberFormatException e) {
            System.err.println("推广场景值格式错误: " + promotionScene);
            return null;
        }
    }

    /**
     * 更新推广者的推广统计（使用推广等级系统）
     * @param promoter 推广者
     */
    private void updatePromoterStats(User promoter) {
        try {
            // 使用推广等级服务更新推广统计和等级
            promotionLevelService.updateUserPromotionLevel(promoter.getId());
            log.info("更新推广者{}的推广统计和等级", promoter.getId());
        } catch (Exception e) {
            log.error("更新推广者统计失败: 推广者ID={}", promoter.getId(), e);
        }
    }
    
    /**
     * 建立推广关系（新版本，使用推广等级系统）
     * @param userId 用户ID
     * @param promotionScene 推广场景值
     * @return 是否成功
     */
    @Transactional
    public boolean establishPromotionRelationWithLevel(Integer userId, String promotionScene) {
        try {
            log.info("开始建立推广关系: 用户={}, 场景值={}", userId, promotionScene);
            
            // 解析推广场景值，提取推广者ID
            Integer promoterId = parsePromoterIdFromScene(promotionScene);
            if (promoterId == null) {
                log.warn("无效的推广场景值: {}", promotionScene);
                return false;
            }
            
            // 检查推广者是否存在
            User promoter = userMapper.selectById(promoterId);
            if (promoter == null) {
                log.warn("推广者不存在: {}", promoterId);
                return false;
            }
            
            // 检查用户是否存在
            User user = userMapper.selectById(userId);
            if (user == null) {
                log.warn("用户不存在: {}", userId);
                return false;
            }
            
            // 检查是否自己推广自己
            if (promoterId.equals(userId)) {
                log.warn("用户不能推广自己: {}", userId);
                return false;
            }
            
            // 检查用户是否已经有推广者
            if (user.getPromoterId() != null) {
                log.info("用户{}已有推广者{}，跳过建立推广关系", userId, user.getPromoterId());
                return true;
            }
            
            // 建立推广关系
            User updateUser = new User();
            updateUser.setId(userId);
            updateUser.setPromoterId(promoterId);
            updateUser.setPromotionTime(new Date());
            
            int result = userMapper.updateById(updateUser);
            
            if (result > 0) {
                // 处理推广奖励
                promotionLevelService.handlePromotionReward(promoterId, userId);
                
                log.info("推广关系建立成功: 推广者={}, 被推广者={}", promoterId, userId);
                return true;
            } else {
                log.error("推广关系建立失败: 数据库更新失败");
                return false;
            }
            
        } catch (Exception e) {
            log.error("建立推广关系失败: 用户={}, 场景值={}", userId, promotionScene, e);
            return false;
        }
    }
    
    /**
     * 从推广场景值中解析推广者ID
     * @param promotionScene 推广场景值，支持多种格式：
     *                      - "promo_123" (二维码推广)
     *                      - "goods_share_123" (商品分享推广)
     *                      - "123" (直接数字)
     * @return 推广者ID，解析失败返回null
     */
    private Integer parsePromoterIdFromScene(String promotionScene) {
        try {
            if (promotionScene == null || promotionScene.trim().isEmpty()) {
                return null;
            }
            
            String scene = promotionScene.trim();
            
            // 处理 "promo_123" 格式（二维码推广）
            if (scene.startsWith("promo_")) {
                String idStr = scene.substring(6); // 去掉 "promo_" 前缀
                return Integer.parseInt(idStr);
            }
            
            // 处理 "goods_share_123" 格式（商品分享推广）
            if (scene.startsWith("goods_share_")) {
                String idStr = scene.substring(12); // 去掉 "goods_share_" 前缀
                return Integer.parseInt(idStr);
            }
            
            // 尝试直接解析为数字
            return Integer.parseInt(scene);
            
        } catch (NumberFormatException e) {
            log.warn("无法解析推广场景值: {}", promotionScene);
            return null;
        }
    }
    
    /**
     * 获取用户推广等级信息
     * @param userId 用户ID
     * @return 推广等级信息
     */
    public Map<String, Object> getUserPromotionLevelInfo(Integer userId) {
        return promotionLevelService.getUserPromotionLevelInfo(userId);
    }
    
    /**
     * 获取推广积分记录
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 推广积分记录
     */
    public Map<String, Object> getPromotionPointsRecords(Integer userId, Integer page, Integer size) {
        return promotionLevelService.getPromotionPointsRecords(userId, page, size);
    }

    /**
     * 搜索推广用户（支持用户名和手机号模糊搜索）
     * @param currentUserId 当前用户ID
     * @param searchKeyword 搜索关键词
     * @param page 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    public Map<String, Object> searchPromotionUsers(Integer currentUserId, String searchKeyword, Integer page, Integer size) {
        try {
            // 验证权限：只能搜索自己推广的用户
            User currentUser = userMapper.selectById(currentUserId);
            if (currentUser == null) {
                throw new RuntimeException("当前用户不存在");
            }
            
            // 构建查询条件
            QueryWrapper<User> wrapper = new QueryWrapper<>();
            wrapper.eq("promoter_id", currentUserId); // 只查询当前用户推广的用户
            
            // 添加模糊搜索条件（用户名或手机号）
            if (searchKeyword != null && !searchKeyword.trim().isEmpty()) {
                String keyword = searchKeyword.trim();
                wrapper.and(w -> w.like("nickname", keyword)
                                .or().like("mobile", keyword));
            }
            
            // 设置排序
            wrapper.orderByDesc("promotion_time");
            
            // 计算分页
            int offset = (page - 1) * size;
            wrapper.last("LIMIT " + offset + ", " + size);
            
            // 执行查询
            List<User> users = userMapper.selectList(wrapper);
            
            // 获取总数
            QueryWrapper<User> countWrapper = new QueryWrapper<>();
            countWrapper.eq("promoter_id", currentUserId);
            if (searchKeyword != null && !searchKeyword.trim().isEmpty()) {
                String keyword = searchKeyword.trim();
                countWrapper.and(w -> w.like("nickname", keyword)
                                     .or().like("mobile", keyword));
            }
            Long total = userMapper.selectCount(countWrapper);
            
            // 批量获取用户统计信息
            List<Integer> userIds = users.stream().map(User::getId).collect(Collectors.toList());
            Map<Integer, Map<String, Object>> userStatsMap = getBatchUserStatistics(userIds);
            
            // 构建返回结果
            List<Map<String, Object>> userList = new ArrayList<>();
            for (User user : users) {
                Map<String, Object> userInfo = new HashMap<>();
                userInfo.put("id", user.getId());
                userInfo.put("nickname", user.getNickname());
                userInfo.put("username", user.getUsername());
                userInfo.put("avatar", user.getAvatar());
                userInfo.put("mobile", user.getMobile());
                userInfo.put("userLevelId", user.getUserLevelId());
                userInfo.put("points", user.getPoints() != null ? user.getPoints() : 0);
                userInfo.put("balance", user.getBalance() != null ? user.getBalance().toString() : "0.00");
                userInfo.put("inviteTime", user.getPromotionTime()); // 兼容前端字段名
                userInfo.put("promotionTime", user.getPromotionTime());
                userInfo.put("registerTime", user.getRegisterTime());
                userInfo.put("lastLoginTime", user.getLastLoginTime());

                // 获取订单统计
                QueryWrapper<Order> orderWrapper = new QueryWrapper<>();
                orderWrapper.eq("user_id", user.getId());
                List<Order> orders = orderMapper.selectList(orderWrapper);

                int orderCount = orders.size();
                BigDecimal totalAmount = orders.stream()
                    .map(Order::getOrderAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

                userInfo.put("orderCount", orderCount);
                userInfo.put("totalAmount", totalAmount.toString());

                // 获取推广统计
                QueryWrapper<User> promotionWrapper = new QueryWrapper<>();
                promotionWrapper.eq("promoter_id", user.getId());
                Long promotionCount = userMapper.selectCount(promotionWrapper);
                userInfo.put("promotionCount", promotionCount.intValue());

                // 获取今日直邀数量
                QueryWrapper<User> todayInviteWrapper = new QueryWrapper<>();
                todayInviteWrapper.eq("promoter_id", user.getId());
                todayInviteWrapper.ge("promotion_time", getTodayStart());
                Long todayInviteCount = userMapper.selectCount(todayInviteWrapper);
                userInfo.put("todayInviteCount", todayInviteCount.intValue());

                // 获取本月直邀数量
                QueryWrapper<User> monthInviteWrapper = new QueryWrapper<>();
                monthInviteWrapper.eq("promoter_id", user.getId());
                monthInviteWrapper.ge("promotion_time", getMonthStart());
                Long monthInviteCount = userMapper.selectCount(monthInviteWrapper);
                userInfo.put("monthInviteCount", monthInviteCount.intValue());

                // 获取推广收益信息
                try {
                    // 获取待确认收益
                    QueryWrapper<PromotionEarnings> pendingWrapper = new QueryWrapper<>();
                    pendingWrapper.eq("promoter_id", user.getId());
                    pendingWrapper.eq("status", "pending");
                    List<PromotionEarnings> pendingEarningsList = promotionEarningsService.list(pendingWrapper);
                    BigDecimal pendingEarnings = pendingEarningsList.stream()
                            .map(PromotionEarnings::getCommissionAmount)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    userInfo.put("pendingEarnings", pendingEarnings.toString());

                    // 获取可提现金额
                    BigDecimal lastMonthEarnings = promotionEarningsService.getLastMonthEarnings(user.getId());
                    BigDecimal withdrawableAmount = lastMonthEarnings.add(pendingEarnings);
                    userInfo.put("withdrawableAmount", withdrawableAmount.toString());
                } catch (Exception e) {
                    userInfo.put("pendingEarnings", "0.00");
                    userInfo.put("withdrawableAmount", "0.00");
                }

                // 添加统计信息（如果有的话）
                Map<String, Object> stats = userStatsMap.get(user.getId());
                if (stats != null) {
                    userInfo.putAll(stats);
                } else {
                    // 设置默认统计信息
                    userInfo.put("totalOrderCount", orderCount);
                    userInfo.put("totalOrderAmount", totalAmount.toString());
                    userInfo.put("todayEstimatedIncome", "0.00");
                    userInfo.put("monthEstimatedIncome", "0.00");
                }

                userList.add(userInfo);
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("list", userList);
            result.put("total", total);
            result.put("page", page);
            result.put("size", size);
            result.put("pages", (int) Math.ceil((double) total / size));
            result.put("searchKeyword", searchKeyword);
            
            return result;
        } catch (Exception e) {
            throw new RuntimeException("搜索推广用户失败: " + e.getMessage(), e);
        }
    }
}
