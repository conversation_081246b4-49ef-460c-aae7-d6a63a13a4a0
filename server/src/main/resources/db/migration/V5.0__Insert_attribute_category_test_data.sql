-- 插入参数分类测试数据
-- 确保表存在
CREATE TABLE IF NOT EXISTS `weshop_attribute_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用，1-启用，0-禁用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='属性分类表';

-- 插入测试分类数据
INSERT INTO `weshop_attribute_category` (`name`, `enabled`) VALUES
('手机参数', 1),
('电脑参数', 1),
('服装参数', 1),
('家电参数', 1),
('数码配件', 1)
ON DUPLICATE KEY UPDATE `enabled` = VALUES(`enabled`);

-- 确保属性表存在
CREATE TABLE IF NOT EXISTS `weshop_attribute` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '属性ID',
  `attribute_category_id` int(11) NOT NULL COMMENT '属性分类ID',
  `name` varchar(100) NOT NULL COMMENT '属性名称',
  `input_type` tinyint(1) DEFAULT '0' COMMENT '输入类型，0-输入框，1-下拉选择',
  `sort_order` tinyint(4) DEFAULT '0' COMMENT '排序',
  `values` text COMMENT '属性值，JSON格式',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`attribute_category_id`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='属性表';

-- 插入测试属性数据
INSERT INTO `weshop_attribute` (`attribute_category_id`, `name`, `input_type`, `sort_order`, `values`) VALUES
-- 手机参数 (分类ID=1)
(1, '品牌', 1, 1, '["苹果", "华为", "小米", "OPPO", "vivo"]'),
(1, '颜色', 1, 2, '["黑色", "白色", "金色", "蓝色", "红色"]'),
(1, '存储容量', 1, 3, '["64GB", "128GB", "256GB", "512GB", "1TB"]'),
(1, '运行内存', 1, 4, '["4GB", "6GB", "8GB", "12GB", "16GB"]'),
(1, '屏幕尺寸', 0, 5, '["6.1英寸", "6.4英寸", "6.7英寸"]'),

-- 电脑参数 (分类ID=2)
(2, '品牌', 1, 1, '["联想", "戴尔", "华硕", "惠普", "苹果"]'),
(2, '处理器', 1, 2, '["Intel i5", "Intel i7", "Intel i9", "AMD Ryzen 5", "AMD Ryzen 7"]'),
(2, '内存', 1, 3, '["8GB", "16GB", "32GB", "64GB"]'),
(2, '硬盘', 1, 4, '["256GB SSD", "512GB SSD", "1TB SSD", "1TB HDD"]'),
(2, '显卡', 1, 5, '["集成显卡", "GTX 1650", "RTX 3060", "RTX 4070"]'),

-- 服装参数 (分类ID=3)
(3, '尺码', 1, 1, '["XS", "S", "M", "L", "XL", "XXL"]'),
(3, '颜色', 1, 2, '["黑色", "白色", "灰色", "蓝色", "红色"]'),
(3, '材质', 1, 3, '["棉", "涤纶", "丝绸", "羊毛", "混纺"]'),
(3, '季节', 1, 4, '["春季", "夏季", "秋季", "冬季"]'),
(3, '风格', 1, 5, '["休闲", "商务", "运动", "时尚"]'),

-- 家电参数 (分类ID=4)
(4, '品牌', 1, 1, '["海尔", "美的", "格力", "小米", "TCL"]'),
(4, '功率', 0, 2, '["1000W", "1500W", "2000W", "2500W"]'),
(4, '容量', 1, 3, '["小容量", "中容量", "大容量"]'),
(4, '能效等级', 1, 4, '["一级能效", "二级能效", "三级能效"]'),
(4, '颜色', 1, 5, '["白色", "黑色", "银色", "金色"]'),

-- 数码配件 (分类ID=5)
(5, '品牌', 1, 1, '["苹果", "华为", "小米", "Anker", "绿联"]'),
(5, '接口类型', 1, 2, '["USB-A", "USB-C", "Lightning", "Micro USB"]'),
(5, '材质', 1, 3, '["塑料", "金属", "尼龙", "硅胶"]'),
(5, '长度', 1, 4, '["1米", "1.5米", "2米", "3米"]'),
(5, '颜色', 1, 5, '["黑色", "白色", "红色", "蓝色"]')
ON DUPLICATE KEY UPDATE 
  `name` = VALUES(`name`),
  `input_type` = VALUES(`input_type`),
  `sort_order` = VALUES(`sort_order`),
  `values` = VALUES(`values`);

-- 显示插入结果
SELECT 
    '参数分类数据插入完成' as message,
    COUNT(*) as category_count
FROM weshop_attribute_category 
WHERE enabled = 1;

SELECT 
    '属性数据插入完成' as message,
    COUNT(*) as attribute_count
FROM weshop_attribute;

-- 显示分类和对应的属性数量
SELECT 
    ac.id,
    ac.name as category_name,
    ac.enabled,
    COUNT(a.id) as attribute_count
FROM weshop_attribute_category ac
LEFT JOIN weshop_attribute a ON ac.id = a.attribute_category_id
GROUP BY ac.id, ac.name, ac.enabled
ORDER BY ac.id;
