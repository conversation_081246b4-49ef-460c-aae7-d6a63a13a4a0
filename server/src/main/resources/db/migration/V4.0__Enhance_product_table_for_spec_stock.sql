-- 完善产品表以支持完整的规格库存数据
-- 版本: V4.0
-- 说明: 为weshop_product表添加规格库存组件需要的所有字段

-- 为产品表添加缺失的规格库存字段
ALTER TABLE `weshop_product` 
ADD COLUMN `cost_price` DECIMAL(10,2) DEFAULT 0.00 COMMENT '成本价',
ADD COLUMN `ot_price` DECIMAL(10,2) DEFAULT 0.00 COMMENT '划线价/原价',
ADD COLUMN `bar_code` VARCHAR(100) DEFAULT '' COMMENT '商品编码',
ADD COLUMN `bar_code_number` VARCHAR(100) DEFAULT '' COMMENT '条形码',
ADD COLUMN `goods_weight` DECIMAL(8,2) DEFAULT 0.00 COMMENT '商品重量(KG)',
ADD COLUMN `goods_volume` DECIMAL(8,2) DEFAULT 0.00 COMMENT '商品体积(m³)',
ADD COLUMN `pic_url` VARCHAR(500) DEFAULT '' COMMENT '规格图片URL',
ADD COLUMN `brokerage` DECIMAL(8,2) DEFAULT 0.00 COMMENT '一级分销佣金',
ADD COLUMN `brokerage_two` DECIMAL(8,2) DEFAULT 0.00 COMMENT '二级分销佣金',
ADD COLUMN `quota` INT DEFAULT 0 COMMENT '限购数量，0为不限购',
ADD COLUMN `quota_show` TINYINT(1) DEFAULT 0 COMMENT '是否显示限购数量：1显示，0不显示',
ADD COLUMN `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
ADD COLUMN `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';

-- 添加索引以提高查询性能
CREATE INDEX `idx_product_goods_id` ON `weshop_product` (`goods_id`);
CREATE INDEX `idx_product_bar_code` ON `weshop_product` (`bar_code`);
CREATE INDEX `idx_product_bar_code_number` ON `weshop_product` (`bar_code_number`);
CREATE INDEX `idx_product_retail_price` ON `weshop_product` (`retail_price`);
CREATE INDEX `idx_product_cost_price` ON `weshop_product` (`cost_price`);
CREATE INDEX `idx_product_goods_number` ON `weshop_product` (`goods_number`);

-- 更新表注释
ALTER TABLE `weshop_product` COMMENT = '商品产品表 - 存储商品的具体规格和库存信息';

-- 确保商品规格表存在并具有正确的结构
CREATE TABLE IF NOT EXISTS `weshop_goods_specification` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `goods_id` int(11) NOT NULL COMMENT '商品ID',
  `specification_id` int(11) NOT NULL COMMENT '规格ID',
  `value` varchar(255) NOT NULL COMMENT '规格值',
  `pic_url` varchar(500) DEFAULT '' COMMENT '规格图片URL',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_specification_id` (`specification_id`),
  KEY `idx_goods_spec` (`goods_id`, `specification_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品规格表';

-- 确保规格表存在
CREATE TABLE IF NOT EXISTS `weshop_specification` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(255) NOT NULL COMMENT '规格名称',
  `sort_order` tinyint(3) DEFAULT 0 COMMENT '排序',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='规格表';

-- 插入一些默认规格数据
INSERT IGNORE INTO `weshop_specification` (`id`, `name`, `sort_order`) VALUES
(1, '颜色', 1),
(2, '尺寸', 2),
(3, '规格', 3),
(4, '型号', 4),
(5, '版本', 5);

-- 创建商品属性表用于存储商品参数
CREATE TABLE IF NOT EXISTS `weshop_goods_attribute` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `goods_id` int(11) NOT NULL COMMENT '商品ID',
  `attribute_name` varchar(100) NOT NULL COMMENT '属性名称',
  `attribute_value` varchar(500) NOT NULL COMMENT '属性值',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品属性表';

-- 创建商品轮播图表
CREATE TABLE IF NOT EXISTS `weshop_goods_gallery` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `goods_id` int(11) NOT NULL COMMENT '商品ID',
  `img_url` varchar(500) NOT NULL COMMENT '图片URL',
  `img_desc` varchar(255) DEFAULT '' COMMENT '图片描述',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品轮播图表';

-- 显示更新完成信息
SELECT 
    'Product table enhanced successfully!' as message,
    'Added fields: cost_price, ot_price, bar_code, bar_code_number, goods_weight, goods_volume, pic_url, brokerage, brokerage_two, quota, quota_show' as added_fields;
