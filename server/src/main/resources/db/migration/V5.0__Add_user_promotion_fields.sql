-- 为用户表添加推广相关字段
-- 用于记录用户的推广关系和推广统计

-- 添加推广相关字段到用户表
ALTER TABLE `weshop_user` 
ADD COLUMN `promoter_id` INT DEFAULT NULL COMMENT '推广者用户ID，记录是谁推广了这个用户',
ADD COLUMN `promotion_code` VARCHAR(50) DEFAULT NULL COMMENT '用户专属推广码，格式：promo_用户ID',
ADD COLUMN `promotion_count` INT DEFAULT 0 COMMENT '推广用户数量统计',
ADD COLUMN `promotion_time` DATETIME DEFAULT NULL COMMENT '成为推广用户的时间（被推广的时间）',
ADD COLUMN `first_promotion_time` DATETIME DEFAULT NULL COMMENT '首次进行推广的时间',
ADD COLUMN `promotion_level` TINYINT DEFAULT 0 COMMENT '推广等级，0-普通用户，1-推广员，2-高级推广员';

-- 添加索引以提高查询性能
CREATE INDEX `idx_user_promoter_id` ON `weshop_user` (`promoter_id`);
CREATE INDEX `idx_user_promotion_code` ON `weshop_user` (`promotion_code`);
CREATE INDEX `idx_user_promotion_level` ON `weshop_user` (`promotion_level`);

-- 为现有用户生成推广码
UPDATE `weshop_user` 
SET `promotion_code` = CONCAT('promo_', id) 
WHERE `promotion_code` IS NULL;

-- 添加表注释
ALTER TABLE `weshop_user` COMMENT = '用户表 - 包含推广关系和推广统计信息';

-- 创建推广关系统计视图（可选）
CREATE OR REPLACE VIEW `v_user_promotion_stats` AS
SELECT 
    u.id as user_id,
    u.username,
    u.nickname,
    u.promotion_code,
    u.promotion_level,
    u.promotion_count,
    u.promotion_time,
    u.first_promotion_time,
    promoter.username as promoter_username,
    promoter.nickname as promoter_nickname,
    (SELECT COUNT(*) FROM weshop_user WHERE promoter_id = u.id) as actual_promotion_count
FROM weshop_user u
LEFT JOIN weshop_user promoter ON u.promoter_id = promoter.id;

-- 显示建表完成信息
SELECT 
    'User promotion fields added successfully!' as message,
    COUNT(*) as total_users,
    COUNT(CASE WHEN promoter_id IS NOT NULL THEN 1 END) as promoted_users,
    COUNT(CASE WHEN promotion_count > 0 THEN 1 END) as promoters
FROM weshop_user;
