-- 创建推广收益相关表

-- 推广收益记录表
CREATE TABLE `weshop_promotion_earnings` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `promoter_id` int NOT NULL COMMENT '推广者用户ID',
  `promoted_user_id` int NOT NULL COMMENT '被推广用户ID',
  `order_id` int NOT NULL COMMENT '订单ID',
  `order_no` varchar(50) NOT NULL COMMENT '订单编号',
  `order_amount` decimal(10,2) NOT NULL COMMENT '订单金额',
  `commission_rate` decimal(5,2) NOT NULL DEFAULT '10.00' COMMENT '佣金比例（百分比）',
  `commission_amount` decimal(10,2) NOT NULL COMMENT '佣金金额',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '收益状态：pending-待确认，confirmed-已确认，cancelled-已取消',
  `order_create_time` datetime NOT NULL COMMENT '订单创建时间',
  `confirm_time` datetime DEFAULT NULL COMMENT '确认收货时间',
  `effective_time` datetime DEFAULT NULL COMMENT '收益生效时间',
  `description` varchar(200) DEFAULT NULL COMMENT '描述信息',
  `is_withdrawn` tinyint NOT NULL DEFAULT '0' COMMENT '是否已提现：0-未提现，1-已提现',
  `withdraw_time` datetime DEFAULT NULL COMMENT '提现时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_promoter_id` (`promoter_id`),
  KEY `idx_promoted_user_id` (`promoted_user_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_effective_time` (`effective_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='推广收益记录表';

-- 提现记录表
CREATE TABLE `weshop_withdraw_record` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int NOT NULL COMMENT '用户ID',
  `amount` decimal(10,2) NOT NULL COMMENT '提现金额',
  `fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '手续费',
  `actual_amount` decimal(10,2) NOT NULL COMMENT '实际到账金额',
  `payment_method` varchar(20) NOT NULL COMMENT '收款方式：wechat-微信，alipay-支付宝，bank-银行卡',
  `account_info` varchar(200) DEFAULT NULL COMMENT '收款账户信息',
  `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '提现状态：pending-待处理，processing-处理中，success-成功，failed-失败',
  `apply_time` datetime NOT NULL COMMENT '申请时间',
  `process_time` datetime DEFAULT NULL COMMENT '处理时间',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_apply_time` (`apply_time`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='提现记录表';

-- 为订单表添加推广相关字段（如果不存在）
ALTER TABLE `weshop_order` 
ADD COLUMN `promoter_id` int DEFAULT NULL COMMENT '推广者用户ID' AFTER `user_id`,
ADD COLUMN `promotion_commission` decimal(10,2) DEFAULT '0.00' COMMENT '推广佣金金额' AFTER `promoter_id`,
ADD COLUMN `commission_status` varchar(20) DEFAULT 'none' COMMENT '佣金状态：none-无佣金，pending-待确认，confirmed-已确认' AFTER `promotion_commission`;

-- 添加索引
ALTER TABLE `weshop_order` 
ADD INDEX `idx_promoter_id` (`promoter_id`),
ADD INDEX `idx_commission_status` (`commission_status`);

-- 插入测试数据（可选）
INSERT INTO `weshop_promotion_earnings` 
(`promoter_id`, `promoted_user_id`, `order_id`, `order_no`, `order_amount`, `commission_rate`, `commission_amount`, `status`, `order_create_time`, `description`) 
VALUES 
(1, 2, 1001, 'ORD20250118001', 100.00, 10.00, 10.00, 'confirmed', '2025-01-18 10:00:00', '推广用户下单获得佣金'),
(1, 3, 1002, 'ORD20250118002', 50.00, 10.00, 5.00, 'pending', '2025-01-18 14:30:00', '推广用户下单，待确认收货');

-- 显示创建完成信息
SELECT 
    'Promotion earnings tables created successfully!' as message,
    (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'weshop_promotion_earnings') as earnings_table_exists,
    (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'weshop_withdraw_record') as withdraw_table_exists;
