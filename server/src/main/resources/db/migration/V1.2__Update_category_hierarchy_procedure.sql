-- 创建存储过程来递归更新分类层级和路径
-- 这个方法可以处理任意深度的分类结构

DELIMITER $$

-- 创建递归更新分类层级和路径的存储过程
CREATE PROCEDURE UpdateCategoryHierarchy()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE category_id INT;
    DECLARE parent_id INT;
    DECLARE parent_level INT;
    DECLARE parent_path VARCHAR(500);
    DECLARE new_level INT;
    DECLARE new_path VARCHAR(500);
    
    -- 声明游标
    DECLARE category_cursor CURSOR FOR 
        SELECT id, parent_id FROM weshop_file_category 
        WHERE is_delete = 0 AND parent_id != 0
        ORDER BY parent_id;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    -- 首先确保所有根分类都正确设置
    UPDATE weshop_file_category 
    SET level = 0, path = '0' 
    WHERE parent_id = 0 AND is_delete = 0;
    
    -- 循环处理直到所有分类都更新完成
    SET @max_iterations = 10; -- 防止无限循环，最多支持10级分类
    SET @current_iteration = 0;
    
    WHILE @current_iteration < @max_iterations DO
        SET @updated_count = 0;
        SET done = FALSE;
        
        -- 打开游标
        OPEN category_cursor;
        
        read_loop: LOOP
            FETCH category_cursor INTO category_id, parent_id;
            IF done THEN
                LEAVE read_loop;
            END IF;
            
            -- 获取父分类的层级和路径
            SELECT level, path INTO parent_level, parent_path
            FROM weshop_file_category 
            WHERE id = parent_id AND is_delete = 0;
            
            -- 如果父分类的层级和路径已经设置，则更新当前分类
            IF parent_level IS NOT NULL AND parent_path IS NOT NULL THEN
                SET new_level = parent_level + 1;
                SET new_path = CONCAT(parent_path, ',', parent_id);
                
                -- 更新当前分类
                UPDATE weshop_file_category 
                SET level = new_level, path = new_path
                WHERE id = category_id;
                
                SET @updated_count = @updated_count + 1;
            END IF;
            
        END LOOP;
        
        -- 关闭游标
        CLOSE category_cursor;
        
        -- 如果这一轮没有更新任何记录，说明已经完成
        IF @updated_count = 0 THEN
            SET @current_iteration = @max_iterations;
        ELSE
            SET @current_iteration = @current_iteration + 1;
        END IF;
        
    END WHILE;
    
END$$

DELIMITER ;

-- 执行存储过程
CALL UpdateCategoryHierarchy();

-- 删除存储过程
DROP PROCEDURE UpdateCategoryHierarchy;

-- 验证更新结果的查询（可选，用于调试）
-- SELECT id, title, parent_id, level, path, create_time 
-- FROM weshop_file_category 
-- WHERE is_delete = 0 
-- ORDER BY level, parent_id, sort_order;
