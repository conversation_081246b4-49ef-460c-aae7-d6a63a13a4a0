-- 更新文件分类表结构
-- 添加新字段以支持更完善的分类管理功能

-- 添加分类层级字段
ALTER TABLE weshop_file_category ADD COLUMN level INT DEFAULT 0 COMMENT '分类层级，0为根分类';

-- 添加分类路径字段
ALTER TABLE weshop_file_category ADD COLUMN path VARCHAR(500) DEFAULT '0' COMMENT '分类路径，如：0,1,2';

-- 添加分类描述字段
ALTER TABLE weshop_file_category ADD COLUMN description VARCHAR(200) DEFAULT '' COMMENT '分类描述';

-- 添加分类图标字段
ALTER TABLE weshop_file_category ADD COLUMN icon VARCHAR(100) DEFAULT '' COMMENT '分类图标';

-- 更新现有数据的层级和路径
-- 使用临时表避免MySQL的UPDATE限制

-- 首先更新根分类（parent_id = 0）
UPDATE weshop_file_category
SET level = 0, path = '0'
WHERE parent_id = 0 AND is_delete = 0;

-- 创建临时表存储父分类信息
CREATE TEMPORARY TABLE temp_parent_categories AS
SELECT id, title, level, path FROM weshop_file_category WHERE parent_id = 0 AND is_delete = 0;

-- 更新一级分类
UPDATE weshop_file_category c1
INNER JOIN temp_parent_categories p ON c1.parent_id = p.id
SET c1.level = 1, c1.path = CONCAT(p.path, ',', p.id)
WHERE c1.is_delete = 0;

-- 删除临时表
DROP TEMPORARY TABLE temp_parent_categories;

-- 创建临时表存储一级分类信息
CREATE TEMPORARY TABLE temp_level1_categories AS
SELECT id, title, level, path FROM weshop_file_category WHERE level = 1 AND is_delete = 0;

-- 更新二级分类
UPDATE weshop_file_category c2
INNER JOIN temp_level1_categories p ON c2.parent_id = p.id
SET c2.level = 2, c2.path = CONCAT(p.path, ',', p.id)
WHERE c2.is_delete = 0 AND c2.level != 1;

-- 删除临时表
DROP TEMPORARY TABLE temp_level1_categories;

-- 创建临时表存储二级分类信息
CREATE TEMPORARY TABLE temp_level2_categories AS
SELECT id, title, level, path FROM weshop_file_category WHERE level = 2 AND is_delete = 0;

-- 更新三级分类
UPDATE weshop_file_category c3
INNER JOIN temp_level2_categories p ON c3.parent_id = p.id
SET c3.level = 3, c3.path = CONCAT(p.path, ',', p.id)
WHERE c3.is_delete = 0 AND c3.level != 2 AND c3.level != 1;

-- 删除临时表
DROP TEMPORARY TABLE temp_level2_categories;

-- 添加索引以提高查询性能
CREATE INDEX idx_file_category_parent_id ON weshop_file_category(parent_id);
CREATE INDEX idx_file_category_level ON weshop_file_category(level);
CREATE INDEX idx_file_category_path ON weshop_file_category(path);
CREATE INDEX idx_file_category_sort_order ON weshop_file_category(sort_order);

-- 添加唯一索引确保同级分类名称不重复
CREATE UNIQUE INDEX uk_file_category_title_parent ON weshop_file_category(title, parent_id, is_delete);
