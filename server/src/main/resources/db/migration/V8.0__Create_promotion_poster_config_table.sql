-- 创建推广海报配置表
CREATE TABLE IF NOT EXISTS `weshop_promotion_poster_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '海报模板名称',
  `title` varchar(100) NOT NULL DEFAULT '' COMMENT '海报标题',
  `description` varchar(500) NOT NULL DEFAULT '' COMMENT '海报描述文字',
  `background_image` varchar(500) NOT NULL DEFAULT '' COMMENT '背景图片URL',
  `logo_image` varchar(500) NOT NULL DEFAULT '' COMMENT 'Logo图片URL',
  `thumbnail_url` varchar(500) NOT NULL DEFAULT '' COMMENT '模板缩略图URL',
  `template_type` varchar(50) NOT NULL DEFAULT 'default' COMMENT '模板类型',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为默认模板(0-否,1-是)',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用(0-否,1-是)',
  `sort_order` int(11) NOT NULL DEFAULT '1' COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_is_default` (`is_default`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='推广海报配置表';

-- 插入默认海报配置数据
INSERT INTO `weshop_promotion_poster_config` (
  `id`, `name`, `title`, `description`, `background_image`, `logo_image`, 
  `thumbnail_url`, `template_type`, `is_default`, `is_active`, `sort_order`
) VALUES (
  1, '经典模板', '伍俊惠选', '精选优质商品，品质生活选择', '/static/images/poster/bg-classic.jpg', '/static/images/logo.png',
  '/static/images/poster/thumb-classic.jpg', 'classic', 1, 1, 1
);

-- 创建公司配置表
CREATE TABLE IF NOT EXISTS `weshop_company_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `company_name` varchar(100) NOT NULL DEFAULT '' COMMENT '公司名称',
  `company_address` varchar(200) NOT NULL DEFAULT '' COMMENT '公司地址',
  `contact_phone` varchar(50) NOT NULL DEFAULT '' COMMENT '联系电话',
  `contact_wechat` varchar(100) NOT NULL DEFAULT '' COMMENT '联系微信',
  `company_logo` varchar(500) NOT NULL DEFAULT '' COMMENT '公司Logo',
  `work_time` varchar(100) NOT NULL DEFAULT '' COMMENT '工作时间',
  `description` varchar(500) NOT NULL DEFAULT '' COMMENT '公司描述',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用(0-否,1-是)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公司配置表';

-- 插入默认公司配置数据
INSERT INTO `weshop_company_config` (
  `id`, `company_name`, `company_address`, `contact_phone`, `contact_wechat`, 
  `company_logo`, `work_time`, `description`, `is_active`
) VALUES (
  1, '伍俊惠选', '陕西省西安市雁塔区科技路', '136 2929 5757', 'WJSY-029-888',
  '/static/images/logo.png', '工作时间：7:00-24:00', '专注于为用户提供优质商品和服务的电商平台', 1
);