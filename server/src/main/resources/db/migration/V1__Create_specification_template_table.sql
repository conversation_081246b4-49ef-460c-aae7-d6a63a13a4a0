-- 创建规格模板表
CREATE TABLE IF NOT EXISTS `weshop_specification_template` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `rule_name` varchar(100) NOT NULL COMMENT '规格模板名称',
  `attr_name` varchar(100) DEFAULT NULL COMMENT '规格名称',
  `attr_value` text COMMENT '规格值，JSON格式存储',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：1启用，0禁用',
  PRIMARY KEY (`id`),
  KEY `idx_rule_name` (`rule_name`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='规格模板表';

-- 插入示例数据
INSERT IGNORE INTO `weshop_specification_template` (`rule_name`, `attr_name`, `attr_value`, `status`) VALUES
('颜色尺寸模板', '颜色', '[{"value":"颜色","detail":["红色","蓝色","绿色"],"add_pic":0},{"value":"尺寸","detail":["S","M","L","XL"],"add_pic":0}]', 1),
('服装规格模板', '颜色', '[{"value":"颜色","detail":["黑色","白色","灰色"],"add_pic":0},{"value":"尺寸","detail":["S","M","L","XL","XXL"],"add_pic":0}]', 1),
('电子产品规格', '容量', '[{"value":"容量","detail":["64GB","128GB","256GB"],"add_pic":0},{"value":"颜色","detail":["深空灰","银色","金色"],"add_pic":0}]', 1);
