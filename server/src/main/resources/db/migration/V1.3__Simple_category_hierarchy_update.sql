-- 简单的分步骤更新分类层级和路径
-- 这个方法更容易理解和维护

-- 步骤1：更新根分类（parent_id = 0）
UPDATE weshop_file_category 
SET level = 0, path = '0' 
WHERE parent_id = 0 AND is_delete = 0;

-- 步骤2：更新一级分类
UPDATE weshop_file_category 
SET level = 1, path = CONCAT('0,', parent_id)
WHERE parent_id IN (
    SELECT temp.id FROM (
        SELECT id FROM weshop_file_category 
        WHERE parent_id = 0 AND is_delete = 0
    ) AS temp
) AND is_delete = 0;

-- 步骤3：更新二级分类
UPDATE weshop_file_category 
SET level = 2, path = CONCAT('0,', parent_id, ',', parent_id)
WHERE parent_id IN (
    SELECT temp.id FROM (
        SELECT id FROM weshop_file_category 
        WHERE level = 1 AND is_delete = 0
    ) AS temp
) AND is_delete = 0 AND level IS NULL;

-- 步骤4：更新三级分类（如果存在）
UPDATE weshop_file_category c
INNER JOIN (
    SELECT id, path FROM weshop_file_category 
    WHERE level = 2 AND is_delete = 0
) p ON c.parent_id = p.id
SET c.level = 3, c.path = CONCAT(p.path, ',', c.parent_id)
WHERE c.is_delete = 0 AND c.level IS NULL;

-- 步骤5：更新四级分类（如果存在）
UPDATE weshop_file_category c
INNER JOIN (
    SELECT id, path FROM weshop_file_category 
    WHERE level = 3 AND is_delete = 0
) p ON c.parent_id = p.id
SET c.level = 4, c.path = CONCAT(p.path, ',', c.parent_id)
WHERE c.is_delete = 0 AND c.level IS NULL;

-- 步骤6：更新五级分类（如果存在）
UPDATE weshop_file_category c
INNER JOIN (
    SELECT id, path FROM weshop_file_category 
    WHERE level = 4 AND is_delete = 0
) p ON c.parent_id = p.id
SET c.level = 5, c.path = CONCAT(p.path, ',', c.parent_id)
WHERE c.is_delete = 0 AND c.level IS NULL;

-- 验证更新结果
-- 检查是否还有未更新的分类
SELECT COUNT(*) as unprocessed_count 
FROM weshop_file_category 
WHERE is_delete = 0 AND level IS NULL;

-- 显示分类层级结构（用于验证）
SELECT 
    id,
    title,
    parent_id,
    level,
    path,
    CASE 
        WHEN level = 0 THEN title
        WHEN level = 1 THEN CONCAT('  └─ ', title)
        WHEN level = 2 THEN CONCAT('    └─ ', title)
        WHEN level = 3 THEN CONCAT('      └─ ', title)
        WHEN level = 4 THEN CONCAT('        └─ ', title)
        ELSE CONCAT(REPEAT('  ', level * 2), '└─ ', title)
    END as hierarchy_display
FROM weshop_file_category 
WHERE is_delete = 0 
ORDER BY 
    CASE WHEN parent_id = 0 THEN id ELSE parent_id END,
    level,
    sort_order,
    id;
