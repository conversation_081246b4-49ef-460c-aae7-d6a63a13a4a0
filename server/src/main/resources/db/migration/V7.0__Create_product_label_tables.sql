-- 创建商品标签相关表

-- 创建商品标签分类表
CREATE TABLE IF NOT EXISTS `weshop_product_label_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1启用，0禁用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品标签分类表';

-- 创建商品标签表
CREATE TABLE IF NOT EXISTS `weshop_product_label` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `cate_id` int(11) NOT NULL DEFAULT 0 COMMENT '分类ID',
  `name` varchar(100) NOT NULL COMMENT '标签名称',
  `type` tinyint(1) DEFAULT 1 COMMENT '样式类型：1自定义，2图片',
  `font_color` varchar(20) DEFAULT '#e93323' COMMENT '字体颜色',
  `bg_color` varchar(20) DEFAULT '#fff' COMMENT '背景颜色',
  `border_color` varchar(20) DEFAULT '#e93323' COMMENT '边框颜色',
  `image` varchar(500) DEFAULT '' COMMENT '标签图片URL',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `is_show` tinyint(1) DEFAULT 1 COMMENT '是否显示：1显示，0隐藏',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1启用，0禁用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_cate_id` (`cate_id`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_status` (`status`),
  KEY `idx_is_show` (`is_show`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品标签表';

-- 创建商品标签关联表
CREATE TABLE IF NOT EXISTS `weshop_goods_label` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `goods_id` int(11) NOT NULL COMMENT '商品ID',
  `label_id` int(11) NOT NULL COMMENT '标签ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_goods_label` (`goods_id`, `label_id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_label_id` (`label_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品标签关联表';

-- 插入默认标签分类数据
INSERT IGNORE INTO `weshop_product_label_category` (`id`, `name`, `sort_order`, `status`) VALUES
(1, '促销标签', 1, 1),
(2, '品质标签', 2, 1),
(3, '特色标签', 3, 1),
(4, '活动标签', 4, 1);

-- 插入默认标签数据
INSERT IGNORE INTO `weshop_product_label` (`id`, `cate_id`, `name`, `type`, `font_color`, `bg_color`, `border_color`, `sort_order`, `is_show`, `status`) VALUES
(1, 1, '热销', 1, '#fff', '#ff4757', '#ff4757', 1, 1, 1),
(2, 1, '新品', 1, '#fff', '#2ed573', '#2ed573', 2, 1, 1),
(3, 1, '限时特价', 1, '#fff', '#ffa502', '#ffa502', 3, 1, 1),
(4, 1, '包邮', 1, '#fff', '#3742fa', '#3742fa', 4, 1, 1),
(5, 2, '精选', 1, '#fff', '#8b5cf6', '#8b5cf6', 5, 1, 1),
(6, 2, '推荐', 1, '#fff', '#06d6a0', '#06d6a0', 6, 1, 1),
(7, 3, '爆款', 1, '#fff', '#f72585', '#f72585', 7, 1, 1),
(8, 4, '秒杀', 1, '#fff', '#e63946', '#e63946', 8, 1, 1);

-- 显示创建结果
SELECT 
    'Product label tables created successfully!' as message,
    (SELECT COUNT(*) FROM weshop_product_label_category) as categories_count,
    (SELECT COUNT(*) FROM weshop_product_label) as labels_count;
