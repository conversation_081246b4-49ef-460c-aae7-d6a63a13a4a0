-- 创建微信发货信息管理表
CREATE TABLE IF NOT EXISTS `weshop_wx_shipping_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` int(11) DEFAULT NULL COMMENT '订单ID',
  `transaction_id` varchar(64) DEFAULT NULL COMMENT '微信支付交易单号',
  `mchid` varchar(32) DEFAULT NULL COMMENT '商户号',
  `out_trade_no` varchar(64) DEFAULT NULL COMMENT '商户订单号',
  `openid` varchar(64) NOT NULL COMMENT '用户openid',
  `logistics_type` int(11) NOT NULL COMMENT '物流模式：1-实体物流配送，2-同城配送，3-虚拟商品，4-用户自提',
  `delivery_mode` int(11) NOT NULL COMMENT '发货模式：1-统一发货，2-分拆发货',
  `is_all_delivered` tinyint(1) DEFAULT NULL COMMENT '是否全部发货完成（分拆发货时使用）',
  `shipping_list` text COMMENT '物流信息JSON，包含物流单号、快递公司等',
  `upload_time` datetime DEFAULT NULL COMMENT '上传到微信的时间',
  `wx_status` int(11) DEFAULT 0 COMMENT '微信返回状态：0-待上传，1-上传成功，2-上传失败',
  `wx_error_msg` varchar(500) DEFAULT NULL COMMENT '微信返回错误信息',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_transaction_id` (`transaction_id`),
  KEY `idx_mchid_out_trade_no` (`mchid`, `out_trade_no`),
  KEY `idx_openid` (`openid`),
  KEY `idx_wx_status` (`wx_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信发货信息管理表';

-- 为订单表添加微信发货相关字段
ALTER TABLE `weshop_order` 
ADD COLUMN `transaction_id` varchar(64) DEFAULT NULL COMMENT '微信支付交易单号',
ADD COLUMN `mchid` varchar(32) DEFAULT NULL COMMENT '商户号',
ADD COLUMN `shipping_info_status` int(11) DEFAULT 0 COMMENT '发货状态：0-未发货，1-已发货，2-已收货',
ADD COLUMN `shipping_time` datetime DEFAULT NULL COMMENT '发货时间',
ADD COLUMN `confirm_receive_time` datetime DEFAULT NULL COMMENT '确认收货时间';

-- 添加索引
CREATE INDEX `idx_order_transaction_id` ON `weshop_order` (`transaction_id`);
CREATE INDEX `idx_order_mchid` ON `weshop_order` (`mchid`);
CREATE INDEX `idx_order_shipping_status` ON `weshop_order` (`shipping_info_status`);

-- 为订单快递表添加微信相关字段
ALTER TABLE `weshop_order_express`
ADD COLUMN `wx_upload_status` int(11) DEFAULT 0 COMMENT '微信上传状态：0-未上传，1-已上传，2-上传失败',
ADD COLUMN `wx_upload_time` datetime DEFAULT NULL COMMENT '微信上传时间',
ADD COLUMN `wx_error_msg` varchar(500) DEFAULT NULL COMMENT '微信上传错误信息';

-- 添加索引
CREATE INDEX `idx_express_wx_status` ON `weshop_order_express` (`wx_upload_status`);

-- 创建微信发货配置表
CREATE TABLE IF NOT EXISTS `weshop_wx_shipping_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` varchar(500) DEFAULT NULL COMMENT '配置值',
  `config_desc` varchar(200) DEFAULT NULL COMMENT '配置描述',
  `is_enabled` tinyint(1) DEFAULT 1 COMMENT '是否启用：1启用，0禁用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`),
  KEY `idx_enabled` (`is_enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信发货配置表';

-- 插入默认配置
INSERT INTO `weshop_wx_shipping_config` (`config_key`, `config_value`, `config_desc`) VALUES
('msg_jump_path', 'pages/order/detail', '消息跳转路径'),
('auto_upload_shipping', '1', '是否自动上传发货信息：1是，0否'),
('auto_notify_receive', '1', '是否自动发送确认收货提醒：1是，0否'),
('service_enabled', '0', '发货信息管理服务是否已开通：1是，0否');

-- 添加表注释
ALTER TABLE `weshop_wx_shipping_info` COMMENT = '微信发货信息管理表 - 存储向微信平台上传的发货信息';
ALTER TABLE `weshop_wx_shipping_config` COMMENT = '微信发货配置表 - 存储微信发货相关配置信息';

-- 显示建表完成信息
SELECT 
    'WeChat shipping info tables created successfully!' as message,
    (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'weshop_wx_shipping_info') as shipping_info_table_exists,
    (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'weshop_wx_shipping_config') as shipping_config_table_exists;
