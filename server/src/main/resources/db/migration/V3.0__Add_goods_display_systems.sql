-- 为商品表添加展示系统字段
-- 用于标记商品在哪些系统中展示

-- 添加展示系统字段到商品表
ALTER TABLE `weshop_goods` 
ADD COLUMN `display_systems` VARCHAR(500) DEFAULT NULL COMMENT '商品展示系统，JSON格式存储多个系统ID，如：[1,2,3]';

-- 添加索引以提高查询性能
CREATE INDEX `idx_goods_display_systems` ON `weshop_goods` (`display_systems`);

-- 创建系统配置表，用于管理可选的展示系统
CREATE TABLE IF NOT EXISTS `weshop_display_system` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '系统ID',
  `name` varchar(100) NOT NULL COMMENT '系统名称',
  `code` varchar(50) NOT NULL COMMENT '系统编码',
  `description` varchar(200) DEFAULT NULL COMMENT '系统描述',
  `icon` varchar(100) DEFAULT NULL COMMENT '系统图标',
  `color` varchar(20) DEFAULT '#409EFF' COMMENT '系统颜色',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `is_enabled` tinyint(1) DEFAULT 1 COMMENT '是否启用：1启用，0禁用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_system_code` (`code`),
  KEY `idx_enabled_sort` (`is_enabled`, `sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品展示系统配置表';

-- 插入默认的展示系统数据
INSERT INTO `weshop_display_system` (`name`, `code`, `description`, `icon`, `color`, `sort_order`, `is_enabled`) VALUES
('微信小程序', 'wechat_mini', '微信小程序商城', 'el-icon-chat-dot-round', '#07C160', 1, 1),
('H5商城', 'h5_mall', 'H5移动端商城', 'el-icon-mobile-phone', '#409EFF', 2, 1),
('PC商城', 'pc_mall', 'PC端商城网站', 'el-icon-monitor', '#606266', 3, 1),
('APP商城', 'app_mall', '移动APP商城', 'el-icon-mobile', '#E6A23C', 4, 1),
('抖音小程序', 'douyin_mini', '抖音小程序商城', 'el-icon-video-camera', '#FF3040', 5, 1),
('支付宝小程序', 'alipay_mini', '支付宝小程序商城', 'el-icon-wallet', '#1677FF', 6, 1);

-- 为现有商品设置默认展示系统（全部系统）
UPDATE `weshop_goods` 
SET `display_systems` = '[1,2,3,4,5,6]' 
WHERE `display_systems` IS NULL AND `is_delete` = 0;

-- 添加表注释
ALTER TABLE `weshop_goods` COMMENT = '商品表 - 包含商品展示系统配置';
