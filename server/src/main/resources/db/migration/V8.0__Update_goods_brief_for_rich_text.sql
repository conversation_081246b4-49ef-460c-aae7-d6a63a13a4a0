-- 更新商品简介字段以支持富文本编辑器
-- 版本: V8.0
-- 说明: 将 goodsBrief 字段类型从 VARCHAR 改为 LONGTEXT 以支持富文本内容

-- 修改商品表的 goodsBrief 字段类型
ALTER TABLE `weshop_goods` 
MODIFY COLUMN `goods_brief` LONGTEXT COMMENT '商品简介，支持富文本内容';

-- 添加索引以提高查询性能（对于 LONGTEXT 字段，我们可以创建前缀索引）
-- 注意：如果字段已经有索引，需要先删除再创建
-- DROP INDEX IF EXISTS `idx_goods_brief` ON `weshop_goods`;
-- CREATE INDEX `idx_goods_brief` ON `weshop_goods` (`goods_brief`(100));

-- 显示更新完成信息
SELECT 
    'Goods brief field updated successfully!' as message,
    'Field type changed from VARCHAR to LONGTEXT to support rich text content' as description,
    'No character limit for goods brief field' as feature;
