-- 创建银行卡信息表
-- 用于存储用户提现时的银行卡信息

CREATE TABLE `weshop_bank_card_info` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int NOT NULL COMMENT '用户ID',
  `bank_name` varchar(100) NOT NULL COMMENT '开户行名称',
  `card_number` varchar(50) NOT NULL COMMENT '银行卡号（加密存储）',
  `card_holder` varchar(50) NOT NULL COMMENT '持卡人姓名',
  `bank_address` varchar(200) DEFAULT NULL COMMENT '开户行地址',
  `card_image_url` varchar(500) DEFAULT NULL COMMENT '银行卡照片URL',
  `is_default` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否默认银行卡：0-否，1-是',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_card_number` (`card_number`),
  KEY `idx_is_default` (`is_default`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='银行卡信息表';

-- 扩展提现记录表，添加银行卡信息关联字段
ALTER TABLE `weshop_withdraw_record` 
ADD COLUMN `bank_card_id` int DEFAULT NULL COMMENT '银行卡信息ID' AFTER `account_info`,
ADD COLUMN `bank_card_snapshot` json DEFAULT NULL COMMENT '银行卡信息快照（JSON格式存储提现时的银行卡信息）' AFTER `bank_card_id`;

-- 添加银行卡信息关联索引
ALTER TABLE `weshop_withdraw_record` 
ADD INDEX `idx_bank_card_id` (`bank_card_id`);

-- 添加外键约束（可选，如果需要严格的数据一致性）
-- ALTER TABLE `weshop_withdraw_record` 
-- ADD CONSTRAINT `fk_withdraw_bank_card` 
-- FOREIGN KEY (`bank_card_id`) REFERENCES `weshop_bank_card_info` (`id`) 
-- ON DELETE SET NULL ON UPDATE CASCADE;

-- 创建示例数据（可选，用于测试）
-- INSERT INTO `weshop_bank_card_info` 
-- (`user_id`, `bank_name`, `card_number`, `card_holder`, `bank_address`, `card_image_url`, `is_default`) 
-- VALUES 
-- (1, '中国工商银行', 'ENCRYPTED_CARD_NUMBER_1', '测试用户', '北京市朝阳区工商银行支行', '/uploads/cards/test_card_1.jpg', 1),
-- (2, '中国建设银行', 'ENCRYPTED_CARD_NUMBER_2', '测试用户2', '上海市浦东新区建设银行支行', '/uploads/cards/test_card_2.jpg', 1);

-- 显示表创建完成信息
SELECT 
    'Bank card info table created successfully!' as message,
    (SELECT COUNT(*) FROM information_schema.tables 
     WHERE table_schema = DATABASE() AND table_name = 'weshop_bank_card_info') as table_exists,
    (SELECT COUNT(*) FROM information_schema.columns 
     WHERE table_schema = DATABASE() 
     AND table_name = 'weshop_withdraw_record' 
     AND column_name IN ('bank_card_id', 'bank_card_snapshot')) as withdraw_columns_added;