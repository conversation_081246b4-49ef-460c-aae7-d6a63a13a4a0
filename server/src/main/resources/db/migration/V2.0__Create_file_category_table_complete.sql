-- 完整的文件分类表建表语句
-- 版本: V2.0
-- 说明: 包含所有增强功能的完整分类表结构

-- 删除已存在的表（如果存在）
DROP TABLE IF EXISTS `weshop_file_category`;

-- 创建文件分类表
CREATE TABLE `weshop_file_category` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `title` varchar(50) NOT NULL COMMENT '分类名称',
  `parent_id` int NOT NULL DEFAULT '0' COMMENT '父分类ID，0表示根分类',
  `level` int NOT NULL DEFAULT '0' COMMENT '分类层级，0为根分类',
  `path` varchar(500) NOT NULL DEFAULT '0' COMMENT '分类路径，如：0,1,2',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序值，数值越小越靠前',
  `description` varchar(200) DEFAULT NULL COMMENT '分类描述',
  `icon` varchar(100) DEFAULT NULL COMMENT '分类图标',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除，0-否，1-是',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_level` (`level`),
  KEY `idx_path` (`path`(255)),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_is_delete` (`is_delete`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_parent_sort` (`parent_id`, `sort_order`),
  KEY `idx_parent_delete` (`parent_id`, `is_delete`),
  UNIQUE KEY `uk_title_parent_delete` (`title`, `parent_id`, `is_delete`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件分类表';

-- 插入默认的根分类数据
INSERT INTO `weshop_file_category` (`id`, `title`, `parent_id`, `level`, `path`, `sort_order`, `description`, `icon`, `is_delete`, `create_time`, `update_time`) VALUES
(1, '图片分类', 0, 0, '0', 1, '默认图片分类', 'el-icon-picture', 0, NOW(), NOW()),
(2, '文档分类', 0, 0, '0', 2, '默认文档分类', 'el-icon-document', 0, NOW(), NOW()),
(3, '视频分类', 0, 0, '0', 3, '默认视频分类', 'el-icon-video-camera', 0, NOW(), NOW()),
(4, '音频分类', 0, 0, '0', 4, '默认音频分类', 'el-icon-microphone', 0, NOW(), NOW()),
(5, '其他文件', 0, 0, '0', 5, '其他类型文件', 'el-icon-folder', 0, NOW(), NOW());

-- 插入一些示例子分类
INSERT INTO `weshop_file_category` (`title`, `parent_id`, `level`, `path`, `sort_order`, `description`, `icon`, `is_delete`, `create_time`, `update_time`) VALUES
-- 图片分类的子分类
('产品图片', 1, 1, '0,1', 1, '商品产品相关图片', 'el-icon-goods', 0, NOW(), NOW()),
('轮播图片', 1, 1, '0,1', 2, '首页轮播图片', 'el-icon-picture-outline', 0, NOW(), NOW()),
('广告图片', 1, 1, '0,1', 3, '广告宣传图片', 'el-icon-postcard', 0, NOW(), NOW()),
('用户头像', 1, 1, '0,1', 4, '用户头像图片', 'el-icon-user', 0, NOW(), NOW()),

-- 文档分类的子分类
('用户手册', 2, 1, '0,2', 1, '用户使用手册', 'el-icon-notebook-1', 0, NOW(), NOW()),
('技术文档', 2, 1, '0,2', 2, '技术相关文档', 'el-icon-document-copy', 0, NOW(), NOW()),
('合同文件', 2, 1, '0,2', 3, '合同协议文件', 'el-icon-document-checked', 0, NOW(), NOW()),

-- 视频分类的子分类
('产品视频', 3, 1, '0,3', 1, '产品展示视频', 'el-icon-video-play', 0, NOW(), NOW()),
('教程视频', 3, 1, '0,3', 2, '使用教程视频', 'el-icon-video-camera-solid', 0, NOW(), NOW()),

-- 音频分类的子分类
('背景音乐', 4, 1, '0,4', 1, '背景音乐文件', 'el-icon-headset', 0, NOW(), NOW()),
('语音文件', 4, 1, '0,4', 2, '语音录音文件', 'el-icon-microphone', 0, NOW(), NOW());

-- 创建触发器，自动更新 update_time
DELIMITER $$
CREATE TRIGGER `tr_weshop_file_category_update_time`
BEFORE UPDATE ON `weshop_file_category`
FOR EACH ROW
BEGIN
    SET NEW.update_time = CURRENT_TIMESTAMP;
END$$
DELIMITER ;

-- 创建存储过程：更新分类层级和路径
DELIMITER $$
CREATE PROCEDURE `sp_update_category_hierarchy`()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_id INT;
    DECLARE v_parent_id INT;
    DECLARE v_level INT;
    DECLARE v_path VARCHAR(500);
    DECLARE v_parent_level INT;
    DECLARE v_parent_path VARCHAR(500);
    DECLARE updated_count INT DEFAULT 0;
    DECLARE max_iterations INT DEFAULT 100;
    DECLARE current_iteration INT DEFAULT 0;
    
    -- 声明游标
    DECLARE category_cursor CURSOR FOR 
        SELECT id, parent_id FROM weshop_file_category WHERE is_delete = 0 ORDER BY parent_id, id;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    -- 开始事务
    START TRANSACTION;
    
    -- 初始化根分类
    UPDATE weshop_file_category 
    SET level = 0, path = '0' 
    WHERE parent_id = 0 AND is_delete = 0;
    
    -- 循环更新子分类
    WHILE current_iteration < max_iterations DO
        SET current_iteration = current_iteration + 1;
        SET updated_count = 0;
        SET done = FALSE;
        
        OPEN category_cursor;
        
        read_loop: LOOP
            FETCH category_cursor INTO v_id, v_parent_id;
            IF done THEN
                LEAVE read_loop;
            END IF;
            
            -- 跳过根分类
            IF v_parent_id = 0 THEN
                ITERATE read_loop;
            END IF;
            
            -- 获取父分类信息
            SELECT level, path INTO v_parent_level, v_parent_path
            FROM weshop_file_category 
            WHERE id = v_parent_id AND is_delete = 0;
            
            -- 如果父分类信息存在且有效
            IF v_parent_level IS NOT NULL AND v_parent_path IS NOT NULL THEN
                SET v_level = v_parent_level + 1;
                SET v_path = CONCAT(v_parent_path, ',', v_parent_id);
                
                -- 更新当前分类
                UPDATE weshop_file_category 
                SET level = v_level, path = v_path 
                WHERE id = v_id AND is_delete = 0 
                AND (level != v_level OR path != v_path);
                
                IF ROW_COUNT() > 0 THEN
                    SET updated_count = updated_count + 1;
                END IF;
            END IF;
        END LOOP;
        
        CLOSE category_cursor;
        
        -- 如果没有更新任何记录，说明已经完成
        IF updated_count = 0 THEN
            LEAVE;
        END IF;
    END WHILE;
    
    -- 提交事务
    COMMIT;
    
    -- 返回更新统计
    SELECT 
        current_iteration as iterations_used,
        (SELECT COUNT(*) FROM weshop_file_category WHERE is_delete = 0) as total_categories,
        (SELECT COUNT(*) FROM weshop_file_category WHERE is_delete = 0 AND level IS NOT NULL) as categories_with_level,
        (SELECT COUNT(*) FROM weshop_file_category WHERE is_delete = 0 AND path IS NOT NULL AND path != '') as categories_with_path;
        
END$$
DELIMITER ;

-- 创建函数：检查是否为子分类
DELIMITER $$
CREATE FUNCTION `fn_is_descendant`(ancestor_id INT, descendant_id INT) 
RETURNS BOOLEAN
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_path VARCHAR(500);
    DECLARE result BOOLEAN DEFAULT FALSE;
    
    -- 获取descendant的路径
    SELECT path INTO v_path 
    FROM weshop_file_category 
    WHERE id = descendant_id AND is_delete = 0;
    
    -- 检查ancestor_id是否在路径中
    IF v_path IS NOT NULL AND FIND_IN_SET(ancestor_id, REPLACE(v_path, ',', ',')) > 0 THEN
        SET result = TRUE;
    END IF;
    
    RETURN result;
END$$
DELIMITER ;

-- 创建视图：分类树视图
CREATE VIEW `v_category_tree` AS
SELECT 
    c.id,
    c.title,
    c.parent_id,
    c.level,
    c.path,
    c.sort_order,
    c.description,
    c.icon,
    c.create_time,
    c.update_time,
    p.title as parent_name,
    (SELECT COUNT(*) FROM weshop_file_category cc WHERE cc.parent_id = c.id AND cc.is_delete = 0) as children_count,
    (SELECT COUNT(*) FROM weshop_file_attachment fa WHERE fa.category_id = c.id AND fa.is_delete = 0) as file_count
FROM weshop_file_category c
LEFT JOIN weshop_file_category p ON c.parent_id = p.id AND p.is_delete = 0
WHERE c.is_delete = 0
ORDER BY c.level, c.parent_id, c.sort_order, c.id;

-- 执行一次层级更新
CALL sp_update_category_hierarchy();

-- 创建索引优化查询性能
-- 复合索引用于常见查询场景
CREATE INDEX `idx_level_parent_sort` ON `weshop_file_category` (`level`, `parent_id`, `sort_order`);
CREATE INDEX `idx_title_delete` ON `weshop_file_category` (`title`, `is_delete`);

-- 添加表注释
ALTER TABLE `weshop_file_category` COMMENT = '文件分类表 - 支持无限级分类，包含层级路径管理';

-- 显示建表完成信息
SELECT 
    'File category table created successfully!' as message,
    COUNT(*) as total_categories,
    MAX(level) as max_level
FROM weshop_file_category 
WHERE is_delete = 0;
