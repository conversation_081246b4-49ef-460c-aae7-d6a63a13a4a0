-- File category table
CREATE TABLE IF NOT EXISTS `weshop_file_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL COMMENT 'Category name',
  `parent_id` int(11) NOT NULL DEFAULT '0' COMMENT 'Parent category ID',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT 'Sort order',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Is deleted',
  `create_time` datetime NOT NULL COMMENT 'Creation time',
  `update_time` datetime NOT NULL COMMENT 'Update time',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_is_delete` (`is_delete`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='File category';

-- File attachment table
CREATE TABLE IF NOT EXISTS `weshop_file_attachment` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL COMMENT 'File name',
  `path` varchar(255) NOT NULL COMMENT 'File path',
  `url` varchar(255) NOT NULL COMMENT 'File URL',
  `size` bigint(20) NOT NULL DEFAULT '0' COMMENT 'File size in bytes',
  `type` varchar(50) NOT NULL COMMENT 'File type (extension)',
  `category_id` int(11) NOT NULL DEFAULT '0' COMMENT 'Category ID',
  `user_id` int(11) NOT NULL DEFAULT '0' COMMENT 'Upload user ID',
  `user_name` varchar(255) DEFAULT NULL COMMENT 'Upload user name',
  `upload_source` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Upload source (0: local, 1: online, 2: scan)',
  `is_delete` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Is deleted',
  `create_time` datetime NOT NULL COMMENT 'Creation time',
  `update_time` datetime NOT NULL COMMENT 'Update time',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_is_delete` (`is_delete`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='File attachment';

-- Scan upload table
CREATE TABLE IF NOT EXISTS `weshop_scan_upload` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `scan_token` varchar(255) NOT NULL COMMENT 'Scan token',
  `category_id` int(11) NOT NULL DEFAULT '0' COMMENT 'Category ID',
  `user_id` int(11) NOT NULL DEFAULT '0' COMMENT 'User ID',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Status (0: pending, 1: completed)',
  `expire_time` datetime NOT NULL COMMENT 'Expiration time',
  `create_time` datetime NOT NULL COMMENT 'Creation time',
  `update_time` datetime NOT NULL COMMENT 'Update time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_scan_token` (`scan_token`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_expire_time` (`expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Scan upload';

-- Insert default category
INSERT INTO `weshop_file_category` (`title`, `parent_id`, `sort_order`, `is_delete`, `create_time`, `update_time`)
VALUES ('默认分组', 0, 0, 0, NOW(), NOW()); 