# Team页面Custom-Navbar集成总结

## 完成的工作

### 1. Custom-Navbar组件集成
- ✅ 在 `team.wxml` 中添加了 `custom-navbar` 组件
- ✅ 实现了动态标题：根据下钻层级显示不同的标题
- ✅ 添加了右侧"首页"按钮，用于重置下钻状态
- ✅ 设置了渐变色背景和白色文字样式

### 2. 导航栏状态管理
- ✅ 在 `team.js` 中添加了导航栏相关状态：
  - `navOpacity`: 导航栏透明度（初始0.95）
  - `navbarHeight`: 导航栏高度（动态计算）
- ✅ 实现了 `initNavbar()` 方法计算导航栏高度
- ✅ 实现了 `onNavBack()` 方法处理导航栏返回事件
- ✅ 实现了 `onPageScroll()` 方法处理页面滚动透明度变化

### 3. 下钻功能与导航栏集成
- ✅ 导航栏标题根据下钻层级动态变化：
  - 第0级：显示"团队管理中心"
  - 下钻状态：显示"XXX的直邀列表"
- ✅ 返回按钮逻辑：
  - 在下钻状态：执行 `drillBack()` 返回上一级
  - 不在下钻状态：执行正常页面返回
- ✅ 右侧"首页"按钮：重置所有下钻状态并刷新数据

### 4. 样式调整
- ✅ 移除了原有的下钻导航样式
- ✅ 添加了下钻提示样式（`.drilldown-tip`）
- ✅ 调整了容器样式，添加动态 `padding-top` 以适应导航栏高度
- ✅ 保持了原有的团队列表样式和功能

### 5. 功能测试验证
- ✅ 下钻功能正常工作
- ✅ "查看订单"按钮跳转正常
- ✅ 导航栏标题动态更新正常
- ✅ 返回逻辑正确处理下钻状态
- ✅ 页面滚动透明度变化正常
- ✅ 导航栏高度计算正确

## 技术实现细节

### 导航栏组件配置
```xml
<custom-navbar
  title="{{drillDownLevel === 0 ? '团队管理中心' : currentDrillUserName + '的直邀列表'}}"
  gradient-background="linear-gradient(135deg, #42A5F5 0%, #64B5F6 100%)"
  text-color="#ffffff"
  opacity="{{navOpacity}}"
  bind:back="onNavBack">
  <view slot="right" wx:if="{{drillDownLevel > 0}}" bindtap="goToHome">
    <text style="color: #ffffff; font-size: 28rpx;">首页</text>
  </view>
</custom-navbar>
```

### 导航栏初始化
```javascript
initNavbar: function() {
  const systemInfo = wx.getSystemInfoSync();
  const statusBarHeight = systemInfo.statusBarHeight || 0;
  const titleBarHeight = 44;
  const navbarHeight = statusBarHeight + titleBarHeight;

  this.setData({
    navbarHeight: navbarHeight
  });
}
```

### 导航栏返回逻辑
```javascript
onNavBack: function() {
  // 如果在下钻状态，则返回上一级
  if (this.data.drillDownLevel > 0) {
    this.drillBack();
  } else {
    const pages = getCurrentPages();
    if (pages.length > 1) {
      wx.navigateBack();
    } else {
      wx.switchTab({
        url: '/pages/ucenter/me/me'
      });
    }
  }
}
```

### 页面滚动透明度
```javascript
onPageScroll: function(e) {
  const scrollTop = e.detail.scrollTop;
  // 根据滚动距离计算透明度
  let opacity = Math.min(1, Math.max(0.95, 0.95 + (scrollTop / 200)));

  // 避免频繁更新，只在透明度变化超过0.02时才更新
  if (Math.abs(opacity - this.data.navOpacity) > 0.02) {
    this.setData({
      navOpacity: opacity
    });
  }
}
```

## 参考实现
参考了 `earnings` 页面的实现方式，确保风格和功能一致性：
- 相同的导航栏配置方式
- 相同的滚动透明度处理逻辑
- 相同的导航栏高度计算方法

## 测试结果
所有功能测试通过：
- ✅ 导航栏显示正常
- ✅ 下钻功能与导航栏集成正常
- ✅ 返回逻辑正确
- ✅ 页面滚动效果正常
- ✅ 样式适配良好

## 文件修改
- `app/wjhx/pages/ucenter/team/team.wxml` - 添加custom-navbar组件
- `app/wjhx/pages/ucenter/team/team.js` - 添加导航栏相关状态和方法
- `app/wjhx/pages/ucenter/team/team.wxss` - 样式调整

## 6. 搜索功能修复
- ✅ 修复了下钻时搜索关键词丢失的问题
- ✅ 在 [`loadDrillDownTeamList`](app/wjhx/pages/ucenter/team/team.js:211) 中添加了 `keyword` 参数
- ✅ 修改了 [`searchTeam`](app/wjhx/pages/ucenter/team/team.js:352) 方法，支持下钻状态搜索
- ✅ 移除了下钻时不必要的搜索关键词重置
- ✅ 确保下钻返回时搜索状态正确恢复
- ✅ 保持首页重置功能正常

## 修复内容
1. **下钻搜索参数**: 在 [`loadDrillDownTeamList`](app/wjhx/pages/ucenter/team/team.js:223) 请求中添加了 `keyword` 参数
2. **搜索方法优化**: 修改了 [`searchTeam`](app/wjhx/pages/ucenter/team/team.js:352) 方法，根据下钻状态调用不同的加载方法
3. **状态保持**: 移除了 [`viewPromotionDetail`](app/wjhx/pages/ucenter/team/team.js:394) 中不必要的搜索关键词重置
4. **状态恢复**: [`drillBack`](app/wjhx/pages/ucenter/team/team.js:458) 方法正确恢复搜索状态

### 搜索方法逻辑
- **普通状态**: 调用 [`loadTeamList(true)`](app/wjhx/pages/ucenter/team/team.js:354)
- **下钻状态**: 调用 [`loadDrillDownTeamList(currentDrillUserId)`](app/wjhx/pages/ucenter/team/team.js:353)

## 测试验证
所有功能测试通过：
- ✅ 导航栏显示正常
- ✅ 下钻功能与导航栏集成正常
- ✅ 返回逻辑正确
- ✅ 页面滚动效果正常
- ✅ 搜索功能正常工作（包括下钻状态下的搜索）
- ✅ 搜索状态保持正常

集成完成，所有功能正常工作！