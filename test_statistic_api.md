# 统计API测试指南

## 测试API接口

使用以下方法测试统计API是否正常工作：

### 1. 浏览器测试

在浏览器中直接访问以下URL（需要先登录管理后台）：

```
# 基础统计数据
http://localhost:8080/adminapi/statistic/order/get_basic?data=2025/01/01-2025/01/07

# 趋势数据
http://localhost:8080/adminapi/statistic/order/get_trend?data=2025/01/01-2025/01/07

# 来源分析
http://localhost:8080/adminapi/statistic/order/get_channel?data=2025/01/01-2025/01/07

# 类型分析
http://localhost:8080/adminapi/statistic/order/get_type?data=2025/01/01-2025/01/07
```

### 2. 使用Postman或curl测试

```bash
# 获取基础统计数据
curl -X GET "http://localhost:8080/adminapi/statistic/order/get_basic?data=2025/01/01-2025/01/07" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 获取趋势数据
curl -X GET "http://localhost:8080/adminapi/statistic/order/get_trend?data=2025/01/01-2025/01/07" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 3. 前端调试

在浏览器开发者工具中执行：

```javascript
// 检查API调用
console.log('Testing API calls...');

// 测试基础数据API
fetch('/adminapi/statistic/order/get_basic?data=2025/01/01-2025/01/07', {
  headers: {
    'Authorization': 'Bearer ' + localStorage.getItem('token')
  }
})
.then(response => response.json())
.then(data => console.log('Basic data:', data))
.catch(error => console.error('Basic data error:', error));

// 测试趋势数据API
fetch('/adminapi/statistic/order/get_trend?data=2025/01/01-2025/01/07', {
  headers: {
    'Authorization': 'Bearer ' + localStorage.getItem('token')
  }
})
.then(response => response.json())
.then(data => console.log('Trend data:', data))
.catch(error => console.error('Trend data error:', error));
```

## 预期返回数据格式

### 基础统计数据 (get_basic)
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "pay_count": 150,
    "pay_price": 25000,
    "refund_count": 10,
    "refund_price": 2000
  }
}
```

### 趋势数据 (get_trend)
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "xAxis": ["01/01", "01/02", "01/03", "01/04", "01/05", "01/06", "01/07"],
    "series": [
      {
        "name": "订单数量",
        "type": "line",
        "data": [10, 15, 20, 18, 25, 30, 28]
      },
      {
        "name": "订单金额",
        "type": "line",
        "data": [1000, 1500, 2000, 1800, 2500, 3000, 2800]
      }
    ]
  }
}
```

### 来源分析数据 (get_channel)
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "name": "微信小程序",
        "value": 150,
        "amount": 15000,
        "percent": "45.5",
        "conversionRate": 15
      }
    ]
  }
}
```

### 类型分析数据 (get_type)
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "name": "普通订单",
        "count": 120,
        "value": 12000,
        "percent": "60.0",
        "avgOrderValue": 100
      }
    ]
  }
}
```

## 故障排除

### 1. 如果API返回404错误
- 检查后端服务是否启动
- 检查控制器路径是否正确
- 检查Spring Boot的组件扫描配置

### 2. 如果API返回401错误
- 检查用户是否已登录
- 检查Token是否有效
- 检查API权限配置

### 3. 如果API返回500错误
- 检查后端日志
- 检查数据库连接
- 检查Service层的实现

### 4. 如果前端无数据显示
- 检查浏览器Network标签页的API请求
- 检查Console是否有JavaScript错误
- 检查Vue组件的数据绑定

## 调试技巧

1. **启用详细日志**：在application.yml中设置日志级别为DEBUG
2. **使用断点调试**：在IDE中设置断点调试Service方法
3. **检查数据库**：直接查询数据库验证数据是否存在
4. **模拟数据**：先使用模拟数据验证前端显示是否正常

修复完成后，统计页面应该能正常显示数据。如果仍有问题，请按照上述步骤逐一排查。